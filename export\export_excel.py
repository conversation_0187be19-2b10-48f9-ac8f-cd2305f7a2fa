#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel Export Module
وحدة تصدير جدول القطع إلى Excel
"""

import os
from datetime import datetime
from typing import List, Dict, Any
try:
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.utils import get_column_letter
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False


class ExcelExporter:
    """فئة تصدير البيانات إلى Excel"""
    
    def __init__(self):
        self.workbook = None
        self.worksheet = None
        
    def export_cutlist(self, components_data: List[Dict[str, Any]], 
                      file_path: str, project_info: Dict[str, Any] = None) -> bool:
        """
        تصدير جدول القطع إلى ملف Excel
        
        Args:
            components_data: بيانات المكونات
            file_path: مسار الملف للحفظ
            project_info: معلومات المشروع
            
        Returns:
            True إذا تم التصدير بنجاح، False في حالة الخطأ
        """
        if not OPENPYXL_AVAILABLE:
            raise ImportError("مكتبة openpyxl غير مثبتة. يرجى تثبيتها باستخدام: pip install openpyxl")
        
        try:
            # إنشاء مصنف جديد
            self.workbook = openpyxl.Workbook()
            self.worksheet = self.workbook.active
            self.worksheet.title = "جدول القطع"
            
            # إعداد معلومات المشروع
            self._setup_project_info(project_info or {})
            
            # إعداد رؤوس الجدول
            self._setup_headers()
            
            # إضافة البيانات
            self._add_data(components_data)
            
            # تنسيق الجدول
            self._format_table()
            
            # إضافة الإحصائيات
            self._add_statistics(components_data)
            
            # حفظ الملف
            self.workbook.save(file_path)
            return True
            
        except Exception as e:
            print(f"خطأ في تصدير Excel: {str(e)}")
            return False
    
    def _setup_project_info(self, project_info: Dict[str, Any]):
        """إعداد معلومات المشروع في أعلى الملف"""
        # عنوان المشروع
        self.worksheet['A1'] = project_info.get('title', 'جدول القطع - Cut List')
        self.worksheet['A1'].font = Font(size=16, bold=True, color='2F5597')
        
        # تاريخ الإنشاء
        self.worksheet['A2'] = f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        self.worksheet['A2'].font = Font(size=10, italic=True)
        
        # اسم المشروع
        if 'project_name' in project_info:
            self.worksheet['A3'] = f"اسم المشروع: {project_info['project_name']}"
            self.worksheet['A3'].font = Font(size=12, bold=True)
        
        # وصف المشروع
        if 'description' in project_info:
            self.worksheet['A4'] = f"الوصف: {project_info['description']}"
            self.worksheet['A4'].font = Font(size=10)
    
    def _setup_headers(self):
        """إعداد رؤوس الجدول"""
        # تحديد الصف الذي سيبدأ منه الجدول
        start_row = 6
        
        headers = [
            'رقم المكون',
            'اسم المكون', 
            'الطول (مم)',
            'العرض (مم)',
            'السماكة (مم)',
            'نوع المادة',
            'الكمية',
            'الحجم الإجمالي (سم³)',
            'الوزن المقدر (كغ)',
            'التكلفة المقدرة (ريال)',
            'ملاحظات'
        ]
        
        # إضافة الرؤوس
        for col, header in enumerate(headers, 1):
            cell = self.worksheet.cell(row=start_row, column=col, value=header)
            cell.font = Font(bold=True, color='FFFFFF')
            cell.fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        
        self.data_start_row = start_row + 1
    
    def _add_data(self, components_data: List[Dict[str, Any]]):
        """إضافة بيانات المكونات"""
        for row_idx, component in enumerate(components_data, self.data_start_row):
            # رقم المكون
            self.worksheet.cell(row=row_idx, column=1, value=row_idx - self.data_start_row + 1)
            
            # اسم المكون
            self.worksheet.cell(row=row_idx, column=2, value=component.get('name', ''))
            
            # الأبعاد
            dimensions = component.get('dimensions', {})
            self.worksheet.cell(row=row_idx, column=3, value=dimensions.get('length', 0))
            self.worksheet.cell(row=row_idx, column=4, value=dimensions.get('width', 0))
            self.worksheet.cell(row=row_idx, column=5, value=dimensions.get('thickness', 0))
            
            # نوع المادة
            self.worksheet.cell(row=row_idx, column=6, value=component.get('material', 'خشب'))
            
            # الكمية
            self.worksheet.cell(row=row_idx, column=7, value=component.get('quantity', 1))
            
            # الحجم الإجمالي
            volume = component.get('volume', 0) / 1000  # تحويل من مم³ إلى سم³
            self.worksheet.cell(row=row_idx, column=8, value=round(volume, 2))
            
            # الوزن المقدر
            weight = component.get('weight', 0)
            self.worksheet.cell(row=row_idx, column=9, value=round(weight, 2))
            
            # التكلفة المقدرة
            cost = component.get('cost', 0)
            self.worksheet.cell(row=row_idx, column=10, value=round(cost, 2))
            
            # ملاحظات
            notes = component.get('notes', '')
            self.worksheet.cell(row=row_idx, column=11, value=notes)
    
    def _format_table(self):
        """تنسيق الجدول"""
        # تحديد نطاق البيانات
        max_row = self.worksheet.max_row
        max_col = self.worksheet.max_column
        
        # تطبيق الحدود على جميع الخلايا
        for row in range(self.data_start_row - 1, max_row + 1):
            for col in range(1, max_col + 1):
                cell = self.worksheet.cell(row=row, column=col)
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                
                # تنسيق الأرقام
                if col in [3, 4, 5, 8, 9, 10]:  # أعمدة الأرقام
                    cell.number_format = '#,##0.00'
                    cell.alignment = Alignment(horizontal='center')
                elif col in [1, 7]:  # أعمدة الأرقام الصحيحة
                    cell.alignment = Alignment(horizontal='center')
                else:  # أعمدة النص
                    cell.alignment = Alignment(horizontal='right')
        
        # تعديل عرض الأعمدة
        column_widths = [8, 25, 12, 12, 12, 15, 8, 18, 15, 18, 20]
        for col, width in enumerate(column_widths, 1):
            self.worksheet.column_dimensions[get_column_letter(col)].width = width
    
    def _add_statistics(self, components_data: List[Dict[str, Any]]):
        """إضافة إحصائيات في نهاية الجدول"""
        stats_start_row = self.worksheet.max_row + 3
        
        # عنوان الإحصائيات
        self.worksheet.cell(row=stats_start_row, column=1, value="الإحصائيات:")
        self.worksheet.cell(row=stats_start_row, column=1).font = Font(size=14, bold=True, color='2F5597')
        
        # حساب الإحصائيات
        total_components = len(components_data)
        total_quantity = sum(comp.get('quantity', 1) for comp in components_data)
        total_volume = sum(comp.get('volume', 0) for comp in components_data) / 1000  # سم³
        total_weight = sum(comp.get('weight', 0) for comp in components_data)
        total_cost = sum(comp.get('cost', 0) for comp in components_data)
        
        # إضافة الإحصائيات
        stats = [
            f"إجمالي أنواع المكونات: {total_components}",
            f"إجمالي عدد القطع: {total_quantity}",
            f"إجمالي الحجم: {total_volume:.2f} سم³",
            f"إجمالي الوزن المقدر: {total_weight:.2f} كغ",
            f"إجمالي التكلفة المقدرة: {total_cost:.2f} ريال"
        ]
        
        for i, stat in enumerate(stats):
            row = stats_start_row + i + 1
            self.worksheet.cell(row=row, column=1, value=stat)
            self.worksheet.cell(row=row, column=1).font = Font(size=11)
        
        # إضافة معلومات المواد المستخدمة
        materials = set(comp.get('material', 'خشب') for comp in components_data)
        materials_text = f"المواد المستخدمة: {', '.join(materials)}"
        
        materials_row = stats_start_row + len(stats) + 2
        self.worksheet.cell(row=materials_row, column=1, value=materials_text)
        self.worksheet.cell(row=materials_row, column=1).font = Font(size=11, italic=True)


def export_to_excel(components_data: List[Dict[str, Any]], 
                   file_path: str, 
                   project_info: Dict[str, Any] = None) -> bool:
    """
    دالة مساعدة لتصدير البيانات إلى Excel
    
    Args:
        components_data: بيانات المكونات
        file_path: مسار الملف
        project_info: معلومات المشروع
        
    Returns:
        True إذا تم التصدير بنجاح
    """
    exporter = ExcelExporter()
    return exporter.export_cutlist(components_data, file_path, project_info)


def test_excel_export():
    """اختبار تصدير Excel"""
    # بيانات تجريبية
    test_data = [
        {
            'name': 'لوح علوي',
            'dimensions': {'length': 1200, 'width': 600, 'thickness': 18},
            'material': 'خشب',
            'quantity': 1,
            'volume': 12960000,  # مم³
            'weight': 7.78,  # كغ
            'cost': 65.0,  # ريال
            'notes': 'لوح رئيسي للطاولة'
        },
        {
            'name': 'لوح جانبي',
            'dimensions': {'length': 800, 'width': 400, 'thickness': 18},
            'material': 'خشب',
            'quantity': 2,
            'volume': 5760000,  # مم³
            'weight': 3.46,  # كغ
            'cost': 28.8,  # ريال
            'notes': 'الألواح الجانبية'
        }
    ]
    
    project_info = {
        'title': 'جدول قطع طاولة المكتب',
        'project_name': 'طاولة مكتب خشبية',
        'description': 'طاولة مكتب بسيطة من الخشب الطبيعي'
    }
    
    success = export_to_excel(test_data, 'test_cutlist.xlsx', project_info)
    if success:
        print("تم تصدير الملف بنجاح: test_cutlist.xlsx")
    else:
        print("فشل في تصدير الملف")


if __name__ == "__main__":
    test_excel_export()
