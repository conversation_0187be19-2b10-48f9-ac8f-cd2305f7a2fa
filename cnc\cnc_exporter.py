#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CNC Exporter
مصدر ملفات ماكينات CNC
"""

import os
import math
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class CNCOperation:
    """عملية CNC"""
    operation_type: str  # cut, drill, pocket, engrave
    start_point: tuple  # (x, y, z)
    end_point: tuple = None
    depth: float = 0.0
    feed_rate: float = 1000.0  # mm/min
    spindle_speed: float = 18000.0  # RPM
    tool_diameter: float = 6.0  # mm
    parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}


@dataclass
class CNCProgram:
    """برنامج CNC"""
    name: str
    material: str
    thickness: float
    operations: List[CNCOperation]
    setup_notes: str = ""
    safety_notes: str = ""
    estimated_time: float = 0.0  # minutes


class CNCExporter:
    """مصدر ملفات CNC"""
    
    def __init__(self):
        self.machine_configs = {
            'generic': {
                'max_feed_rate': 3000,
                'max_spindle_speed': 24000,
                'tool_change_position': (0, 0, 50),
                'safe_height': 10.0
            },
            'shopbot': {
                'max_feed_rate': 2500,
                'max_spindle_speed': 18000,
                'tool_change_position': (0, 0, 25),
                'safe_height': 5.0
            }
        }
    
    def export_component_to_gcode(self, component: Dict[str, Any], 
                                 output_dir: str = "cnc_output") -> str:
        """تصدير مكون إلى G-code"""
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # إنشاء برنامج CNC
        program = self._create_cnc_program(component)
        
        # توليد G-code
        gcode = self._generate_gcode(program)
        
        # حفظ الملف
        filename = f"{component.get('name', 'component').replace(' ', '_')}.nc"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(gcode)
        
        return filepath
    
    def _create_cnc_program(self, component: Dict[str, Any]) -> CNCProgram:
        """إنشاء برنامج CNC للمكون"""
        dimensions = component.get('dimensions', {})
        length = dimensions.get('length', 0)
        width = dimensions.get('width', 0)
        thickness = dimensions.get('thickness', 0)
        
        operations = []
        
        # عملية القطع الخارجي
        cut_operations = self._create_outline_cut(length, width, thickness)
        operations.extend(cut_operations)
        
        # عمليات الحفر (إذا وجدت)
        drilling = component.get('drilling', [])
        for drill_point in drilling:
            drill_op = self._create_drill_operation(drill_point, thickness)
            operations.append(drill_op)
        
        # تقدير الوقت
        estimated_time = self._estimate_machining_time(operations)
        
        program = CNCProgram(
            name=component.get('name', 'مكون'),
            material=component.get('material', 'خشب'),
            thickness=thickness,
            operations=operations,
            setup_notes=self._generate_setup_notes(component),
            safety_notes=self._generate_safety_notes(component),
            estimated_time=estimated_time
        )
        
        return program
    
    def _create_outline_cut(self, length: float, width: float, thickness: float) -> List[CNCOperation]:
        """إنشاء عمليات القطع الخارجي"""
        operations = []
        
        # نقاط القطع (مستطيل)
        points = [
            (0, 0),
            (length, 0),
            (length, width),
            (0, width),
            (0, 0)  # العودة للنقطة الأولى
        ]
        
        # تحديد عمق القطع (عدة تمريرات)
        cut_depth_per_pass = min(thickness / 3, 6.0)  # حد أقصى 6 مم لكل تمريرة
        num_passes = math.ceil(thickness / cut_depth_per_pass)
        
        for pass_num in range(num_passes):
            current_depth = min((pass_num + 1) * cut_depth_per_pass, thickness)
            
            for i in range(len(points) - 1):
                start_point = (points[i][0], points[i][1], -current_depth)
                end_point = (points[i + 1][0], points[i + 1][1], -current_depth)
                
                operation = CNCOperation(
                    operation_type="cut",
                    start_point=start_point,
                    end_point=end_point,
                    depth=current_depth,
                    feed_rate=1200.0,
                    spindle_speed=18000.0,
                    tool_diameter=6.0
                )
                operations.append(operation)
        
        return operations
    
    def _create_drill_operation(self, drill_point: Dict[str, Any], thickness: float) -> CNCOperation:
        """إنشاء عملية حفر"""
        x = drill_point.get('x', 0)
        y = drill_point.get('y', 0)
        diameter = drill_point.get('diameter', 5.0)
        depth = drill_point.get('depth', thickness)
        
        return CNCOperation(
            operation_type="drill",
            start_point=(x, y, 0),
            end_point=(x, y, -depth),
            depth=depth,
            feed_rate=300.0,  # سرعة أبطأ للحفر
            spindle_speed=12000.0,
            tool_diameter=diameter
        )
    
    def _generate_gcode(self, program: CNCProgram) -> str:
        """توليد كود G"""
        gcode_lines = []
        
        # رأس الملف
        gcode_lines.extend([
            f"( Program: {program.name} )",
            f"( Material: {program.material} )",
            f"( Thickness: {program.thickness}mm )",
            f"( Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} )",
            f"( Estimated time: {program.estimated_time:.1f} minutes )",
            "",
            "( Setup Notes: )",
            f"( {program.setup_notes} )",
            "",
            "( Safety Notes: )",
            f"( {program.safety_notes} )",
            "",
            "G21 ( Metric units )",
            "G90 ( Absolute positioning )",
            "G17 ( XY plane )",
            "G94 ( Feed rate per minute )",
            "",
            "M03 S18000 ( Start spindle )",
            "G00 Z10.0 ( Move to safe height )",
            ""
        ])
        
        # العمليات
        current_tool = None
        
        for operation in program.operations:
            # تغيير الأداة إذا لزم الأمر
            if current_tool != operation.tool_diameter:
                gcode_lines.extend([
                    f"( Tool change: {operation.tool_diameter}mm )",
                    "M05 ( Stop spindle )",
                    "G00 Z25.0 ( Tool change height )",
                    "M00 ( Pause for tool change )",
                    f"M03 S{operation.spindle_speed:.0f} ( Start spindle )",
                    "G04 P2.0 ( Wait 2 seconds )",
                    ""
                ])
                current_tool = operation.tool_diameter
            
            # تنفيذ العملية
            if operation.operation_type == "cut":
                gcode_lines.extend(self._generate_cut_gcode(operation))
            elif operation.operation_type == "drill":
                gcode_lines.extend(self._generate_drill_gcode(operation))
            
            gcode_lines.append("")
        
        # نهاية البرنامج
        gcode_lines.extend([
            "G00 Z10.0 ( Retract to safe height )",
            "M05 ( Stop spindle )",
            "G00 X0 Y0 ( Return to origin )",
            "M30 ( End program )"
        ])
        
        return "\n".join(gcode_lines)
    
    def _generate_cut_gcode(self, operation: CNCOperation) -> List[str]:
        """توليد G-code للقطع"""
        lines = []
        
        start_x, start_y, start_z = operation.start_point
        end_x, end_y, end_z = operation.end_point
        
        lines.extend([
            f"G00 X{start_x:.3f} Y{start_y:.3f} ( Move to start position )",
            f"G01 Z{start_z:.3f} F{operation.feed_rate/3:.0f} ( Plunge )",
            f"G01 X{end_x:.3f} Y{end_y:.3f} F{operation.feed_rate:.0f} ( Cut )"
        ])
        
        return lines
    
    def _generate_drill_gcode(self, operation: CNCOperation) -> List[str]:
        """توليد G-code للحفر"""
        lines = []
        
        x, y, _ = operation.start_point
        _, _, end_z = operation.end_point
        
        lines.extend([
            f"G00 X{x:.3f} Y{y:.3f} ( Move to drill position )",
            f"G01 Z{end_z:.3f} F{operation.feed_rate:.0f} ( Drill )",
            "G00 Z2.0 ( Retract )"
        ])
        
        return lines
    
    def _estimate_machining_time(self, operations: List[CNCOperation]) -> float:
        """تقدير وقت التشغيل بالدقائق"""
        total_time = 0.0
        
        for operation in operations:
            if operation.operation_type == "cut" and operation.end_point:
                # حساب المسافة
                start_x, start_y, start_z = operation.start_point
                end_x, end_y, end_z = operation.end_point
                
                distance = math.sqrt((end_x - start_x)**2 + (end_y - start_y)**2)
                time = distance / operation.feed_rate  # بالدقائق
                total_time += time
                
            elif operation.operation_type == "drill":
                # وقت ثابت للحفر
                total_time += 0.5  # 30 ثانية لكل حفرة
        
        # إضافة وقت الإعداد والتغييرات
        total_time += 5.0  # 5 دقائق إعداد
        
        return total_time
    
    def _generate_setup_notes(self, component: Dict[str, Any]) -> str:
        """توليد ملاحظات الإعداد"""
        dimensions = component.get('dimensions', {})
        material = component.get('material', 'خشب')
        
        notes = f"تثبيت قطعة {material} بأبعاد "
        notes += f"{dimensions.get('length', 0):.0f} × {dimensions.get('width', 0):.0f} × {dimensions.get('thickness', 0):.0f} مم. "
        notes += "تأكد من تثبيت القطعة بإحكام ومحاذاة الزوايا."
        
        return notes
    
    def _generate_safety_notes(self, component: Dict[str, Any]) -> str:
        """توليد ملاحظات السلامة"""
        material = component.get('material', 'خشب')
        
        notes = "ارتداء معدات الحماية الشخصية. "
        
        if material == "خشب":
            notes += "انتباه لاتجاه الألياف. استخدام شفط الغبار."
        elif material == "MDF":
            notes += "شفط قوي للغبار مطلوب. تجنب استنشاق الغبار."
        elif material == "ألمنيوم":
            notes += "استخدام سائل التبريد. سرعة دوران منخفضة."
        
        return notes
    
    def export_cutting_layout_to_gcode(self, layout: Dict[str, Any], 
                                     output_dir: str = "cnc_output") -> str:
        """تصدير تخطيط القطع إلى G-code"""
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # إنشاء برنامج لتخطيط كامل
        sheet = layout.get('sheet', {})
        pieces = layout.get('pieces', [])
        
        operations = []
        
        # إضافة عمليات قطع لكل قطعة
        for piece_info in pieces:
            piece = piece_info.get('piece', {})
            x = piece_info.get('x', 0)
            y = piece_info.get('y', 0)
            length = piece_info.get('length', 0)
            width = piece_info.get('width', 0)
            
            # قطع مستطيل في الموضع المحدد
            cut_ops = self._create_positioned_cut(x, y, length, width, sheet.get('thickness', 18))
            operations.extend(cut_ops)
        
        program = CNCProgram(
            name=f"تخطيط_قطع_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            material=sheet.get('material', 'خشب'),
            thickness=sheet.get('thickness', 18),
            operations=operations,
            setup_notes=f"تخطيط قطع على لوح {sheet.get('length', 0)} × {sheet.get('width', 0)} مم",
            estimated_time=self._estimate_machining_time(operations)
        )
        
        gcode = self._generate_gcode(program)
        
        filename = f"cutting_layout_{datetime.now().strftime('%Y%m%d_%H%M%S')}.nc"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(gcode)
        
        return filepath
    
    def _create_positioned_cut(self, x: float, y: float, length: float, 
                             width: float, thickness: float) -> List[CNCOperation]:
        """إنشاء عمليات قطع في موضع محدد"""
        operations = []
        
        # نقاط القطع المحلية
        local_points = [
            (0, 0),
            (length, 0),
            (length, width),
            (0, width),
            (0, 0)
        ]
        
        # تحويل إلى الإحداثيات العامة
        global_points = [(x + px, y + py) for px, py in local_points]
        
        # إنشاء عمليات القطع
        for i in range(len(global_points) - 1):
            start_point = (global_points[i][0], global_points[i][1], -thickness)
            end_point = (global_points[i + 1][0], global_points[i + 1][1], -thickness)
            
            operation = CNCOperation(
                operation_type="cut",
                start_point=start_point,
                end_point=end_point,
                depth=thickness,
                feed_rate=1200.0,
                spindle_speed=18000.0,
                tool_diameter=6.0
            )
            operations.append(operation)
        
        return operations


def test_cnc_exporter():
    """اختبار مصدر CNC"""
    exporter = CNCExporter()
    
    # مكون تجريبي
    component = {
        'name': 'لوح علوي',
        'dimensions': {'length': 1200, 'width': 600, 'thickness': 18},
        'material': 'خشب',
        'drilling': [
            {'x': 100, 'y': 100, 'diameter': 5, 'depth': 18},
            {'x': 1100, 'y': 100, 'diameter': 5, 'depth': 18}
        ]
    }
    
    # تصدير إلى G-code
    filepath = exporter.export_component_to_gcode(component, "test_cnc")
    print(f"تم تصدير ملف CNC: {filepath}")
    
    # قراءة وعرض جزء من الملف
    if os.path.exists(filepath):
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            print("\nأول 20 سطر من ملف G-code:")
            for i, line in enumerate(lines[:20]):
                print(f"{i+1:2d}: {line.rstrip()}")
    
    # تنظيف
    import shutil
    if os.path.exists("test_cnc"):
        shutil.rmtree("test_cnc")


if __name__ == "__main__":
    test_cnc_exporter()
