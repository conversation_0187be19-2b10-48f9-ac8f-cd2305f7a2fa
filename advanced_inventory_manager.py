#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Inventory Management System
نظام إدارة المخزون المتقدم
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *


@dataclass
class InventoryItem:
    """عنصر مخزون"""
    id: str
    name: str
    category: str
    material_type: str
    dimensions: str  # الأبعاد (طول × عرض × سماكة)
    unit: str  # الوحدة (قطعة، متر، متر مربع، متر مكعب)
    current_stock: float
    minimum_stock: float
    maximum_stock: float
    cost_price: float
    selling_price: float
    supplier: str
    supplier_code: str
    location: str  # موقع التخزين
    notes: str
    created_date: str
    last_updated: str
    last_purchase_date: str
    last_sale_date: str
    total_purchased: float = 0.0
    total_sold: float = 0.0

    @property
    def stock_status(self) -> str:
        """حالة المخزون"""
        if self.current_stock <= self.minimum_stock:
            return "منخفض"
        elif self.current_stock >= self.maximum_stock:
            return "مرتفع"
        else:
            return "طبيعي"

    @property
    def stock_value(self) -> float:
        """قيمة المخزون"""
        return self.current_stock * self.cost_price


@dataclass
class StockMovement:
    """حركة مخزون"""
    id: str
    item_id: str
    movement_type: str  # شراء، بيع، تعديل، تلف، إرجاع
    quantity: float
    unit_price: float
    total_amount: float
    reference_number: str  # رقم المرجع (فاتورة، طلب، إلخ)
    notes: str
    movement_date: str
    created_by: str


class InventoryDatabaseManager:
    """مدير قاعدة بيانات المخزون"""

    def __init__(self, db_path: str = "inventory.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """تهيئة قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # جدول عناصر المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_items (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                category TEXT,
                material_type TEXT,
                dimensions TEXT,
                unit TEXT,
                current_stock REAL DEFAULT 0.0,
                minimum_stock REAL DEFAULT 0.0,
                maximum_stock REAL DEFAULT 0.0,
                cost_price REAL DEFAULT 0.0,
                selling_price REAL DEFAULT 0.0,
                supplier TEXT,
                supplier_code TEXT,
                location TEXT,
                notes TEXT,
                created_date TEXT,
                last_updated TEXT,
                last_purchase_date TEXT,
                last_sale_date TEXT,
                total_purchased REAL DEFAULT 0.0,
                total_sold REAL DEFAULT 0.0
            )
        ''')

        # جدول حركات المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_movements (
                id TEXT PRIMARY KEY,
                item_id TEXT,
                movement_type TEXT,
                quantity REAL,
                unit_price REAL,
                total_amount REAL,
                reference_number TEXT,
                notes TEXT,
                movement_date TEXT,
                created_by TEXT,
                FOREIGN KEY (item_id) REFERENCES inventory_items (id)
            )
        ''')

        # جدول الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                payment_terms INTEGER DEFAULT 30,
                notes TEXT,
                created_date TEXT
            )
        ''')

        # جدول فئات المواد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS material_categories (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                created_date TEXT
            )
        ''')

        conn.commit()
        conn.close()

        # إضافة بيانات افتراضية
        self.add_default_data()

    def add_default_data(self):
        """إضافة بيانات افتراضية"""
        # فئات المواد الافتراضية
        default_categories = [
            ("CAT001", "ألواح خشبية", "ألواح خشب طبيعي ومصنع"),
            ("CAT002", "مواد تشطيب", "حواف، دهانات، ورنيش"),
            ("CAT003", "مفصلات وأقفال", "مفصلات، أقفال، مقابض"),
            ("CAT004", "براغي ومسامير", "براغي، مسامير، صواميل"),
            ("CAT005", "أدوات ومعدات", "أدوات قطع، قياس، تشكيل"),
        ]

        # موردين افتراضيين
        default_suppliers = [
            ("SUP001", "شركة الخشب الذهبي", "أحمد محمد", "0501234567", "<EMAIL>", "الرياض - حي الصناعية", 30),
            ("SUP002", "مؤسسة النجارة الحديثة", "سالم أحمد", "0509876543", "<EMAIL>", "جدة - شارع الملك عبدالعزيز", 45),
            ("SUP003", "متجر الأدوات المتخصص", "محمد علي", "0551122334", "<EMAIL>", "الدمام - الكورنيش", 15),
        ]

        # عناصر مخزون افتراضية
        default_items = [
            ("ITEM001", "لوح خشب طبيعي", "ألواح خشبية", "خشب طبيعي", "2440×1220×18", "قطعة", 50, 10, 100, 120.0, 180.0, "شركة الخشب الذهبي", "GW-001", "مستودع A - رف 1"),
            ("ITEM002", "لوح MDF", "ألواح خشبية", "MDF", "2440×1220×15", "قطعة", 75, 15, 150, 85.0, 130.0, "مؤسسة النجارة الحديثة", "MN-002", "مستودع A - رف 2"),
            ("ITEM003", "حافة PVC بيضاء", "مواد تشطيب", "PVC", "22×0.4", "متر", 500, 50, 1000, 2.5, 4.0, "شركة الخشب الذهبي", "GW-003", "مستودع B - رف 1"),
            ("ITEM004", "مفصلة خفية", "مفصلات وأقفال", "معدن", "35مم", "قطعة", 200, 20, 500, 8.0, 15.0, "متجر الأدوات المتخصص", "TS-004", "مستودع C - درج 1"),
            ("ITEM005", "براغي خشب", "براغي ومسامير", "معدن", "4×50مم", "علبة", 100, 10, 200, 12.0, 20.0, "متجر الأدوات المتخصص", "TS-005", "مستودع C - درج 2"),
        ]

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إضافة الفئات
            for category in default_categories:
                cursor.execute('''
                    INSERT OR IGNORE INTO material_categories
                    (id, name, description, created_date)
                    VALUES (?, ?, ?, ?)
                ''', (*category, datetime.now().isoformat()))

            # إضافة الموردين
            for supplier in default_suppliers:
                cursor.execute('''
                    INSERT OR IGNORE INTO suppliers
                    (id, name, contact_person, phone, email, address, payment_terms, created_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (*supplier, datetime.now().isoformat()))

            # إضافة عناصر المخزون
            for item in default_items:
                cursor.execute('''
                    INSERT OR IGNORE INTO inventory_items
                    (id, name, category, material_type, dimensions, unit, current_stock,
                     minimum_stock, maximum_stock, cost_price, selling_price, supplier,
                     supplier_code, location, created_date, last_updated)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (*item, datetime.now().isoformat(), datetime.now().isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في إضافة البيانات الافتراضية: {e}")

    def get_all_items(self) -> List[Dict[str, Any]]:
        """الحصول على جميع عناصر المخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM inventory_items ORDER BY name')
            items_data = cursor.fetchall()

            items = []
            columns = [desc[0] for desc in cursor.description]

            for item_data in items_data:
                item_dict = dict(zip(columns, item_data))
                # حساب حالة المخزون
                current_stock = item_dict.get('current_stock', 0)
                minimum_stock = item_dict.get('minimum_stock', 0)
                maximum_stock = item_dict.get('maximum_stock', 0)

                if current_stock <= minimum_stock:
                    item_dict['stock_status'] = "منخفض"
                elif current_stock >= maximum_stock:
                    item_dict['stock_status'] = "مرتفع"
                else:
                    item_dict['stock_status'] = "طبيعي"

                # حساب قيمة المخزون
                cost_price = item_dict.get('cost_price', 0)
                item_dict['stock_value'] = current_stock * cost_price

                items.append(item_dict)

            conn.close()
            return items

        except Exception as e:
            print(f"خطأ في تحميل المخزون: {e}")
            return []

    def search_items(self, search_term: str) -> List[Dict[str, Any]]:
        """البحث في المخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            search_pattern = f"%{search_term}%"
            cursor.execute('''
                SELECT * FROM inventory_items
                WHERE name LIKE ? OR category LIKE ? OR material_type LIKE ? OR supplier LIKE ?
                ORDER BY name
            ''', (search_pattern, search_pattern, search_pattern, search_pattern))

            items_data = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            items = []
            for item_data in items_data:
                item_dict = dict(zip(columns, item_data))
                items.append(item_dict)

            conn.close()
            return items

        except Exception as e:
            print(f"خطأ في البحث: {e}")
            return []

    def get_low_stock_items(self) -> List[Dict[str, Any]]:
        """الحصول على العناصر منخفضة المخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM inventory_items
                WHERE current_stock <= minimum_stock
                ORDER BY (current_stock - minimum_stock)
            ''')

            items_data = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            items = []
            for item_data in items_data:
                item_dict = dict(zip(columns, item_data))
                items.append(item_dict)

            conn.close()
            return items

        except Exception as e:
            print(f"خطأ في تحميل العناصر منخفضة المخزون: {e}")
            return []

    def add_stock_movement(self, movement: StockMovement) -> bool:
        """إضافة حركة مخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إضافة الحركة
            cursor.execute('''
                INSERT INTO stock_movements
                (id, item_id, movement_type, quantity, unit_price, total_amount,
                 reference_number, notes, movement_date, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                movement.id, movement.item_id, movement.movement_type, movement.quantity,
                movement.unit_price, movement.total_amount, movement.reference_number,
                movement.notes, movement.movement_date, movement.created_by
            ))

            # تحديث المخزون
            if movement.movement_type in ['شراء', 'إرجاع']:
                # زيادة المخزون
                cursor.execute('''
                    UPDATE inventory_items
                    SET current_stock = current_stock + ?,
                        total_purchased = total_purchased + ?,
                        last_purchase_date = ?,
                        last_updated = ?
                    WHERE id = ?
                ''', (movement.quantity, movement.quantity, movement.movement_date,
                      datetime.now().isoformat(), movement.item_id))

            elif movement.movement_type in ['بيع', 'تلف']:
                # تقليل المخزون
                cursor.execute('''
                    UPDATE inventory_items
                    SET current_stock = current_stock - ?,
                        total_sold = total_sold + ?,
                        last_sale_date = ?,
                        last_updated = ?
                    WHERE id = ?
                ''', (movement.quantity, movement.quantity, movement.movement_date,
                      datetime.now().isoformat(), movement.item_id))

            elif movement.movement_type == 'تعديل':
                # تعديل المخزون مباشرة
                cursor.execute('''
                    UPDATE inventory_items
                    SET current_stock = ?,
                        last_updated = ?
                    WHERE id = ?
                ''', (movement.quantity, datetime.now().isoformat(), movement.item_id))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إضافة حركة المخزون: {e}")
            return False

    def get_item_movements(self, item_id: str) -> List[Dict[str, Any]]:
        """الحصول على حركات عنصر معين"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM stock_movements
                WHERE item_id = ?
                ORDER BY movement_date DESC
            ''', (item_id,))

            movements_data = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            movements = []
            for movement_data in movements_data:
                movement_dict = dict(zip(columns, movement_data))
                movements.append(movement_dict)

            conn.close()
            return movements

        except Exception as e:
            print(f"خطأ في تحميل حركات المخزون: {e}")
            return []


class InventoryManagementDialog(QDialog):
    """حوار إدارة المخزون"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = InventoryDatabaseManager()
        self.init_ui()
        self.load_inventory()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة المخزون المتقدمة")
        self.setModal(True)
        self.resize(1400, 900)

        layout = QVBoxLayout(self)

        # شريط الأدوات العلوي
        toolbar_layout = QHBoxLayout()

        # البحث
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم، الفئة، المادة، أو المورد...")
        self.search_edit.textChanged.connect(self.search_inventory)

        # فلاتر
        filter_label = QLabel("الفلتر:")
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["جميع العناصر", "مخزون منخفض", "مخزون مرتفع", "نفد المخزون"])
        self.filter_combo.currentTextChanged.connect(self.apply_filter)

        # أزرار الإدارة
        add_item_btn = QPushButton("➕ إضافة عنصر")
        add_item_btn.clicked.connect(self.add_new_item)

        edit_item_btn = QPushButton("✏️ تعديل")
        edit_item_btn.clicked.connect(self.edit_selected_item)

        stock_movement_btn = QPushButton("📦 حركة مخزون")
        stock_movement_btn.clicked.connect(self.add_stock_movement)

        low_stock_btn = QPushButton("⚠️ مخزون منخفض")
        low_stock_btn.clicked.connect(self.show_low_stock)

        export_btn = QPushButton("📊 تصدير")
        export_btn.clicked.connect(self.export_inventory)

        toolbar_layout.addWidget(search_label)
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(filter_label)
        toolbar_layout.addWidget(self.filter_combo)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(add_item_btn)
        toolbar_layout.addWidget(edit_item_btn)
        toolbar_layout.addWidget(stock_movement_btn)
        toolbar_layout.addWidget(low_stock_btn)
        toolbar_layout.addWidget(export_btn)

        layout.addLayout(toolbar_layout)

        # جدول المخزون
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(14)
        self.inventory_table.setHorizontalHeaderLabels([
            "الاسم", "الفئة", "نوع المادة", "الأبعاد", "الوحدة", "المخزون الحالي",
            "الحد الأدنى", "الحد الأقصى", "سعر التكلفة", "سعر البيع", "قيمة المخزون",
            "المورد", "الموقع", "الحالة"
        ])

        # تعديل عرض الأعمدة
        header = self.inventory_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)

        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.inventory_table.setAlternatingRowColors(True)
        self.inventory_table.setSortingEnabled(True)
        self.inventory_table.itemDoubleClicked.connect(self.show_item_details)

        layout.addWidget(self.inventory_table)

        # إحصائيات المخزون
        stats_layout = QHBoxLayout()

        self.total_items_label = QLabel("إجمالي العناصر: 0")
        self.total_value_label = QLabel("قيمة المخزون: 0.00 ريال")
        self.low_stock_count_label = QLabel("مخزون منخفض: 0")
        self.out_of_stock_label = QLabel("نفد المخزون: 0")

        stats_layout.addWidget(self.total_items_label)
        stats_layout.addWidget(self.total_value_label)
        stats_layout.addWidget(self.low_stock_count_label)
        stats_layout.addWidget(self.out_of_stock_label)
        stats_layout.addStretch()

        layout.addLayout(stats_layout)

        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Close)
        buttons.rejected.connect(self.close)
        layout.addWidget(buttons)

    def load_inventory(self):
        """تحميل المخزون"""
        items = self.db_manager.get_all_items()
        self.populate_table(items)
        self.update_statistics(items)

    def populate_table(self, items: List[Dict[str, Any]]):
        """ملء الجدول بعناصر المخزون"""
        self.inventory_table.setRowCount(len(items))

        for row, item in enumerate(items):
            self.inventory_table.setItem(row, 0, QTableWidgetItem(item.get('name', '')))
            self.inventory_table.setItem(row, 1, QTableWidgetItem(item.get('category', '')))
            self.inventory_table.setItem(row, 2, QTableWidgetItem(item.get('material_type', '')))
            self.inventory_table.setItem(row, 3, QTableWidgetItem(item.get('dimensions', '')))
            self.inventory_table.setItem(row, 4, QTableWidgetItem(item.get('unit', '')))

            # المخزون الحالي مع تلوين حسب الحالة
            current_stock = item.get('current_stock', 0)
            stock_item = QTableWidgetItem(f"{current_stock:.1f}")

            stock_status = item.get('stock_status', 'طبيعي')
            if stock_status == 'منخفض':
                stock_item.setBackground(QColor("#fff3cd"))
            elif stock_status == 'مرتفع':
                stock_item.setBackground(QColor("#d1ecf1"))
            elif current_stock == 0:
                stock_item.setBackground(QColor("#f8d7da"))

            self.inventory_table.setItem(row, 5, stock_item)

            self.inventory_table.setItem(row, 6, QTableWidgetItem(f"{item.get('minimum_stock', 0):.1f}"))
            self.inventory_table.setItem(row, 7, QTableWidgetItem(f"{item.get('maximum_stock', 0):.1f}"))
            self.inventory_table.setItem(row, 8, QTableWidgetItem(f"{item.get('cost_price', 0):.2f}"))
            self.inventory_table.setItem(row, 9, QTableWidgetItem(f"{item.get('selling_price', 0):.2f}"))
            self.inventory_table.setItem(row, 10, QTableWidgetItem(f"{item.get('stock_value', 0):.2f}"))
            self.inventory_table.setItem(row, 11, QTableWidgetItem(item.get('supplier', '')))
            self.inventory_table.setItem(row, 12, QTableWidgetItem(item.get('location', '')))

            # حالة المخزون
            status_item = QTableWidgetItem(stock_status)
            if stock_status == 'منخفض':
                status_item.setBackground(QColor("#fff3cd"))
            elif stock_status == 'مرتفع':
                status_item.setBackground(QColor("#d1ecf1"))
            else:
                status_item.setBackground(QColor("#d4edda"))

            self.inventory_table.setItem(row, 13, status_item)

            # حفظ ID العنصر في البيانات المخفية
            self.inventory_table.item(row, 0).setData(Qt.UserRole, item.get('id'))

    def update_statistics(self, items: List[Dict[str, Any]]):
        """تحديث إحصائيات المخزون"""
        total_items = len(items)
        total_value = sum(item.get('stock_value', 0) for item in items)
        low_stock_count = len([item for item in items if item.get('stock_status') == 'منخفض'])
        out_of_stock_count = len([item for item in items if item.get('current_stock', 0) == 0])

        self.total_items_label.setText(f"إجمالي العناصر: {total_items}")
        self.total_value_label.setText(f"قيمة المخزون: {total_value:.2f} ريال")
        self.low_stock_count_label.setText(f"مخزون منخفض: {low_stock_count}")
        self.out_of_stock_label.setText(f"نفد المخزون: {out_of_stock_count}")

    def search_inventory(self):
        """البحث في المخزون"""
        search_term = self.search_edit.text().strip()
        if search_term:
            items = self.db_manager.search_items(search_term)
        else:
            items = self.db_manager.get_all_items()

        self.populate_table(items)
        self.update_statistics(items)

    def apply_filter(self):
        """تطبيق الفلتر"""
        filter_type = self.filter_combo.currentText()

        if filter_type == "مخزون منخفض":
            items = self.db_manager.get_low_stock_items()
        elif filter_type == "نفد المخزون":
            all_items = self.db_manager.get_all_items()
            items = [item for item in all_items if item.get('current_stock', 0) == 0]
        elif filter_type == "مخزون مرتفع":
            all_items = self.db_manager.get_all_items()
            items = [item for item in all_items if item.get('stock_status') == 'مرتفع']
        else:  # جميع العناصر
            items = self.db_manager.get_all_items()

        self.populate_table(items)
        self.update_statistics(items)

    def add_new_item(self):
        """إضافة عنصر جديد"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إضافة عنصر جديد قريباً")
        # dialog = InventoryItemDialog(self)
        # if dialog.exec_() == QDialog.Accepted:
        #     self.load_inventory()

    def edit_selected_item(self):
        """تعديل العنصر المختار"""
        current_row = self.inventory_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر للتعديل")
            return

        QMessageBox.information(self, "معلومات", "سيتم تنفيذ تعديل العنصر قريباً")
        # item_id = self.inventory_table.item(current_row, 0).data(Qt.UserRole)
        # dialog = InventoryItemDialog(self, item_id)
        # if dialog.exec_() == QDialog.Accepted:
        #     self.load_inventory()

    def add_stock_movement(self):
        """إضافة حركة مخزون"""
        current_row = self.inventory_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر لإضافة حركة مخزون")
            return

        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إضافة حركة مخزون قريباً")
        # item_id = self.inventory_table.item(current_row, 0).data(Qt.UserRole)
        # item_name = self.inventory_table.item(current_row, 0).text()
        #
        # dialog = StockMovementDialog(self, item_id, item_name)
        # if dialog.exec_() == QDialog.Accepted:
        #     self.load_inventory()

    def show_item_details(self, item):
        """عرض تفاصيل العنصر"""
        row = item.row()
        item_name = self.inventory_table.item(row, 0).text()

        QMessageBox.information(self, "تفاصيل العنصر", f"عرض تفاصيل: {item_name}\nسيتم تنفيذ هذه الميزة قريباً")
        # item_id = self.inventory_table.item(row, 0).data(Qt.UserRole)
        # dialog = ItemDetailsDialog(self, item_id, item_name)
        # dialog.exec_()

    def show_low_stock(self):
        """عرض العناصر منخفضة المخزون"""
        items = self.db_manager.get_low_stock_items()
        if not items:
            QMessageBox.information(self, "معلومات", "لا توجد عناصر منخفضة المخزون")
            return

        # عرض تقرير بسيط
        report_text = "العناصر منخفضة المخزون:\n\n"
        for item in items:
            report_text += f"• {item.get('name', '')}: {item.get('current_stock', 0)} (الحد الأدنى: {item.get('minimum_stock', 0)})\n"

        if not items:
            report_text = "لا توجد عناصر منخفضة المخزون"

        QMessageBox.information(self, "تقرير المخزون المنخفض", report_text)

    def export_inventory(self):
        """تصدير المخزون"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "تصدير المخزون",
            f"inventory_report_{datetime.now().strftime('%Y%m%d')}.xlsx",
            "Excel Files (*.xlsx)"
        )

        if filename:
            try:
                import openpyxl
                from openpyxl.styles import Font, Alignment, PatternFill

                wb = openpyxl.Workbook()
                ws = wb.active
                ws.title = "تقرير المخزون"

                # العناوين
                headers = [
                    "الاسم", "الفئة", "نوع المادة", "الأبعاد", "الوحدة", "المخزون الحالي",
                    "الحد الأدنى", "الحد الأقصى", "سعر التكلفة", "سعر البيع", "قيمة المخزون",
                    "المورد", "الموقع", "الحالة", "آخر تحديث"
                ]

                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=1, col=col, value=header)
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")

                # البيانات
                items = self.db_manager.get_all_items()
                for row, item in enumerate(items, 2):
                    ws.cell(row=row, col=1, value=item.get('name', ''))
                    ws.cell(row=row, col=2, value=item.get('category', ''))
                    ws.cell(row=row, col=3, value=item.get('material_type', ''))
                    ws.cell(row=row, col=4, value=item.get('dimensions', ''))
                    ws.cell(row=row, col=5, value=item.get('unit', ''))
                    ws.cell(row=row, col=6, value=item.get('current_stock', 0))
                    ws.cell(row=row, col=7, value=item.get('minimum_stock', 0))
                    ws.cell(row=row, col=8, value=item.get('maximum_stock', 0))
                    ws.cell(row=row, col=9, value=item.get('cost_price', 0))
                    ws.cell(row=row, col=10, value=item.get('selling_price', 0))
                    ws.cell(row=row, col=11, value=item.get('stock_value', 0))
                    ws.cell(row=row, col=12, value=item.get('supplier', ''))
                    ws.cell(row=row, col=13, value=item.get('location', ''))
                    ws.cell(row=row, col=14, value=item.get('stock_status', ''))

                    last_updated = item.get('last_updated', '')
                    if last_updated:
                        last_updated = last_updated[:10]
                    ws.cell(row=row, col=15, value=last_updated)

                # تعديل عرض الأعمدة
                for col in range(1, len(headers) + 1):
                    ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15

                wb.save(filename)
                QMessageBox.information(self, "نجح", f"تم تصدير تقرير المخزون:\n{filename}")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في التصدير:\n{str(e)}")
