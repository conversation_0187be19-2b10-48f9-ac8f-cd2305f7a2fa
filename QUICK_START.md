# دليل البدء السريع - CutList Desktop App

## التشغيل السريع

### Windows
```bash
# انقر نقراً مزدوجاً على الملف
run_app.bat

# أو من سطر الأوامر
python main_simple.py
```

### macOS/Linux
```bash
# اجعل الملف قابلاً للتنفيذ
chmod +x run_app.sh

# شغل التطبيق
./run_app.sh

# أو مباشرة
python3 main_simple.py
```

## الاستخدام الأساسي

### 1. إضافة مكون جديد
1. انقر على زر **"إضافة مكون يدوياً"**
2. أدخل البيانات:
   - **اسم المكون**: مثل "لوح علوي"
   - **الطول**: بالمليمتر (مثل 1200)
   - **العرض**: بالمليمتر (مثل 600)
   - **السماكة**: بالمليمتر (مثل 18)
   - **نوع المادة**: اختر من القائمة
   - **الكمية**: عدد القطع المطلوبة
3. انقر **"موافق"**

### 2. فتح ملف ثلاثي الأبعاد
1. انقر على زر **"فتح ملف ثلاثي الأبعاد"**
2. اختر ملف بصيغة `.obj`, `.stl`, أو `.dae`
3. سيتم إضافة مكونات تجريبية للجدول

### 3. إدارة الجدول
- **مراجعة البيانات**: جميع المكونات تظهر في الجدول
- **مسح الجدول**: انقر "مسح الجدول" لحذف جميع البيانات
- **تعديل البيانات**: انقر نقراً مزدوجاً على أي خلية للتعديل

## نصائح مفيدة

### وحدات القياس
- جميع الأبعاد بالمليمتر (mm)
- 1 سم = 10 مم
- 1 متر = 1000 مم

### أنواع المواد المتاحة
- **خشب**: للألواح الخشبية العادية
- **MDF**: للألواح المضغوطة
- **خشب رقائقي**: للألواح الرقيقة
- **ألمنيوم**: للقطع المعدنية
- **بلاستيك**: للقطع البلاستيكية

### أمثلة على الأبعاد الشائعة
- **لوح خشب قياسي**: 2440 × 1220 × 18 مم
- **لوح MDF**: 2500 × 1250 × 15 مم
- **خشب رقائقي**: 2440 × 1220 × 6 مم

## استكشاف الأخطاء

### التطبيق لا يعمل
1. تأكد من تثبيت Python 3.8+
2. ثبت PyQt5: `pip install PyQt5`
3. شغل من سطر الأوامر لرؤية رسائل الخطأ

### النص العربي لا يظهر بشكل صحيح
- تأكد من أن نظامك يدعم الخطوط العربية
- جرب تغيير خط النظام

### الملفات ثلاثية الأبعاد لا تعمل
- في الإصدار المبسط، الملفات تضيف بيانات تجريبية فقط
- للتحليل الحقيقي، استخدم الإصدار الكامل مع مكتبة trimesh

## الخطوات التالية

### للمطورين
1. ثبت المكتبات الإضافية: `pip install trimesh pandas numpy`
2. شغل الإصدار الكامل: `python main.py`
3. طور ميزات إضافية في المجلدات المخصصة

### للمستخدمين
1. جرب إضافة مكونات مختلفة
2. احفظ لقطة شاشة من الجدول للمرجع
3. انتظر الميزات القادمة (تصدير Excel/PDF)

## الدعم

إذا واجهت مشاكل:
1. راجع ملف README.md للتفاصيل الكاملة
2. تأكد من تثبيت جميع المتطلبات
3. شغل التطبيق من سطر الأوامر لرؤية رسائل الخطأ

---

**ملاحظة**: هذا الإصدار الأول من التطبيق. المزيد من الميزات قادمة قريباً!
