#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CutList Desktop App - Advanced Launcher
مشغل التطبيق المتقدم مع فحص المتطلبات
"""

import sys
import subprocess
import importlib
from typing import List, <PERSON><PERSON>


def check_python_version() -> bool:
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def check_required_packages() -> Tuple[List[str], List[str]]:
    """فحص المكتبات المطلوبة"""
    required_packages = [
        ('PyQt5', 'PyQt5'),
        ('trimesh', 'trimesh'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('openpyxl', 'openpyxl'),
        ('reportlab', 'reportlab'),
        ('arabic_reshaper', 'arabic-reshaper'),
        ('bidi', 'python-bidi')
    ]
    
    available = []
    missing = []
    
    for module_name, package_name in required_packages:
        try:
            importlib.import_module(module_name)
            available.append(package_name)
            print(f"✅ {package_name}")
        except ImportError:
            missing.append(package_name)
            print(f"❌ {package_name} - غير مثبت")
    
    return available, missing


def install_missing_packages(missing_packages: List[str]) -> bool:
    """تثبيت المكتبات المفقودة"""
    if not missing_packages:
        return True
    
    print(f"\n🔧 جاري تثبيت {len(missing_packages)} مكتبة مفقودة...")
    
    try:
        for package in missing_packages:
            print(f"تثبيت {package}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ تم تثبيت {package}")
            else:
                print(f"❌ فشل تثبيت {package}: {result.stderr}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False


def run_application():
    """تشغيل التطبيق"""
    try:
        print("\n🚀 تشغيل التطبيق...")
        
        # استيراد وتشغيل التطبيق
        from main import main
        main()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من وجود ملف main.py في نفس المجلد")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False
    
    return True


def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🎯 CutList Desktop App - Advanced Launcher")
    print("تطبيق جدول القطع المتقدم")
    print("=" * 50)
    
    # فحص إصدار Python
    print("\n📋 فحص المتطلبات:")
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # فحص المكتبات
    print("\n📦 فحص المكتبات:")
    available, missing = check_required_packages()
    
    if missing:
        print(f"\n⚠️  يوجد {len(missing)} مكتبة مفقودة:")
        for package in missing:
            print(f"   • {package}")
        
        # سؤال المستخدم عن التثبيت
        response = input("\nهل تريد تثبيت المكتبات المفقودة؟ (y/n): ").lower()
        
        if response in ['y', 'yes', 'نعم', 'ن']:
            if install_missing_packages(missing):
                print("\n✅ تم تثبيت جميع المكتبات بنجاح!")
            else:
                print("\n❌ فشل في تثبيت بعض المكتبات")
                print("يمكنك تشغيل التطبيق الأساسي بدون الميزات المتقدمة")
                response = input("هل تريد المتابعة؟ (y/n): ").lower()
                if response not in ['y', 'yes', 'نعم', 'ن']:
                    return
        else:
            print("\n⚠️  سيتم تشغيل التطبيق بالميزات الأساسية فقط")
    else:
        print("\n✅ جميع المكتبات متوفرة!")
    
    # تشغيل التطبيق
    if not run_application():
        input("\nاضغط Enter للخروج...")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
