#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CutList Pro - Furniture Designer Edition Launcher
مشغل تطبيق مصمم الأثاث الاحترافي
"""

import sys
import subprocess
import importlib
import os
from typing import List, <PERSON><PERSON>


def check_python_version() -> bool:
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def check_professional_packages() -> Tuple[List[str], List[str]]:
    """فحص المكتبات الاحترافية"""
    professional_packages = [
        ('PyQt5', 'PyQt5'),
        ('trimesh', 'trimesh'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('openpyxl', 'openpyxl'),
        ('reportlab', 'reportlab'),
        ('arabic_reshaper', 'arabic-reshaper'),
        ('bidi', 'python-bidi'),
        ('matplotlib', 'matplotlib'),
        ('Pillow', 'Pillow'),
        ('scipy', 'scipy')
    ]
    
    available = []
    missing = []
    
    print("\n📦 فحص المكتبات الاحترافية:")
    for module_name, package_name in professional_packages:
        try:
            importlib.import_module(module_name)
            available.append(package_name)
            print(f"✅ {package_name}")
        except ImportError:
            missing.append(package_name)
            print(f"❌ {package_name} - غير مثبت")
    
    return available, missing


def install_missing_packages(missing_packages: List[str]) -> bool:
    """تثبيت المكتبات المفقودة"""
    if not missing_packages:
        return True
    
    print(f"\n🔧 جاري تثبيت {len(missing_packages)} مكتبة مفقودة...")
    
    try:
        # تثبيت جميع المكتبات في أمر واحد لتوفير الوقت
        cmd = [sys.executable, '-m', 'pip', 'install'] + missing_packages
        print(f"تنفيذ: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت جميع المكتبات بنجاح!")
            return True
        else:
            print(f"❌ فشل في التثبيت: {result.stderr}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False


def check_system_requirements():
    """فحص متطلبات النظام"""
    print("\n🖥️ فحص متطلبات النظام:")
    
    # فحص الذاكرة المتاحة
    try:
        import psutil
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        print(f"✅ الذاكرة: {memory_gb:.1f} GB")
        
        if memory_gb < 4:
            print("⚠️  تحذير: يُنصح بـ 4 GB ذاكرة أو أكثر للأداء الأمثل")
    except ImportError:
        print("ℹ️  لا يمكن فحص الذاكرة (psutil غير مثبت)")
    
    # فحص مساحة القرص
    try:
        import shutil
        disk_usage = shutil.disk_usage('.')
        free_gb = disk_usage.free / (1024**3)
        print(f"✅ مساحة القرص المتاحة: {free_gb:.1f} GB")
        
        if free_gb < 1:
            print("⚠️  تحذير: مساحة قرص منخفضة")
    except:
        print("ℹ️  لا يمكن فحص مساحة القرص")


def create_sample_data():
    """إنشاء بيانات تجريبية"""
    print("\n📁 إنشاء بيانات تجريبية...")
    
    # إنشاء المجلدات المطلوبة
    directories = [
        'furniture_templates',
        'clients_data',
        'pricing_data',
        'inventory_data',
        'projects',
        'cnc_output',
        'exports'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ تم إنشاء مجلد: {directory}")


def run_application():
    """تشغيل التطبيق"""
    try:
        print("\n🚀 تشغيل تطبيق مصمم الأثاث الاحترافي...")
        
        # استيراد وتشغيل التطبيق
        from furniture_designer_app import FurnitureDesignerMainWindow, QApplication
        
        app = QApplication(sys.argv)
        
        # تعيين معلومات التطبيق
        app.setApplicationName("CutList Pro - Furniture Designer")
        app.setApplicationVersion("3.0")
        app.setOrganizationName("Professional Furniture Design Solutions")
        
        # إنشاء النافذة الرئيسية
        window = FurnitureDesignerMainWindow()
        window.show()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("💡 نصائح للاستخدام:")
        print("   • ابدأ بتصفح قوالب الأثاث في اللوحة اليسرى")
        print("   • استخدم أدوات التصميم لإضافة مكونات جديدة")
        print("   • جرب محسن القطع لتوفير المواد")
        print("   • صدر ملفات CNC للإنتاج المباشر")
        
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من وجود ملف furniture_designer_app.py في نفس المجلد")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False
    
    return True


def show_welcome_message():
    """عرض رسالة الترحيب"""
    print("=" * 70)
    print("🪑 CutList Pro - Furniture Designer Edition")
    print("   تطبيق مصمم الأثاث الاحترافي")
    print("=" * 70)
    print("🎯 الميزات الاحترافية:")
    print("   • مكتبة قوالب أثاث شاملة")
    print("   • محسن القطع المتقدم")
    print("   • إدارة العملاء والمشاريع")
    print("   • تصدير ملفات CNC")
    print("   • حاسبة التكلفة الذكية")
    print("   • إدارة المخزون")
    print("   • طباعة الملصقات")
    print("=" * 70)


def main():
    """الدالة الرئيسية"""
    show_welcome_message()
    
    # فحص إصدار Python
    print("\n📋 فحص المتطلبات الأساسية:")
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # فحص متطلبات النظام
    check_system_requirements()
    
    # فحص المكتبات الاحترافية
    available, missing = check_professional_packages()
    
    if missing:
        print(f"\n⚠️  يوجد {len(missing)} مكتبة مفقودة من أصل {len(available) + len(missing)}")
        print("المكتبات المفقودة:")
        for package in missing:
            print(f"   • {package}")
        
        # سؤال المستخدم عن التثبيت
        print(f"\nالمكتبات المتوفرة: {len(available)}")
        print(f"المكتبات المفقودة: {len(missing)}")
        
        response = input("\nهل تريد تثبيت المكتبات المفقودة؟ (y/n): ").lower()
        
        if response in ['y', 'yes', 'نعم', 'ن']:
            if install_missing_packages(missing):
                print("\n✅ تم تثبيت جميع المكتبات بنجاح!")
            else:
                print("\n❌ فشل في تثبيت بعض المكتبات")
                print("يمكنك تشغيل التطبيق بالميزات المتاحة")
                response = input("هل تريد المتابعة؟ (y/n): ").lower()
                if response not in ['y', 'yes', 'نعم', 'ن']:
                    return
        else:
            print("\n⚠️  سيتم تشغيل التطبيق بالميزات المتاحة فقط")
            print("بعض الميزات الاحترافية قد لا تعمل")
    else:
        print(f"\n✅ جميع المكتبات متوفرة! ({len(available)} مكتبة)")
    
    # إنشاء البيانات التجريبية
    create_sample_data()
    
    # تشغيل التطبيق
    if not run_application():
        input("\nاضغط Enter للخروج...")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
