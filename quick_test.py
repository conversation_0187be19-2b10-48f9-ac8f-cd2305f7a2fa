#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Test - CutList Pro Furniture Designer
اختبار سريع لتطبيق مصمم الأثاث
"""

import sys
import os
from datetime import datetime


def test_furniture_templates():
    """اختبار قوالب الأثاث"""
    print("🪑 اختبار قوالب الأثاث...")
    
    try:
        from furniture.furniture_templates import FurnitureTemplateManager
        
        manager = FurnitureTemplateManager("test_templates")
        
        # عرض الفئات المتاحة
        categories = manager.get_all_categories()
        print(f"✅ الفئات المتاحة: {categories}")
        
        # عرض القوالب
        for category in categories:
            templates = manager.get_templates_by_category(category)
            print(f"   {category}: {len(templates)} قالب")
            
            for template in templates:
                print(f"     - {template.name} ({len(template.components)} مكون)")
        
        # تنظيف
        import shutil
        if os.path.exists("test_templates"):
            shutil.rmtree("test_templates")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار القوالب: {e}")
        return False


def test_client_management():
    """اختبار إدارة العملاء"""
    print("\n👥 اختبار إدارة العملاء...")
    
    try:
        from clients.client_manager import ClientManager, Client, ClientContact, ClientType
        
        manager = ClientManager("test_clients")
        
        # إضافة عميل تجريبي
        contact = ClientContact(
            phone="0501234567",
            email="<EMAIL>",
            address="شارع الملك فهد",
            city="الرياض"
        )
        
        client = Client(
            id=manager.generate_client_id(),
            name="أحمد محمد",
            client_type=ClientType.INDIVIDUAL,
            contact=contact
        )
        
        success = manager.add_client(client)
        print(f"✅ إضافة عميل: {'نجح' if success else 'فشل'}")
        
        # البحث في العملاء
        results = manager.search_clients("أحمد")
        print(f"✅ نتائج البحث: {len(results)} عميل")
        
        # تنظيف
        import shutil
        if os.path.exists("test_clients"):
            shutil.rmtree("test_clients")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العملاء: {e}")
        return False


def test_cutting_optimization():
    """اختبار تحسين القطع"""
    print("\n⚡ اختبار تحسين القطع...")
    
    try:
        from optimization.cutting_optimizer import CuttingOptimizer, CutPiece, Sheet
        
        optimizer = CuttingOptimizer()
        
        # قطع تجريبية
        pieces = [
            CutPiece("P1", "لوح علوي", 1200, 600, 18, "خشب", 1),
            CutPiece("P2", "لوح جانبي", 800, 400, 18, "خشب", 2),
            CutPiece("P3", "رف", 1100, 300, 18, "خشب", 2),
        ]
        
        # ألواح متاحة
        sheets = [
            Sheet("S1", 2440, 1220, 18, "خشب", 150, 5),
        ]
        
        # تحسين القطع
        layouts = optimizer.optimize_cutting_plan(pieces, sheets)
        print(f"✅ عدد التخطيطات: {len(layouts)}")
        
        for i, layout in enumerate(layouts, 1):
            print(f"   تخطيط {i}: كفاءة {layout.efficiency:.1%}, {len(layout.pieces)} قطعة")
        
        # حساب متطلبات المواد
        requirements = optimizer.calculate_material_requirements(pieces, sheets)
        print(f"✅ التكلفة الإجمالية: {requirements['total_cost']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحسين: {e}")
        return False


def test_pricing_engine():
    """اختبار محرك التسعير"""
    print("\n💰 اختبار محرك التسعير...")
    
    try:
        from pricing.market_pricing import MarketPricingEngine, ComplexityLevel
        
        engine = MarketPricingEngine("test_pricing")
        
        # مكونات تجريبية
        components = [
            {
                'name': 'لوح علوي',
                'dimensions': {'length': 1200, 'width': 600, 'thickness': 18},
                'material': 'خشب',
                'quantity': 1
            },
            {
                'name': 'لوح جانبي',
                'dimensions': {'length': 800, 'width': 400, 'thickness': 18},
                'material': 'خشب',
                'quantity': 2
            }
        ]
        
        # توليد تسعير
        pricing = engine.generate_project_pricing(
            components, 
            "TEST_PROJECT",
            ComplexityLevel.MEDIUM,
            profit_margin=0.25
        )
        
        print(f"✅ تكلفة المواد: {pricing.materials_cost:.2f} ريال")
        print(f"✅ تكلفة العمالة: {pricing.labor_cost:.2f} ريال")
        print(f"✅ السعر النهائي: {pricing.final_price:.2f} ريال")
        
        # تنظيف
        import shutil
        if os.path.exists("test_pricing"):
            shutil.rmtree("test_pricing")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التسعير: {e}")
        return False


def test_inventory_management():
    """اختبار إدارة المخزون"""
    print("\n📦 اختبار إدارة المخزون...")
    
    try:
        from inventory.inventory_manager import InventoryManager, InventoryItem
        
        manager = InventoryManager("test_inventory")
        
        # إضافة عنصر تجريبي
        item = InventoryItem(
            id=manager.generate_item_id(),
            name="لوح خشب 18مم",
            material_type="خشب",
            dimensions={"length": 2440, "width": 1220, "thickness": 18},
            quantity=10,
            unit="قطعة",
            cost_per_unit=150.0,
            min_stock_level=3
        )
        
        success = manager.add_item(item)
        print(f"✅ إضافة عنصر: {'نجح' if success else 'فشل'}")
        
        # فحص التوفر
        requirements = [
            {
                'material': 'خشب',
                'length': 1200,
                'width': 600,
                'thickness': 18,
                'quantity': 2
            }
        ]
        
        availability = manager.check_availability(requirements)
        print(f"✅ العناصر المتوفرة: {len(availability['available'])}")
        
        # قيمة المخزون
        value = manager.get_inventory_value()
        print(f"✅ قيمة المخزون: {value['total_value']:.2f} ريال")
        
        # تنظيف
        import shutil
        if os.path.exists("test_inventory"):
            shutil.rmtree("test_inventory")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المخزون: {e}")
        return False


def test_cnc_export():
    """اختبار تصدير CNC"""
    print("\n🤖 اختبار تصدير CNC...")
    
    try:
        from cnc.cnc_exporter import CNCExporter
        
        exporter = CNCExporter()
        
        # مكون تجريبي
        component = {
            'name': 'لوح علوي',
            'dimensions': {'length': 1200, 'width': 600, 'thickness': 18},
            'material': 'خشب',
            'drilling': [
                {'x': 100, 'y': 100, 'diameter': 5, 'depth': 18}
            ]
        }
        
        # تصدير إلى G-code
        filepath = exporter.export_component_to_gcode(component, "test_cnc")
        print(f"✅ تم تصدير ملف CNC: {os.path.basename(filepath)}")
        
        # فحص الملف
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"✅ عدد أسطر G-code: {len(lines)}")
        
        # تنظيف
        import shutil
        if os.path.exists("test_cnc"):
            shutil.rmtree("test_cnc")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار CNC: {e}")
        return False


def test_label_printing():
    """اختبار طباعة الملصقات"""
    print("\n🏷️ اختبار طباعة الملصقات...")
    
    try:
        from labels.label_printer import LabelPrinter
        
        printer = LabelPrinter()
        
        # مكونات تجريبية
        components = [
            {
                'name': 'سطح المكتب',
                'dimensions': {'length': 1200, 'width': 600, 'thickness': 25},
                'material': 'خشب',
                'quantity': 1
            }
        ]
        
        # إنشاء ملصقات
        labels_path = printer.create_component_labels(components, "test_labels.pdf")
        print(f"✅ تم إنشاء ملصقات: {os.path.basename(labels_path)}")
        
        # إنشاء رمز QR
        qr_path = printer.create_qr_code("TEST:123", "test_qr.png")
        if qr_path:
            print(f"✅ تم إنشاء رمز QR: {os.path.basename(qr_path)}")
        
        # تنظيف
        for file in ["test_labels.pdf", "test_qr.png"]:
            if os.path.exists(file):
                os.remove(file)
                
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الملصقات: {e}")
        return False


def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار سريع لتطبيق مصمم الأثاث الاحترافي")
    print("=" * 60)
    
    tests = [
        ("قوالب الأثاث", test_furniture_templates),
        ("إدارة العملاء", test_client_management),
        ("تحسين القطع", test_cutting_optimization),
        ("محرك التسعير", test_pricing_engine),
        ("إدارة المخزون", test_inventory_management),
        ("تصدير CNC", test_cnc_export),
        ("طباعة الملصقات", test_label_printing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام.")
    else:
        print("⚠️  بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")
    
    print("=" * 60)
    
    return passed == total


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
