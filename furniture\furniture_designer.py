#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Furniture Designer
مصمم الأثاث التفاعلي
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class DesignComponent:
    """مكون تصميم"""
    id: str
    name: str
    x: float = 0.0
    y: float = 0.0
    z: float = 0.0
    length: float = 100.0
    width: float = 100.0
    thickness: float = 18.0
    material: str = "خشب"
    color: str = "#8B4513"
    visible: bool = True


class FurnitureDesigner:
    """مصمم الأثاث التفاعلي"""
    
    def __init__(self):
        self.components: List[DesignComponent] = []
        self.selected_component: Optional[DesignComponent] = None
        self.design_name: str = "تصميم جديد"
    
    def add_component(self, component: DesignComponent) -> bool:
        """إضافة مكون جديد"""
        try:
            self.components.append(component)
            return True
        except Exception as e:
            print(f"خطأ في إضافة المكون: {e}")
            return False
    
    def remove_component(self, component_id: str) -> bool:
        """حذف مكون"""
        try:
            self.components = [c for c in self.components if c.id != component_id]
            return True
        except Exception as e:
            print(f"خطأ في حذف المكون: {e}")
            return False
    
    def get_component(self, component_id: str) -> Optional[DesignComponent]:
        """الحصول على مكون بالمعرف"""
        for component in self.components:
            if component.id == component_id:
                return component
        return None
    
    def update_component(self, component_id: str, **kwargs) -> bool:
        """تحديث خصائص مكون"""
        try:
            component = self.get_component(component_id)
            if component:
                for key, value in kwargs.items():
                    if hasattr(component, key):
                        setattr(component, key, value)
                return True
            return False
        except Exception as e:
            print(f"خطأ في تحديث المكون: {e}")
            return False
    
    def duplicate_component(self, component_id: str) -> Optional[DesignComponent]:
        """نسخ مكون"""
        try:
            original = self.get_component(component_id)
            if original:
                new_component = DesignComponent(
                    id=f"{original.id}_copy",
                    name=f"{original.name} (نسخة)",
                    x=original.x + 50,
                    y=original.y + 50,
                    z=original.z,
                    length=original.length,
                    width=original.width,
                    thickness=original.thickness,
                    material=original.material,
                    color=original.color
                )
                self.add_component(new_component)
                return new_component
            return None
        except Exception as e:
            print(f"خطأ في نسخ المكون: {e}")
            return None
    
    def get_design_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص التصميم"""
        total_volume = 0.0
        materials_used = set()
        
        for component in self.components:
            volume = (component.length * component.width * component.thickness) / 1000000  # سم³
            total_volume += volume
            materials_used.add(component.material)
        
        return {
            'name': self.design_name,
            'components_count': len(self.components),
            'total_volume': total_volume,
            'materials_used': list(materials_used),
            'components': [
                {
                    'id': c.id,
                    'name': c.name,
                    'dimensions': {
                        'length': c.length,
                        'width': c.width,
                        'thickness': c.thickness
                    },
                    'material': c.material,
                    'position': {'x': c.x, 'y': c.y, 'z': c.z}
                }
                for c in self.components
            ]
        }
    
    def clear_design(self):
        """مسح التصميم"""
        self.components.clear()
        self.selected_component = None
        self.design_name = "تصميم جديد"
    
    def load_from_template(self, template_components: List[Dict[str, Any]]) -> bool:
        """تحميل من قالب"""
        try:
            self.clear_design()
            
            for i, comp_data in enumerate(template_components):
                component = DesignComponent(
                    id=f"comp_{i}",
                    name=comp_data.get('name', f'مكون {i+1}'),
                    length=comp_data.get('length', 100),
                    width=comp_data.get('width', 100),
                    thickness=comp_data.get('thickness', 18),
                    material=comp_data.get('material', 'خشب'),
                    x=i * 120,  # ترتيب المكونات
                    y=0,
                    z=0
                )
                self.add_component(component)
            
            return True
        except Exception as e:
            print(f"خطأ في تحميل القالب: {e}")
            return False


def test_furniture_designer():
    """اختبار مصمم الأثاث"""
    designer = FurnitureDesigner()
    
    # إضافة مكونات تجريبية
    comp1 = DesignComponent(
        id="top",
        name="سطح المكتب",
        length=1200,
        width=600,
        thickness=25
    )
    
    comp2 = DesignComponent(
        id="leg1",
        name="ساق المكتب",
        length=720,
        width=50,
        thickness=50,
        x=0,
        y=0,
        z=0
    )
    
    designer.add_component(comp1)
    designer.add_component(comp2)
    
    # نسخ مكون
    designer.duplicate_component("leg1")
    
    # ملخص التصميم
    summary = designer.get_design_summary()
    print(f"التصميم: {summary['name']}")
    print(f"عدد المكونات: {summary['components_count']}")
    print(f"الحجم الإجمالي: {summary['total_volume']:.2f} سم³")
    print(f"المواد المستخدمة: {summary['materials_used']}")


if __name__ == "__main__":
    test_furniture_designer()
