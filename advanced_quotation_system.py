#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Quotation System
نظام عروض الأسعار المتقدم
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *


@dataclass
class QuotationItem:
    """عنصر في عرض السعر"""
    id: str
    name: str
    description: str
    quantity: float
    unit: str
    unit_price: float
    discount_percent: float = 0.0
    
    @property
    def subtotal(self) -> float:
        """المجموع الفرعي"""
        return self.quantity * self.unit_price
    
    @property
    def discount_amount(self) -> float:
        """مبلغ الخصم"""
        return self.subtotal * (self.discount_percent / 100.0)
    
    @property
    def total(self) -> float:
        """المجموع بعد الخصم"""
        return self.subtotal - self.discount_amount


@dataclass
class Quotation:
    """عرض سعر"""
    id: str
    client_id: str
    client_name: str
    project_name: str
    quotation_number: str
    quotation_date: str
    valid_until: str
    items: List[QuotationItem]
    subtotal: float = 0.0
    discount_percent: float = 0.0
    discount_amount: float = 0.0
    tax_percent: float = 15.0
    tax_amount: float = 0.0
    total_amount: float = 0.0
    notes: str = ""
    terms_conditions: str = ""
    status: str = "مسودة"  # مسودة، مرسل، مقبول، مرفوض، منتهي الصلاحية
    created_by: str = ""
    created_date: str = ""
    
    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()
        self.calculate_totals()
    
    def calculate_totals(self):
        """حساب المجاميع"""
        self.subtotal = sum(item.total for item in self.items)
        self.discount_amount = self.subtotal * (self.discount_percent / 100.0)
        taxable_amount = self.subtotal - self.discount_amount
        self.tax_amount = taxable_amount * (self.tax_percent / 100.0)
        self.total_amount = taxable_amount + self.tax_amount


class QuotationDatabaseManager:
    """مدير قاعدة بيانات عروض الأسعار"""
    
    def __init__(self, db_path: str = "quotations.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """تهيئة قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول عروض الأسعار
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS quotations (
                id TEXT PRIMARY KEY,
                client_id TEXT,
                client_name TEXT,
                project_name TEXT,
                quotation_number TEXT UNIQUE,
                quotation_date TEXT,
                valid_until TEXT,
                subtotal REAL DEFAULT 0.0,
                discount_percent REAL DEFAULT 0.0,
                discount_amount REAL DEFAULT 0.0,
                tax_percent REAL DEFAULT 15.0,
                tax_amount REAL DEFAULT 0.0,
                total_amount REAL DEFAULT 0.0,
                notes TEXT,
                terms_conditions TEXT,
                status TEXT DEFAULT 'مسودة',
                created_by TEXT,
                created_date TEXT
            )
        ''')
        
        # جدول عناصر عروض الأسعار
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS quotation_items (
                id TEXT PRIMARY KEY,
                quotation_id TEXT,
                name TEXT,
                description TEXT,
                quantity REAL,
                unit TEXT,
                unit_price REAL,
                discount_percent REAL DEFAULT 0.0,
                FOREIGN KEY (quotation_id) REFERENCES quotations (id)
            )
        ''')
        
        # جدول قوالب عروض الأسعار
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS quotation_templates (
                id TEXT PRIMARY KEY,
                name TEXT,
                description TEXT,
                items_json TEXT,
                created_date TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def generate_quotation_number(self) -> str:
        """توليد رقم عرض سعر"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على آخر رقم
            cursor.execute('''
                SELECT quotation_number FROM quotations 
                WHERE quotation_number LIKE 'QUO-%' 
                ORDER BY created_date DESC LIMIT 1
            ''')
            
            result = cursor.fetchone()
            if result:
                last_number = int(result[0].split('-')[1])
                new_number = last_number + 1
            else:
                new_number = 1
            
            conn.close()
            return f"QUO-{new_number:06d}"
            
        except Exception as e:
            print(f"خطأ في توليد رقم عرض السعر: {e}")
            return f"QUO-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    def save_quotation(self, quotation: Quotation) -> bool:
        """حفظ عرض السعر"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # حفظ عرض السعر
            cursor.execute('''
                INSERT OR REPLACE INTO quotations 
                (id, client_id, client_name, project_name, quotation_number, quotation_date,
                 valid_until, subtotal, discount_percent, discount_amount, tax_percent,
                 tax_amount, total_amount, notes, terms_conditions, status, created_by, created_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                quotation.id, quotation.client_id, quotation.client_name, quotation.project_name,
                quotation.quotation_number, quotation.quotation_date, quotation.valid_until,
                quotation.subtotal, quotation.discount_percent, quotation.discount_amount,
                quotation.tax_percent, quotation.tax_amount, quotation.total_amount,
                quotation.notes, quotation.terms_conditions, quotation.status,
                quotation.created_by, quotation.created_date
            ))
            
            # حذف العناصر القديمة
            cursor.execute('DELETE FROM quotation_items WHERE quotation_id = ?', (quotation.id,))
            
            # حفظ العناصر
            for item in quotation.items:
                cursor.execute('''
                    INSERT INTO quotation_items 
                    (id, quotation_id, name, description, quantity, unit, unit_price, discount_percent)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    item.id, quotation.id, item.name, item.description,
                    item.quantity, item.unit, item.unit_price, item.discount_percent
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ عرض السعر: {e}")
            return False
    
    def get_all_quotations(self) -> List[Dict[str, Any]]:
        """الحصول على جميع عروض الأسعار"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM quotations ORDER BY created_date DESC')
            quotations_data = cursor.fetchall()
            
            quotations = []
            columns = [desc[0] for desc in cursor.description]
            
            for quotation_data in quotations_data:
                quotation_dict = dict(zip(columns, quotation_data))
                quotations.append(quotation_dict)
            
            conn.close()
            return quotations
            
        except Exception as e:
            print(f"خطأ في تحميل عروض الأسعار: {e}")
            return []
    
    def load_quotation(self, quotation_id: str) -> Optional[Quotation]:
        """تحميل عرض سعر"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # تحميل عرض السعر
            cursor.execute('SELECT * FROM quotations WHERE id = ?', (quotation_id,))
            quotation_data = cursor.fetchone()
            
            if not quotation_data:
                return None
            
            # تحميل العناصر
            cursor.execute('SELECT * FROM quotation_items WHERE quotation_id = ?', (quotation_id,))
            items_data = cursor.fetchall()
            
            items = []
            for item_data in items_data:
                item = QuotationItem(
                    id=item_data[0],
                    name=item_data[2],
                    description=item_data[3],
                    quantity=item_data[4],
                    unit=item_data[5],
                    unit_price=item_data[6],
                    discount_percent=item_data[7] or 0.0
                )
                items.append(item)
            
            quotation = Quotation(
                id=quotation_data[0],
                client_id=quotation_data[1] or "",
                client_name=quotation_data[2] or "",
                project_name=quotation_data[3] or "",
                quotation_number=quotation_data[4] or "",
                quotation_date=quotation_data[5] or "",
                valid_until=quotation_data[6] or "",
                items=items,
                subtotal=quotation_data[7] or 0.0,
                discount_percent=quotation_data[8] or 0.0,
                discount_amount=quotation_data[9] or 0.0,
                tax_percent=quotation_data[10] or 15.0,
                tax_amount=quotation_data[11] or 0.0,
                total_amount=quotation_data[12] or 0.0,
                notes=quotation_data[13] or "",
                terms_conditions=quotation_data[14] or "",
                status=quotation_data[15] or "مسودة",
                created_by=quotation_data[16] or "",
                created_date=quotation_data[17] or ""
            )
            
            conn.close()
            return quotation
            
        except Exception as e:
            print(f"خطأ في تحميل عرض السعر: {e}")
            return None


class QuotationManagementDialog(QDialog):
    """حوار إدارة عروض الأسعار"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = QuotationDatabaseManager()
        self.init_ui()
        self.load_quotations()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة عروض الأسعار")
        self.setModal(True)
        self.resize(1200, 800)
        
        layout = QVBoxLayout(self)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        # البحث
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث برقم العرض، اسم العميل، أو المشروع...")
        self.search_edit.textChanged.connect(self.search_quotations)
        
        # فلتر الحالة
        status_label = QLabel("الحالة:")
        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "مسودة", "مرسل", "مقبول", "مرفوض", "منتهي الصلاحية"])
        self.status_filter.currentTextChanged.connect(self.filter_by_status)
        
        # أزرار الإدارة
        new_quotation_btn = QPushButton("➕ عرض سعر جديد")
        new_quotation_btn.clicked.connect(self.create_new_quotation)
        
        edit_quotation_btn = QPushButton("✏️ تعديل")
        edit_quotation_btn.clicked.connect(self.edit_selected_quotation)
        
        duplicate_btn = QPushButton("📋 نسخ")
        duplicate_btn.clicked.connect(self.duplicate_quotation)
        
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.clicked.connect(self.export_to_pdf)
        
        send_email_btn = QPushButton("📧 إرسال إيميل")
        send_email_btn.clicked.connect(self.send_email)
        
        toolbar_layout.addWidget(search_label)
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(status_label)
        toolbar_layout.addWidget(self.status_filter)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(new_quotation_btn)
        toolbar_layout.addWidget(edit_quotation_btn)
        toolbar_layout.addWidget(duplicate_btn)
        toolbar_layout.addWidget(export_pdf_btn)
        toolbar_layout.addWidget(send_email_btn)
        
        layout.addLayout(toolbar_layout)
        
        # جدول عروض الأسعار
        self.quotations_table = QTableWidget()
        self.quotations_table.setColumnCount(9)
        self.quotations_table.setHorizontalHeaderLabels([
            "رقم العرض", "العميل", "المشروع", "تاريخ العرض", "صالح حتى",
            "المبلغ الإجمالي", "الحالة", "تاريخ الإنشاء", "المنشئ"
        ])
        
        # تعديل عرض الأعمدة
        header = self.quotations_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        
        self.quotations_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.quotations_table.setAlternatingRowColors(True)
        self.quotations_table.setSortingEnabled(True)
        self.quotations_table.itemDoubleClicked.connect(self.view_quotation_details)
        
        layout.addWidget(self.quotations_table)
        
        # إحصائيات
        stats_layout = QHBoxLayout()
        
        self.total_quotations_label = QLabel("إجمالي العروض: 0")
        self.pending_quotations_label = QLabel("في الانتظار: 0")
        self.accepted_quotations_label = QLabel("مقبولة: 0")
        self.total_value_label = QLabel("القيمة الإجمالية: 0.00 ريال")
        
        stats_layout.addWidget(self.total_quotations_label)
        stats_layout.addWidget(self.pending_quotations_label)
        stats_layout.addWidget(self.accepted_quotations_label)
        stats_layout.addWidget(self.total_value_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Close)
        buttons.rejected.connect(self.close)
        layout.addWidget(buttons)
    
    def load_quotations(self):
        """تحميل عروض الأسعار"""
        quotations = self.db_manager.get_all_quotations()
        self.populate_table(quotations)
        self.update_statistics(quotations)
    
    def populate_table(self, quotations: List[Dict[str, Any]]):
        """ملء الجدول بعروض الأسعار"""
        self.quotations_table.setRowCount(len(quotations))
        
        for row, quotation in enumerate(quotations):
            self.quotations_table.setItem(row, 0, QTableWidgetItem(quotation.get('quotation_number', '')))
            self.quotations_table.setItem(row, 1, QTableWidgetItem(quotation.get('client_name', '')))
            self.quotations_table.setItem(row, 2, QTableWidgetItem(quotation.get('project_name', '')))
            
            quotation_date = quotation.get('quotation_date', '')
            if quotation_date:
                quotation_date = quotation_date[:10]
            self.quotations_table.setItem(row, 3, QTableWidgetItem(quotation_date))
            
            valid_until = quotation.get('valid_until', '')
            if valid_until:
                valid_until = valid_until[:10]
            self.quotations_table.setItem(row, 4, QTableWidgetItem(valid_until))
            
            total_amount = quotation.get('total_amount', 0)
            self.quotations_table.setItem(row, 5, QTableWidgetItem(f"{total_amount:.2f} ريال"))
            
            # تلوين الحالة
            status = quotation.get('status', 'مسودة')
            status_item = QTableWidgetItem(status)
            
            if status == 'مقبول':
                status_item.setBackground(QColor("#d4edda"))
            elif status == 'مرفوض':
                status_item.setBackground(QColor("#f8d7da"))
            elif status == 'منتهي الصلاحية':
                status_item.setBackground(QColor("#f8d7da"))
            elif status == 'مرسل':
                status_item.setBackground(QColor("#d1ecf1"))
            else:  # مسودة
                status_item.setBackground(QColor("#fff3cd"))
            
            self.quotations_table.setItem(row, 6, status_item)
            
            created_date = quotation.get('created_date', '')
            if created_date:
                created_date = created_date[:10]
            self.quotations_table.setItem(row, 7, QTableWidgetItem(created_date))
            
            self.quotations_table.setItem(row, 8, QTableWidgetItem(quotation.get('created_by', '')))
            
            # حفظ ID عرض السعر في البيانات المخفية
            self.quotations_table.item(row, 0).setData(Qt.UserRole, quotation.get('id'))
    
    def update_statistics(self, quotations: List[Dict[str, Any]]):
        """تحديث الإحصائيات"""
        total_quotations = len(quotations)
        pending_quotations = len([q for q in quotations if q.get('status') in ['مسودة', 'مرسل']])
        accepted_quotations = len([q for q in quotations if q.get('status') == 'مقبول'])
        total_value = sum(q.get('total_amount', 0) for q in quotations if q.get('status') == 'مقبول')
        
        self.total_quotations_label.setText(f"إجمالي العروض: {total_quotations}")
        self.pending_quotations_label.setText(f"في الانتظار: {pending_quotations}")
        self.accepted_quotations_label.setText(f"مقبولة: {accepted_quotations}")
        self.total_value_label.setText(f"القيمة الإجمالية: {total_value:.2f} ريال")
    
    def search_quotations(self):
        """البحث في عروض الأسعار"""
        # TODO: تنفيذ البحث
        pass
    
    def filter_by_status(self):
        """فلترة حسب الحالة"""
        # TODO: تنفيذ الفلترة
        pass
    
    def create_new_quotation(self):
        """إنشاء عرض سعر جديد"""
        dialog = QuotationEditDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_quotations()
    
    def edit_selected_quotation(self):
        """تعديل عرض السعر المختار"""
        current_row = self.quotations_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عرض سعر للتعديل")
            return
        
        quotation_id = self.quotations_table.item(current_row, 0).data(Qt.UserRole)
        dialog = QuotationEditDialog(self, quotation_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_quotations()
    
    def duplicate_quotation(self):
        """نسخ عرض السعر"""
        # TODO: تنفيذ النسخ
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ النسخ قريباً")
    
    def export_to_pdf(self):
        """تصدير إلى PDF"""
        # TODO: تنفيذ التصدير إلى PDF
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ التصدير إلى PDF قريباً")
    
    def send_email(self):
        """إرسال إيميل"""
        # TODO: تنفيذ إرسال الإيميل
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إرسال الإيميل قريباً")
    
    def view_quotation_details(self, item):
        """عرض تفاصيل عرض السعر"""
        row = item.row()
        quotation_id = self.quotations_table.item(row, 0).data(Qt.UserRole)
        
        dialog = QuotationViewDialog(self, quotation_id)
        dialog.exec_()
