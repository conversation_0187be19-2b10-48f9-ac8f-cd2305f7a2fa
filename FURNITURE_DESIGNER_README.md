# 🪑 CutList Pro - Furniture Designer Edition

## تطبيق مصمم الأثاث الاحترافي

تطبيق سطح مكتب متقدم ومتكامل مصمم خصيصاً لمصممي الأثاث حسب الطلب، يوفر جميع الأدوات اللازمة من التصميم إلى الإنتاج.

---

## 🎯 الميزات الاحترافية

### 🎨 واجهة مستخدم متقدمة
- **تصميم احترافي** مع تبويبات متعددة ولوحات جانبية
- **دعم السحب والإفلات** للملفات ثلاثية الأبعاد
- **معاينة ثلاثية الأبعاد** للنماذج والتصاميم
- **أدوات قياس وتحليل بصري** متقدمة
- **واجهة عربية** كاملة مع دعم النصوص المختلطة

### 🏗️ مكتبة قوالب الأثاث
- **قوالب جاهزة** للأثاث الشائع (طاولات، خزائن، كراسي)
- **تخصيص القوالب** بمعاملات قابلة للتعديل
- **إضافة قوالب جديدة** بسهولة
- **تصنيف القوالب** حسب النوع والاستخدام
- **معاينة فورية** للقوالب قبل الاستخدام

### 💰 حاسبة التكلفة المتقدمة
- **أسعار السوق المحدثة** للمواد المختلفة
- **حساب تلقائي** للتكلفة والوزن
- **مستويات تعقيد** مختلفة للمشاريع
- **هوامش ربح** قابلة للتخصيص
- **خصومات العملاء** وطلبات الاستعجال

### ⚡ تحسين خطة القطع
- **خوارزميات متقدمة** لتقليل الهدر
- **تحسين متعدد الأهداف** (تكلفة، هدر، عدد الألواح)
- **مراعاة اتجاه الألياف** في الخشب
- **معاينة بصرية** لخطة القطع
- **تقارير كفاءة** مفصلة

### 👥 إدارة العملاء والمشاريع
- **قاعدة بيانات العملاء** الشاملة
- **تتبع المشاريع** من العرض إلى التسليم
- **أرشيف التصاميم** السابقة
- **نظام إشعارات** ومتابعة
- **تقارير الإيرادات** والإحصائيات

### 🏭 أدوات التصميم والإنتاج
- **رسم مخططات القطع** البصرية
- **تصدير ملفات CNC** جاهزة للتشغيل
- **طباعة ملصقات** وباركود للقطع
- **جدولة الإنتاج** والمواعيد
- **تعليمات التجميع** التلقائية

### 📊 التكامل والتصدير
- **تصدير عروض أسعار** احترافية
- **ملفات Excel و PDF** منسقة
- **تكامل مع برامج التصميم** (AutoCAD, SketchUp)
- **تقارير مفصلة** للعملاء
- **نسخ احتياطية** تلقائية

---

## 🚀 التشغيل السريع

### المتطلبات الأساسية
- **Python 3.8+**
- **4 GB RAM** (موصى به)
- **1 GB مساحة قرص** متاحة
- **Windows 10/11, macOS 10.14+, أو Linux**

### التثبيت والتشغيل

#### الطريقة الموصى بها
```bash
# تشغيل المشغل التلقائي
python run_furniture_designer.py
```

المشغل سيقوم بـ:
- ✅ فحص إصدار Python
- ✅ فحص المكتبات المطلوبة
- ✅ تثبيت المكتبات المفقودة تلقائياً
- ✅ إنشاء البيانات التجريبية
- ✅ تشغيل التطبيق

#### التثبيت اليدوي
```bash
# تثبيت المكتبات الأساسية
pip install PyQt5 trimesh pandas numpy

# تثبيت مكتبات التصدير
pip install openpyxl reportlab

# تثبيت دعم العربية
pip install arabic-reshaper python-bidi

# تثبيت مكتبات إضافية (اختيارية)
pip install matplotlib Pillow scipy

# تشغيل التطبيق
python furniture_designer_app.py
```

---

## 📖 دليل الاستخدام

### البدء السريع

#### 1. إنشاء مشروع جديد
1. انقر على **"🆕 جديد"** في شريط الأدوات
2. أدخل معلومات المشروع والعميل
3. اختر قالب أثاث أو ابدأ من الصفر

#### 2. تصميم الأثاث
- **استخدام القوالب**: تصفح القوالب في اللوحة اليسرى
- **إضافة مكونات**: استخدم زر "➕ إضافة مكون"
- **تخصيص الأبعاد**: انقر نقراً مزدوجاً للتعديل
- **معاينة ثلاثية الأبعاد**: راجع التصميم في الوقت الفعلي

#### 3. تحسين القطع
1. انتقل إلى تبويب **"⚡ تحسين القطع"**
2. اختر طريقة التحسين المناسبة
3. انقر **"تحسين خطة القطع"**
4. راجع النتائج والكفاءة

#### 4. التصدير والإنتاج
- **جدول القطع**: تصدير Excel أو PDF
- **ملفات CNC**: تصدير G-code للماكينات
- **الملصقات**: طباعة ملصقات المكونات
- **عرض السعر**: إنشاء عرض احترافي للعميل

### الميزات المتقدمة

#### إدارة المواد
- **قاعدة بيانات شاملة** للمواد والأسعار
- **تحديث الأسعار** من الموردين
- **حساب تلقائي** للتكلفة والوزن
- **تتبع المخزون** والكميات المتاحة

#### إدارة العملاء
- **ملفات العملاء** الكاملة
- **تاريخ المشاريع** والطلبات
- **خصومات مخصصة** لكل عميل
- **تقارير الإيرادات** الشهرية والسنوية

#### تحسين الإنتاج
- **جدولة المشاريع** حسب الأولوية
- **تتبع مراحل الإنتاج** المختلفة
- **تقدير أوقات التسليم** الدقيقة
- **تحسين استخدام العمالة** والمعدات

---

## 🏗️ هيكل المشروع

```
CutList-Pro-Furniture-Designer/
├── furniture_designer_app.py      # التطبيق الرئيسي
├── run_furniture_designer.py      # المشغل التلقائي
├── 
├── furniture/                     # قوالب الأثاث
│   ├── furniture_templates.py
│   └── furniture_designer.py
├── 
├── clients/                       # إدارة العملاء
│   └── client_manager.py
├── 
├── optimization/                  # تحسين القطع
│   └── cutting_optimizer.py
├── 
├── pricing/                       # التسعير والأسعار
│   └── market_pricing.py
├── 
├── inventory/                     # إدارة المخزون
│   └── inventory_manager.py
├── 
├── cnc/                          # تصدير CNC
│   └── cnc_exporter.py
├── 
├── export/                       # تصدير Excel/PDF
│   ├── export_excel.py
│   └── export_pdf.py
├── 
├── models/                       # تحليل النماذج ثلاثية الأبعاد
│   └── model_reader.py
├── 
├── materials/                    # إدارة المواد
│   └── material_manager.py
├── 
└── data/                        # البيانات والقوالب
    ├── furniture_templates/
    ├── clients_data/
    ├── pricing_data/
    └── inventory_data/
```

---

## 🎓 أمثلة عملية

### مثال 1: تصميم مكتب مكتبي

```python
# استخدام قالب المكتب
template = template_manager.get_template("مكتب مكتبي بسيط")

# تخصيص الأبعاد
custom_params = {
    "length_scale": 1.5,  # زيادة الطول 50%
    "width_scale": 1.2,   # زيادة العرض 20%
    "material": "MDF"     # تغيير المادة
}

# إنشاء التصميم المخصص
custom_desk = template_manager.customize_template(
    template.name, custom_params
)
```

### مثال 2: تحسين خطة القطع

```python
# إعداد القطع المطلوبة
pieces = [
    CutPiece("P1", "سطح المكتب", 1800, 800, 25, "MDF", 1),
    CutPiece("P2", "ساق جانبية", 720, 500, 25, "MDF", 2),
    CutPiece("P3", "رف سفلي", 1750, 450, 18, "MDF", 1)
]

# الألواح المتاحة
sheets = [
    Sheet("S1", 2440, 1220, 25, "MDF", 180, 5),
    Sheet("S2", 2440, 1220, 18, "MDF", 150, 3)
]

# تحسين القطع
optimizer = CuttingOptimizer()
layouts = optimizer.optimize_cutting_plan(pieces, sheets)

# عرض النتائج
for layout in layouts:
    print(f"كفاءة: {layout.efficiency:.2%}")
    print(f"هدر: {layout.waste_area:.0f} مم²")
```

### مثال 3: حساب التكلفة

```python
# إنشاء محرك التسعير
pricing_engine = MarketPricingEngine()

# حساب تسعير المشروع
pricing = pricing_engine.generate_project_pricing(
    components=desk_components,
    project_id="DESK_001",
    complexity=ComplexityLevel.MEDIUM,
    profit_margin=0.30,
    client_discount=0.10
)

print(f"تكلفة المواد: {pricing.materials_cost:.2f} ريال")
print(f"تكلفة العمالة: {pricing.labor_cost:.2f} ريال")
print(f"السعر النهائي: {pricing.final_price:.2f} ريال")
```

---

## 🔧 التخصيص والتطوير

### إضافة قوالب جديدة
```python
# إنشاء قالب جديد
new_template = FurnitureTemplate(
    name="خزانة كتب حديثة",
    category="خزائن",
    description="خزانة كتب بتصميم حديث",
    components=custom_components,
    parameters={"height": 2000, "width": 1200, "depth": 400}
)

# إضافة للمكتبة
template_manager.add_template(new_template)
```

### تخصيص أسعار المواد
```python
# تحديث سعر مادة
new_price = PriceEntry(
    material="خشب البلوط",
    price_per_unit=800,
    unit="م³",
    supplier="مورد الخشب الفاخر"
)

pricing_engine.update_material_price("خشب البلوط", new_price)
```

---

## 🆘 الدعم والمساعدة

### المشاكل الشائعة

#### التطبيق لا يبدأ
1. تأكد من إصدار Python (3.8+)
2. ثبت المكتبات المطلوبة
3. شغل من سطر الأوامر لرؤية الأخطاء

#### بطء في الأداء
1. أغلق التطبيقات الأخرى
2. تأكد من توفر ذاكرة كافية (4GB+)
3. قلل من تعقيد النماذج ثلاثية الأبعاد

#### مشاكل في التصدير
1. تأكد من صلاحيات الكتابة في المجلد
2. تحقق من تثبيت مكتبات التصدير
3. جرب مجلد مختلف للحفظ

### الحصول على المساعدة
- 📧 **البريد الإلكتروني**: <EMAIL>
- 💬 **المنتدى**: forum.cutlistpro.com
- 📚 **الوثائق**: docs.cutlistpro.com
- 🐛 **الإبلاغ عن الأخطاء**: github.com/cutlistpro/issues

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 🙏 شكر وتقدير

- **مجتمع PyQt5** لتوفير أدوات الواجهة الرسومية
- **مطوري trimesh** لمكتبة تحليل النماذج ثلاثية الأبعاد
- **مجتمع Python** للمكتبات المفتوحة المصدر
- **مصممي الأثاث** الذين قدموا ملاحظات قيمة

---

**🪑 CutList Pro - حيث يلتقي التصميم بالتكنولوجيا**

*تطبيق مصمم خصيصاً لمصممي الأثاث الطموحين الذين يسعون للتميز والاحترافية*
