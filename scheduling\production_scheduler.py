#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Production Scheduler
مجدول الإنتاج والمواعيد
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum


class TaskStatus(Enum):
    """حالة المهمة"""
    PENDING = "في الانتظار"
    IN_PROGRESS = "قيد التنفيذ"
    COMPLETED = "مكتملة"
    DELAYED = "متأخرة"
    CANCELLED = "ملغية"


class Priority(Enum):
    """أولوية المهمة"""
    LOW = "منخفضة"
    NORMAL = "عادية"
    HIGH = "عالية"
    URGENT = "عاجلة"


@dataclass
class ProductionTask:
    """مهمة إنتاج"""
    id: str
    name: str
    project_id: str
    component_id: str = ""
    task_type: str = "قطع"  # قطع، تجميع، تشطيب، فحص
    estimated_duration: int = 60  # بالدقائق
    actual_duration: int = 0
    start_date: datetime = None
    end_date: datetime = None
    actual_start: datetime = None
    actual_end: datetime = None
    status: TaskStatus = TaskStatus.PENDING
    priority: Priority = Priority.NORMAL
    assigned_worker: str = ""
    machine_required: str = ""
    materials_needed: List[str] = None
    dependencies: List[str] = None  # معرفات المهام التي يجب إكمالها أولاً
    notes: str = ""
    
    def __post_init__(self):
        if self.materials_needed is None:
            self.materials_needed = []
        if self.dependencies is None:
            self.dependencies = []


class ProductionScheduler:
    """مجدول الإنتاج"""
    
    def __init__(self):
        self.tasks: Dict[str, ProductionTask] = {}
        self.workers: Dict[str, Dict[str, Any]] = {}
        self.machines: Dict[str, Dict[str, Any]] = {}
        self.working_hours = {
            'start': 8,  # 8 صباحاً
            'end': 17,   # 5 مساءً
            'break_duration': 60,  # استراحة ساعة
            'working_days': [0, 1, 2, 3, 4]  # الاثنين إلى الجمعة
        }
    
    def add_task(self, task: ProductionTask) -> bool:
        """إضافة مهمة جديدة"""
        try:
            self.tasks[task.id] = task
            return True
        except Exception as e:
            print(f"خطأ في إضافة المهمة: {e}")
            return False
    
    def schedule_project(self, project_components: List[Dict[str, Any]], 
                        start_date: datetime = None,
                        priority: Priority = Priority.NORMAL) -> List[ProductionTask]:
        """جدولة مشروع كامل"""
        
        if start_date is None:
            start_date = datetime.now()
        
        tasks = []
        
        for component in project_components:
            # إنشاء مهام للمكون
            component_tasks = self._create_component_tasks(component, priority)
            
            # جدولة المهام
            scheduled_tasks = self._schedule_tasks(component_tasks, start_date)
            tasks.extend(scheduled_tasks)
            
            # تحديث تاريخ البدء للمكون التالي
            if scheduled_tasks:
                last_task = max(scheduled_tasks, key=lambda t: t.end_date)
                start_date = last_task.end_date
        
        # إضافة المهام للجدولة
        for task in tasks:
            self.add_task(task)
        
        return tasks
    
    def _create_component_tasks(self, component: Dict[str, Any], 
                               priority: Priority) -> List[ProductionTask]:
        """إنشاء مهام لمكون واحد"""
        tasks = []
        component_id = component.get('id', f"comp_{len(self.tasks)}")
        
        # مهمة القطع
        cutting_task = ProductionTask(
            id=f"{component_id}_cut",
            name=f"قطع {component.get('name', 'مكون')}",
            project_id=component.get('project_id', ''),
            component_id=component_id,
            task_type="قطع",
            estimated_duration=self._estimate_cutting_time(component),
            priority=priority,
            machine_required="منشار"
        )
        tasks.append(cutting_task)
        
        # مهمة الحفر (إذا لزم الأمر)
        drilling = component.get('drilling', [])
        if drilling:
            drilling_task = ProductionTask(
                id=f"{component_id}_drill",
                name=f"حفر {component.get('name', 'مكون')}",
                project_id=component.get('project_id', ''),
                component_id=component_id,
                task_type="حفر",
                estimated_duration=len(drilling) * 5,  # 5 دقائق لكل حفرة
                priority=priority,
                machine_required="مثقاب",
                dependencies=[cutting_task.id]
            )
            tasks.append(drilling_task)
        
        # مهمة التشطيب
        finishing_task = ProductionTask(
            id=f"{component_id}_finish",
            name=f"تشطيب {component.get('name', 'مكون')}",
            project_id=component.get('project_id', ''),
            component_id=component_id,
            task_type="تشطيب",
            estimated_duration=30,  # 30 دقيقة تشطيب
            priority=priority,
            dependencies=[cutting_task.id] + ([f"{component_id}_drill"] if drilling else [])
        )
        tasks.append(finishing_task)
        
        return tasks
    
    def _estimate_cutting_time(self, component: Dict[str, Any]) -> int:
        """تقدير وقت القطع بالدقائق"""
        dimensions = component.get('dimensions', {})
        
        # حساب محيط القطع
        length = dimensions.get('length', 0) / 1000  # تحويل إلى متر
        width = dimensions.get('width', 0) / 1000
        perimeter = 2 * (length + width)
        
        # تقدير الوقت (متر واحد = 5 دقائق)
        cutting_time = perimeter * 5
        
        # إضافة وقت الإعداد
        setup_time = 10
        
        return int(cutting_time + setup_time)
    
    def _schedule_tasks(self, tasks: List[ProductionTask], 
                       start_date: datetime) -> List[ProductionTask]:
        """جدولة قائمة من المهام"""
        
        # ترتيب المهام حسب التبعيات
        sorted_tasks = self._topological_sort(tasks)
        
        current_time = start_date
        
        for task in sorted_tasks:
            # العثور على أقرب وقت متاح
            available_time = self._find_next_available_slot(
                current_time, 
                task.estimated_duration,
                task.machine_required,
                task.assigned_worker
            )
            
            task.start_date = available_time
            task.end_date = self._add_working_time(available_time, task.estimated_duration)
            
            current_time = task.end_date
        
        return sorted_tasks
    
    def _topological_sort(self, tasks: List[ProductionTask]) -> List[ProductionTask]:
        """ترتيب المهام حسب التبعيات"""
        # خوارزمية بسيطة للترتيب الطوبولوجي
        sorted_tasks = []
        remaining_tasks = tasks.copy()
        
        while remaining_tasks:
            # العثور على مهمة بدون تبعيات
            for task in remaining_tasks:
                dependencies_met = all(
                    dep_id in [t.id for t in sorted_tasks] 
                    for dep_id in task.dependencies
                )
                
                if dependencies_met:
                    sorted_tasks.append(task)
                    remaining_tasks.remove(task)
                    break
            else:
                # إذا لم نجد مهمة بدون تبعيات، أضف الأولى
                sorted_tasks.append(remaining_tasks.pop(0))
        
        return sorted_tasks
    
    def _find_next_available_slot(self, start_time: datetime, 
                                 duration_minutes: int,
                                 machine: str = "",
                                 worker: str = "") -> datetime:
        """العثور على أقرب فترة متاحة"""
        
        current_time = start_time
        
        while True:
            # التحقق من ساعات العمل
            if self._is_working_time(current_time):
                # التحقق من توفر الآلة والعامل
                if self._is_resource_available(current_time, duration_minutes, machine, worker):
                    return current_time
            
            # الانتقال للساعة التالية
            current_time += timedelta(hours=1)
    
    def _is_working_time(self, time: datetime) -> bool:
        """فحص ما إذا كان الوقت ضمن ساعات العمل"""
        # فحص اليوم
        if time.weekday() not in self.working_hours['working_days']:
            return False
        
        # فحص الساعة
        hour = time.hour
        if hour < self.working_hours['start'] or hour >= self.working_hours['end']:
            return False
        
        return True
    
    def _is_resource_available(self, start_time: datetime, 
                              duration_minutes: int,
                              machine: str = "",
                              worker: str = "") -> bool:
        """فحص توفر الموارد"""
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        # فحص تضارب المهام الأخرى
        for task in self.tasks.values():
            if (task.status == TaskStatus.IN_PROGRESS and
                task.start_date and task.end_date):
                
                # فحص تضارب الوقت
                if (start_time < task.end_date and end_time > task.start_date):
                    # فحص تضارب الموارد
                    if (machine and task.machine_required == machine) or \
                       (worker and task.assigned_worker == worker):
                        return False
        
        return True
    
    def _add_working_time(self, start_time: datetime, 
                         duration_minutes: int) -> datetime:
        """إضافة وقت عمل مع مراعاة ساعات العمل"""
        
        current_time = start_time
        remaining_minutes = duration_minutes
        
        while remaining_minutes > 0:
            if self._is_working_time(current_time):
                # حساب الدقائق المتاحة في هذه الساعة
                minutes_in_hour = min(60, remaining_minutes)
                current_time += timedelta(minutes=minutes_in_hour)
                remaining_minutes -= minutes_in_hour
            else:
                # الانتقال لساعة العمل التالية
                current_time = self._next_working_hour(current_time)
        
        return current_time
    
    def _next_working_hour(self, time: datetime) -> datetime:
        """العثور على ساعة العمل التالية"""
        next_time = time.replace(minute=0, second=0, microsecond=0)
        
        while not self._is_working_time(next_time):
            next_time += timedelta(hours=1)
        
        return next_time
    
    def get_project_timeline(self, project_id: str) -> Dict[str, Any]:
        """الحصول على الجدول الزمني للمشروع"""
        project_tasks = [task for task in self.tasks.values() 
                        if task.project_id == project_id]
        
        if not project_tasks:
            return {}
        
        # حساب التواريخ
        start_dates = [task.start_date for task in project_tasks if task.start_date]
        end_dates = [task.end_date for task in project_tasks if task.end_date]
        
        project_start = min(start_dates) if start_dates else None
        project_end = max(end_dates) if end_dates else None
        
        # حساب الإحصائيات
        total_estimated = sum(task.estimated_duration for task in project_tasks)
        completed_tasks = [task for task in project_tasks 
                          if task.status == TaskStatus.COMPLETED]
        total_actual = sum(task.actual_duration for task in completed_tasks)
        
        return {
            'project_id': project_id,
            'start_date': project_start,
            'end_date': project_end,
            'total_tasks': len(project_tasks),
            'completed_tasks': len(completed_tasks),
            'estimated_duration': total_estimated,
            'actual_duration': total_actual,
            'progress': len(completed_tasks) / len(project_tasks) if project_tasks else 0,
            'tasks': project_tasks
        }
    
    def update_task_status(self, task_id: str, new_status: TaskStatus,
                          actual_duration: int = None) -> bool:
        """تحديث حالة المهمة"""
        try:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            old_status = task.status
            task.status = new_status
            
            # تحديث التواريخ الفعلية
            now = datetime.now()
            
            if new_status == TaskStatus.IN_PROGRESS and old_status == TaskStatus.PENDING:
                task.actual_start = now
            elif new_status == TaskStatus.COMPLETED:
                task.actual_end = now
                if actual_duration:
                    task.actual_duration = actual_duration
                elif task.actual_start:
                    duration = (now - task.actual_start).total_seconds() / 60
                    task.actual_duration = int(duration)
            
            return True
        except Exception as e:
            print(f"خطأ في تحديث حالة المهمة: {e}")
            return False
    
    def get_daily_schedule(self, date: datetime) -> List[ProductionTask]:
        """الحصول على جدول يوم معين"""
        daily_tasks = []
        
        for task in self.tasks.values():
            if (task.start_date and task.end_date and
                task.start_date.date() <= date.date() <= task.end_date.date()):
                daily_tasks.append(task)
        
        # ترتيب حسب وقت البدء
        daily_tasks.sort(key=lambda t: t.start_date)
        
        return daily_tasks
    
    def generate_task_id(self) -> str:
        """توليد معرف مهمة جديد"""
        return f"TASK_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.tasks)}"


def test_production_scheduler():
    """اختبار مجدول الإنتاج"""
    scheduler = ProductionScheduler()
    
    # مكونات تجريبية
    components = [
        {
            'id': 'comp1',
            'name': 'سطح المكتب',
            'project_id': 'PROJECT_001',
            'dimensions': {'length': 1200, 'width': 600, 'thickness': 25},
            'drilling': [{'x': 100, 'y': 100}]
        },
        {
            'id': 'comp2',
            'name': 'ساق المكتب',
            'project_id': 'PROJECT_001',
            'dimensions': {'length': 720, 'width': 500, 'thickness': 25}
        }
    ]
    
    # جدولة المشروع
    start_date = datetime.now() + timedelta(days=1)
    tasks = scheduler.schedule_project(components, start_date, Priority.HIGH)
    
    print(f"تم جدولة {len(tasks)} مهمة")
    
    for task in tasks:
        print(f"- {task.name}: {task.start_date} إلى {task.end_date}")
    
    # الحصول على الجدول الزمني
    timeline = scheduler.get_project_timeline('PROJECT_001')
    print(f"\nالجدول الزمني للمشروع:")
    print(f"البداية: {timeline['start_date']}")
    print(f"النهاية: {timeline['end_date']}")
    print(f"المدة المقدرة: {timeline['estimated_duration']} دقيقة")


if __name__ == "__main__":
    test_production_scheduler()
