#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Export Module
وحدة تصدير جدول القطع إلى PDF
"""

import os
from datetime import datetime
from typing import List, Dict, Any

try:
    from reportlab.lib.pagesizes import A4, landscape
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import mm, inch
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False


class PDFExporter:
    """فئة تصدير البيانات إلى PDF"""
    
    def __init__(self):
        self.doc = None
        self.story = []
        self.styles = None
        
    def export_cutlist(self, components_data: List[Dict[str, Any]], 
                      file_path: str, project_info: Dict[str, Any] = None) -> bool:
        """
        تصدير جدول القطع إلى ملف PDF
        
        Args:
            components_data: بيانات المكونات
            file_path: مسار الملف للحفظ
            project_info: معلومات المشروع
            
        Returns:
            True إذا تم التصدير بنجاح، False في حالة الخطأ
        """
        if not REPORTLAB_AVAILABLE:
            raise ImportError("مكتبة reportlab غير مثبتة. يرجى تثبيتها باستخدام: pip install reportlab")
        
        try:
            # إعداد المستند
            self._setup_document(file_path)
            
            # إعداد الأنماط
            self._setup_styles()
            
            # إضافة معلومات المشروع
            self._add_project_info(project_info or {})
            
            # إضافة جدول البيانات
            self._add_data_table(components_data)
            
            # إضافة الإحصائيات
            self._add_statistics(components_data)
            
            # بناء المستند
            self.doc.build(self.story)
            return True
            
        except Exception as e:
            print(f"خطأ في تصدير PDF: {str(e)}")
            return False
    
    def _setup_document(self, file_path: str):
        """إعداد المستند"""
        # استخدام الاتجاه الأفقي للصفحة لاستيعاب الجدول
        self.doc = SimpleDocTemplate(
            file_path,
            pagesize=landscape(A4),
            rightMargin=20*mm,
            leftMargin=20*mm,
            topMargin=20*mm,
            bottomMargin=20*mm
        )
        
        self.story = []
    
    def _setup_styles(self):
        """إعداد أنماط النص"""
        self.styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        self.styles.add(ParagraphStyle(
            name='ArabicTitle',
            parent=self.styles['Title'],
            fontSize=18,
            spaceAfter=12,
            alignment=1,  # وسط
            textColor=colors.HexColor('#2F5597')
        ))
        
        # نمط العنوان الفرعي
        self.styles.add(ParagraphStyle(
            name='ArabicSubtitle',
            parent=self.styles['Normal'],
            fontSize=14,
            spaceAfter=8,
            alignment=1,  # وسط
            textColor=colors.HexColor('#366092')
        ))
        
        # نمط النص العادي
        self.styles.add(ParagraphStyle(
            name='ArabicNormal',
            parent=self.styles['Normal'],
            fontSize=10,
            spaceAfter=6,
            alignment=2,  # يمين
        ))
        
        # نمط الإحصائيات
        self.styles.add(ParagraphStyle(
            name='ArabicStats',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=4,
            alignment=2,  # يمين
            textColor=colors.HexColor('#2F5597')
        ))
    
    def _format_arabic_text(self, text: str) -> str:
        """تنسيق النص العربي للعرض الصحيح"""
        if not ARABIC_SUPPORT:
            return text
        
        try:
            # إعادة تشكيل النص العربي
            reshaped_text = arabic_reshaper.reshape(text)
            # تطبيق خوارزمية الاتجاه الثنائي
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return text
    
    def _add_project_info(self, project_info: Dict[str, Any]):
        """إضافة معلومات المشروع"""
        # العنوان الرئيسي
        title = project_info.get('title', 'جدول القطع - Cut List')
        title_formatted = self._format_arabic_text(title)
        self.story.append(Paragraph(title_formatted, self.styles['ArabicTitle']))
        
        # تاريخ الإنشاء
        date_text = f"تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        date_formatted = self._format_arabic_text(date_text)
        self.story.append(Paragraph(date_formatted, self.styles['ArabicNormal']))
        
        # اسم المشروع
        if 'project_name' in project_info:
            project_name = f"اسم المشروع: {project_info['project_name']}"
            project_name_formatted = self._format_arabic_text(project_name)
            self.story.append(Paragraph(project_name_formatted, self.styles['ArabicSubtitle']))
        
        # وصف المشروع
        if 'description' in project_info:
            description = f"الوصف: {project_info['description']}"
            description_formatted = self._format_arabic_text(description)
            self.story.append(Paragraph(description_formatted, self.styles['ArabicNormal']))
        
        self.story.append(Spacer(1, 20))
    
    def _add_data_table(self, components_data: List[Dict[str, Any]]):
        """إضافة جدول البيانات"""
        # إعداد رؤوس الجدول
        headers = [
            'رقم',
            'اسم المكون',
            'الطول\n(مم)',
            'العرض\n(مم)',
            'السماكة\n(مم)',
            'المادة',
            'الكمية',
            'الحجم\n(سم³)',
            'الوزن\n(كغ)',
            'التكلفة\n(ريال)'
        ]
        
        # تنسيق رؤوس الجدول
        formatted_headers = [self._format_arabic_text(header) for header in headers]
        
        # إعداد بيانات الجدول
        table_data = [formatted_headers]
        
        for idx, component in enumerate(components_data, 1):
            dimensions = component.get('dimensions', {})
            volume = component.get('volume', 0) / 1000  # تحويل إلى سم³
            
            row = [
                str(idx),
                self._format_arabic_text(component.get('name', '')),
                f"{dimensions.get('length', 0):.0f}",
                f"{dimensions.get('width', 0):.0f}",
                f"{dimensions.get('thickness', 0):.0f}",
                self._format_arabic_text(component.get('material', 'خشب')),
                str(component.get('quantity', 1)),
                f"{volume:.2f}",
                f"{component.get('weight', 0):.2f}",
                f"{component.get('cost', 0):.2f}"
            ]
            table_data.append(row)
        
        # إنشاء الجدول
        table = Table(table_data, repeatRows=1)
        
        # تنسيق الجدول
        table.setStyle(TableStyle([
            # تنسيق الرؤوس
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#366092')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            
            # تنسيق البيانات
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('ALIGN', (0, 1), (0, -1), 'CENTER'),  # رقم المكون
            ('ALIGN', (1, 1), (1, -1), 'RIGHT'),   # اسم المكون
            ('ALIGN', (2, 1), (-1, -1), 'CENTER'), # باقي الأعمدة
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            
            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            
            # تلوين الصفوف بالتناوب
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
        ]))
        
        self.story.append(table)
        self.story.append(Spacer(1, 20))
    
    def _add_statistics(self, components_data: List[Dict[str, Any]]):
        """إضافة الإحصائيات"""
        # عنوان الإحصائيات
        stats_title = self._format_arabic_text("الإحصائيات:")
        self.story.append(Paragraph(stats_title, self.styles['ArabicSubtitle']))
        
        # حساب الإحصائيات
        total_components = len(components_data)
        total_quantity = sum(comp.get('quantity', 1) for comp in components_data)
        total_volume = sum(comp.get('volume', 0) for comp in components_data) / 1000  # سم³
        total_weight = sum(comp.get('weight', 0) for comp in components_data)
        total_cost = sum(comp.get('cost', 0) for comp in components_data)
        
        # إعداد الإحصائيات
        stats = [
            f"إجمالي أنواع المكونات: {total_components}",
            f"إجمالي عدد القطع: {total_quantity}",
            f"إجمالي الحجم: {total_volume:.2f} سم³",
            f"إجمالي الوزن المقدر: {total_weight:.2f} كغ",
            f"إجمالي التكلفة المقدرة: {total_cost:.2f} ريال"
        ]
        
        # إضافة الإحصائيات
        for stat in stats:
            stat_formatted = self._format_arabic_text(stat)
            self.story.append(Paragraph(stat_formatted, self.styles['ArabicStats']))
        
        # إضافة معلومات المواد
        materials = set(comp.get('material', 'خشب') for comp in components_data)
        materials_text = f"المواد المستخدمة: {', '.join(materials)}"
        materials_formatted = self._format_arabic_text(materials_text)
        
        self.story.append(Spacer(1, 10))
        self.story.append(Paragraph(materials_formatted, self.styles['ArabicStats']))
        
        # إضافة ملاحظة في النهاية
        footer_text = self._format_arabic_text("تم إنشاء هذا التقرير بواسطة تطبيق CutList Desktop App")
        self.story.append(Spacer(1, 20))
        self.story.append(Paragraph(footer_text, self.styles['ArabicNormal']))


def export_to_pdf(components_data: List[Dict[str, Any]], 
                 file_path: str, 
                 project_info: Dict[str, Any] = None) -> bool:
    """
    دالة مساعدة لتصدير البيانات إلى PDF
    
    Args:
        components_data: بيانات المكونات
        file_path: مسار الملف
        project_info: معلومات المشروع
        
    Returns:
        True إذا تم التصدير بنجاح
    """
    exporter = PDFExporter()
    return exporter.export_cutlist(components_data, file_path, project_info)


def test_pdf_export():
    """اختبار تصدير PDF"""
    # بيانات تجريبية
    test_data = [
        {
            'name': 'لوح علوي',
            'dimensions': {'length': 1200, 'width': 600, 'thickness': 18},
            'material': 'خشب',
            'quantity': 1,
            'volume': 12960000,  # مم³
            'weight': 7.78,  # كغ
            'cost': 65.0,  # ريال
        },
        {
            'name': 'لوح جانبي',
            'dimensions': {'length': 800, 'width': 400, 'thickness': 18},
            'material': 'خشب',
            'quantity': 2,
            'volume': 5760000,  # مم³
            'weight': 3.46,  # كغ
            'cost': 28.8,  # ريال
        }
    ]
    
    project_info = {
        'title': 'جدول قطع طاولة المكتب',
        'project_name': 'طاولة مكتب خشبية',
        'description': 'طاولة مكتب بسيطة من الخشب الطبيعي'
    }
    
    success = export_to_pdf(test_data, 'test_cutlist.pdf', project_info)
    if success:
        print("تم تصدير الملف بنجاح: test_cutlist.pdf")
    else:
        print("فشل في تصدير الملف")


if __name__ == "__main__":
    test_pdf_export()
