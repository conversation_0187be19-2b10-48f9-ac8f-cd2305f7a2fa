#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cutting Optimizer
محسن خطة القطع لتقليل الهدر
"""

import math
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum


class CutDirection(Enum):
    """اتجاه القطع"""
    HORIZONTAL = "أفقي"
    VERTICAL = "عمودي"


@dataclass
class CutPiece:
    """قطعة مطلوبة للقطع"""
    id: str
    name: str
    length: float
    width: float
    thickness: float
    material: str
    quantity: int
    grain_direction: str = "أي اتجاه"  # اتجاه الألياف
    edge_banding: List[str] = None
    priority: int = 1  # أولوية القطع (1 = عالية)
    
    def __post_init__(self):
        if self.edge_banding is None:
            self.edge_banding = []


@dataclass
class Sheet:
    """لوح خام للقطع"""
    id: str
    length: float
    width: float
    thickness: float
    material: str
    cost: float
    available_quantity: int = 1
    grain_direction: str = "طولي"


@dataclass
class CutLayout:
    """تخطيط القطع على لوح"""
    sheet: Sheet
    pieces: List[Dict[str, Any]]  # القطع المرتبة
    efficiency: float  # كفاءة الاستغلال
    waste_area: float  # مساحة الهدر
    cuts_required: List[Dict[str, Any]]  # القطعات المطلوبة


class CuttingOptimizer:
    """محسن خطة القطع"""
    
    def __init__(self):
        self.kerf_width = 3.0  # عرض القطع بالمليمتر
        self.edge_margin = 5.0  # هامش الحافة
        self.min_waste_size = 100.0  # أصغر حجم هدر مفيد
    
    def optimize_cutting_plan(self, pieces: List[CutPiece], sheets: List[Sheet]) -> List[CutLayout]:
        """تحسين خطة القطع"""
        # تجميع القطع حسب المادة والسماكة
        grouped_pieces = self._group_pieces_by_material_thickness(pieces)
        
        cutting_plans = []
        
        for (material, thickness), piece_group in grouped_pieces.items():
            # العثور على الألواح المناسبة
            suitable_sheets = [s for s in sheets 
                             if s.material == material and s.thickness == thickness]
            
            if not suitable_sheets:
                continue
            
            # ترتيب القطع حسب الحجم (الأكبر أولاً)
            sorted_pieces = sorted(piece_group, 
                                 key=lambda p: p.length * p.width, 
                                 reverse=True)
            
            # تحسين التخطيط
            layouts = self._optimize_layouts(sorted_pieces, suitable_sheets)
            cutting_plans.extend(layouts)
        
        return cutting_plans
    
    def _group_pieces_by_material_thickness(self, pieces: List[CutPiece]) -> Dict[Tuple[str, float], List[CutPiece]]:
        """تجميع القطع حسب المادة والسماكة"""
        groups = {}
        
        for piece in pieces:
            # توسيع القطع حسب الكمية
            for _ in range(piece.quantity):
                key = (piece.material, piece.thickness)
                if key not in groups:
                    groups[key] = []
                groups[key].append(piece)
        
        return groups
    
    def _optimize_layouts(self, pieces: List[CutPiece], sheets: List[Sheet]) -> List[CutLayout]:
        """تحسين تخطيط القطع على الألواح"""
        layouts = []
        remaining_pieces = pieces.copy()
        
        while remaining_pieces:
            best_layout = None
            best_efficiency = 0
            
            # جرب كل لوح متاح
            for sheet in sheets:
                if sheet.available_quantity <= 0:
                    continue
                
                layout = self._create_layout(remaining_pieces, sheet)
                if layout and layout.efficiency > best_efficiency:
                    best_layout = layout
                    best_efficiency = layout.efficiency
            
            if best_layout:
                layouts.append(best_layout)
                
                # إزالة القطع المستخدمة
                used_piece_ids = [p['piece'].id for p in best_layout.pieces]
                remaining_pieces = [p for p in remaining_pieces 
                                  if p.id not in used_piece_ids]
                
                # تقليل كمية اللوح المتاحة
                best_layout.sheet.available_quantity -= 1
            else:
                # لا يمكن وضع المزيد من القطع
                break
        
        return layouts
    
    def _create_layout(self, pieces: List[CutPiece], sheet: Sheet) -> Optional[CutLayout]:
        """إنشاء تخطيط قطع على لوح واحد"""
        # استخدام خوارزمية Bottom-Left Fill
        placed_pieces = []
        occupied_areas = []
        
        available_width = sheet.width - 2 * self.edge_margin
        available_length = sheet.length - 2 * self.edge_margin
        
        for piece in pieces:
            # جرب وضع القطعة في اتجاهات مختلفة
            orientations = [(piece.length, piece.width), (piece.width, piece.length)]
            
            placed = False
            for length, width in orientations:
                if length > available_length or width > available_width:
                    continue
                
                # العثور على أفضل موضع
                position = self._find_best_position(length, width, occupied_areas, 
                                                  available_length, available_width)
                
                if position:
                    x, y = position
                    placed_pieces.append({
                        'piece': piece,
                        'x': x + self.edge_margin,
                        'y': y + self.edge_margin,
                        'length': length,
                        'width': width,
                        'rotated': length != piece.length
                    })
                    
                    # إضافة المنطقة المشغولة
                    occupied_areas.append({
                        'x': x,
                        'y': y,
                        'length': length + self.kerf_width,
                        'width': width + self.kerf_width
                    })
                    
                    placed = True
                    break
            
            if not placed:
                break
        
        if not placed_pieces:
            return None
        
        # حساب الكفاءة
        used_area = sum(p['length'] * p['width'] for p in placed_pieces)
        total_area = sheet.length * sheet.width
        efficiency = used_area / total_area
        waste_area = total_area - used_area
        
        # توليد قائمة القطعات المطلوبة
        cuts_required = self._generate_cut_list(placed_pieces, sheet)
        
        return CutLayout(
            sheet=sheet,
            pieces=placed_pieces,
            efficiency=efficiency,
            waste_area=waste_area,
            cuts_required=cuts_required
        )
    
    def _find_best_position(self, length: float, width: float, 
                           occupied_areas: List[Dict], 
                           max_length: float, max_width: float) -> Optional[Tuple[float, float]]:
        """العثور على أفضل موضع للقطعة"""
        # جرب المواضع من الأسفل إلى الأعلى، من اليسار إلى اليمين
        step = 10  # خطوة البحث بالمليمتر
        
        for y in range(0, int(max_width - width) + 1, step):
            for x in range(0, int(max_length - length) + 1, step):
                # تحقق من عدم التداخل
                if not self._check_overlap(x, y, length, width, occupied_areas):
                    return (float(x), float(y))
        
        return None
    
    def _check_overlap(self, x: float, y: float, length: float, width: float,
                      occupied_areas: List[Dict]) -> bool:
        """فحص التداخل مع المناطق المشغولة"""
        for area in occupied_areas:
            if (x < area['x'] + area['length'] and
                x + length > area['x'] and
                y < area['y'] + area['width'] and
                y + width > area['y']):
                return True
        return False
    
    def _generate_cut_list(self, placed_pieces: List[Dict], sheet: Sheet) -> List[Dict[str, Any]]:
        """توليد قائمة القطعات المطلوبة"""
        cuts = []
        
        # ترتيب القطع للحصول على أفضل تسلسل قطع
        sorted_pieces = sorted(placed_pieces, key=lambda p: (p['y'], p['x']))
        
        # توليد القطعات الأفقية والعمودية
        horizontal_cuts = set()
        vertical_cuts = set()
        
        for piece in sorted_pieces:
            # قطعات أفقية
            horizontal_cuts.add(piece['y'])
            horizontal_cuts.add(piece['y'] + piece['width'])
            
            # قطعات عمودية
            vertical_cuts.add(piece['x'])
            vertical_cuts.add(piece['x'] + piece['length'])
        
        # إضافة حدود اللوح
        horizontal_cuts.add(0)
        horizontal_cuts.add(sheet.width)
        vertical_cuts.add(0)
        vertical_cuts.add(sheet.length)
        
        # تحويل إلى قائمة مرتبة
        h_cuts = sorted(list(horizontal_cuts))
        v_cuts = sorted(list(vertical_cuts))
        
        # إنشاء تعليمات القطع
        cut_sequence = []
        
        # القطعات الأفقية أولاً
        for i, y in enumerate(h_cuts[1:-1], 1):
            cut_sequence.append({
                'type': 'horizontal',
                'position': y,
                'from': 0,
                'to': sheet.length,
                'order': i
            })
        
        # ثم القطعات العمودية
        for i, x in enumerate(v_cuts[1:-1], len(h_cuts)):
            cut_sequence.append({
                'type': 'vertical',
                'position': x,
                'from': 0,
                'to': sheet.width,
                'order': i
            })
        
        return cut_sequence
    
    def calculate_material_requirements(self, pieces: List[CutPiece], 
                                      sheets: List[Sheet]) -> Dict[str, Any]:
        """حساب متطلبات المواد"""
        layouts = self.optimize_cutting_plan(pieces, sheets)
        
        requirements = {}
        total_cost = 0
        total_waste = 0
        
        for layout in layouts:
            sheet_key = f"{layout.sheet.material}_{layout.sheet.thickness}mm"
            
            if sheet_key not in requirements:
                requirements[sheet_key] = {
                    'sheets_needed': 0,
                    'total_cost': 0,
                    'total_waste': 0,
                    'efficiency': []
                }
            
            requirements[sheet_key]['sheets_needed'] += 1
            requirements[sheet_key]['total_cost'] += layout.sheet.cost
            requirements[sheet_key]['total_waste'] += layout.waste_area
            requirements[sheet_key]['efficiency'].append(layout.efficiency)
            
            total_cost += layout.sheet.cost
            total_waste += layout.waste_area
        
        # حساب متوسط الكفاءة
        for req in requirements.values():
            if req['efficiency']:
                req['average_efficiency'] = sum(req['efficiency']) / len(req['efficiency'])
            else:
                req['average_efficiency'] = 0
        
        return {
            'by_material': requirements,
            'total_cost': total_cost,
            'total_waste_area': total_waste,
            'total_layouts': len(layouts)
        }


def test_cutting_optimizer():
    """اختبار محسن القطع"""
    optimizer = CuttingOptimizer()
    
    # قطع تجريبية
    pieces = [
        CutPiece("P1", "لوح علوي", 1200, 600, 18, "خشب", 1),
        CutPiece("P2", "لوح جانبي", 800, 400, 18, "خشب", 2),
        CutPiece("P3", "رف", 1100, 300, 18, "خشب", 3),
        CutPiece("P4", "ظهر", 1180, 780, 6, "خشب رقائقي", 1),
    ]
    
    # ألواح متاحة
    sheets = [
        Sheet("S1", 2440, 1220, 18, "خشب", 150, 5),
        Sheet("S2", 2440, 1220, 6, "خشب رقائقي", 80, 3),
    ]
    
    # تحسين القطع
    layouts = optimizer.optimize_cutting_plan(pieces, sheets)
    
    print(f"عدد التخطيطات: {len(layouts)}")
    for i, layout in enumerate(layouts, 1):
        print(f"\nتخطيط {i}:")
        print(f"  اللوح: {layout.sheet.material} {layout.sheet.thickness}mm")
        print(f"  الكفاءة: {layout.efficiency:.2%}")
        print(f"  القطع: {len(layout.pieces)}")
        print(f"  الهدر: {layout.waste_area:.0f} مم²")
    
    # متطلبات المواد
    requirements = optimizer.calculate_material_requirements(pieces, sheets)
    print(f"\nمتطلبات المواد:")
    print(f"التكلفة الإجمالية: {requirements['total_cost']:.2f}")
    print(f"إجمالي الهدر: {requirements['total_waste_area']:.0f} مم²")


if __name__ == "__main__":
    test_cutting_optimizer()
