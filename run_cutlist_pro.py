#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CutList Pro - Launcher Script
سكريبت تشغيل تطبيق مصمم الأثاث الاحترافي
"""

import sys
import os
import subprocess
from pathlib import Path

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")
    
    # فحص Python
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # فحص المكتبات المطلوبة
    required_packages = [
        'PyQt5',
        'sqlite3',
        'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sqlite3':
                import sqlite3
            elif package == 'PyQt5':
                import PyQt5
            elif package == 'openpyxl':
                import openpyxl
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - غير مثبت")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  المكتبات المفقودة: {', '.join(missing_packages)}")
        print("هل تريد تثبيت المكتبات المفقودة؟ (y/n): ", end="")
        
        response = input().lower()
        if response in ['y', 'yes', 'نعم']:
            install_packages(missing_packages)
        else:
            return False
    
    return True

def install_packages(packages):
    """تثبيت المكتبات المفقودة"""
    print("\n📦 تثبيت المكتبات المفقودة...")
    
    for package in packages:
        if package == 'sqlite3':
            continue  # sqlite3 مدمج مع Python
        
        print(f"تثبيت {package}...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"❌ فشل في تثبيت {package}")
            return False
    
    print("✅ تم تثبيت جميع المكتبات بنجاح!")
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🪑 CutList Pro - مصمم الأثاث الاحترافي")
    print("   تطبيق شامل لتصميم الأثاث وتحسين القطع")
    print("=" * 60)
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ لا يمكن تشغيل التطبيق بسبب متطلبات مفقودة")
        input("اضغط Enter للخروج...")
        return
    
    print("\n🚀 تشغيل التطبيق...")
    
    # التحقق من وجود الملف الرئيسي
    main_file = Path("cutlist_pro_real.py")
    if not main_file.exists():
        print("❌ خطأ: لم يتم العثور على ملف التطبيق الرئيسي")
        input("اضغط Enter للخروج...")
        return
    
    try:
        # تشغيل التطبيق
        from cutlist_pro_real import main as run_app
        run_app()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
