@echo off
echo ========================================
echo    CutList Desktop App
echo    تطبيق جدول القطع
echo ========================================
echo.

echo جاري التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

echo جاري التحقق من PyQt5...
python -c "import PyQt5" >nul 2>&1
if errorlevel 1 (
    echo PyQt5 غير مثبت. جاري التثبيت...
    pip install PyQt5
    if errorlevel 1 (
        echo فشل في تثبيت PyQt5
        pause
        exit /b 1
    )
)

echo جاري تشغيل التطبيق...
echo.
python main_simple.py

if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل التطبيق
    pause
)
