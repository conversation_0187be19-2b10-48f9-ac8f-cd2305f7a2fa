#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CutList Desktop App
تطبيق سطح مكتب لتوليد جدول القطع من النماذج ثلاثية الأبعاد
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QTableWidget, QTableWidgetItem,
                             QFileDialog, QMessageBox, QLabel, QHeaderView)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), 'models'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'cutlist'))

from models.model_reader import ModelReader
from cutlist.cutlist_generator import CutListGenerator


class ModelAnalysisThread(QThread):
    """خيط منفصل لتحليل النموذج ثلاثي الأبعاد"""
    analysis_complete = pyqtSignal(list)
    analysis_error = pyqtSignal(str)
    
    def __init__(self, file_path):
        super().__init__()
        self.file_path = file_path
    
    def run(self):
        try:
            reader = ModelReader()
            components = reader.analyze_model(self.file_path)
            self.analysis_complete.emit(components)
        except Exception as e:
            self.analysis_error.emit(str(e))


class CutListMainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.components_data = []
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("CutList Desktop App - تطبيق جدول القطع")
        self.setGeometry(100, 100, 1000, 600)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # شريط الأزرار العلوي
        button_layout = QHBoxLayout()
        
        # زر فتح الملف
        self.open_file_btn = QPushButton("فتح ملف ثلاثي الأبعاد")
        self.open_file_btn.setFont(QFont("Arial", 12))
        self.open_file_btn.clicked.connect(self.open_file)
        button_layout.addWidget(self.open_file_btn)
        
        # زر تصدير إلى Excel
        self.export_excel_btn = QPushButton("تصدير إلى Excel")
        self.export_excel_btn.setFont(QFont("Arial", 12))
        self.export_excel_btn.setEnabled(False)
        button_layout.addWidget(self.export_excel_btn)
        
        # زر تصدير إلى PDF
        self.export_pdf_btn = QPushButton("تصدير إلى PDF")
        self.export_pdf_btn.setFont(QFont("Arial", 12))
        self.export_pdf_btn.setEnabled(False)
        button_layout.addWidget(self.export_pdf_btn)
        
        button_layout.addStretch()
        main_layout.addLayout(button_layout)
        
        # تسمية الجدول
        self.table_label = QLabel("جدول القطع - Cut List")
        self.table_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.table_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.table_label)
        
        # جدول النتائج
        self.results_table = QTableWidget()
        self.setup_table()
        main_layout.addWidget(self.results_table)
        
        # شريط الحالة
        self.status_label = QLabel("جاهز - اختر ملف ثلاثي الأبعاد للبدء")
        self.status_label.setFont(QFont("Arial", 10))
        main_layout.addWidget(self.status_label)
        
    def setup_table(self):
        """إعداد جدول النتائج"""
        # تحديد الأعمدة
        headers = ["اسم المكون", "الطول (مم)", "العرض (مم)", "السماكة (مم)", "نوع المادة", "الكمية"]
        self.results_table.setColumnCount(len(headers))
        self.results_table.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # تنسيق الخط
        font = QFont("Arial", 10)
        self.results_table.setFont(font)
        
    def open_file(self):
        """فتح ملف ثلاثي الأبعاد"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف ثلاثي الأبعاد",
            "",
            "3D Files (*.obj *.stl *.dae);;OBJ Files (*.obj);;STL Files (*.stl);;DAE Files (*.dae)"
        )
        
        if file_path:
            self.status_label.setText(f"جاري تحليل الملف: {os.path.basename(file_path)}")
            self.open_file_btn.setEnabled(False)
            
            # بدء تحليل النموذج في خيط منفصل
            self.analysis_thread = ModelAnalysisThread(file_path)
            self.analysis_thread.analysis_complete.connect(self.on_analysis_complete)
            self.analysis_thread.analysis_error.connect(self.on_analysis_error)
            self.analysis_thread.start()
    
    def on_analysis_complete(self, components):
        """عند اكتمال تحليل النموذج"""
        self.components_data = components
        self.populate_table(components)
        self.status_label.setText(f"تم تحليل النموذج بنجاح - تم العثور على {len(components)} مكون")
        self.open_file_btn.setEnabled(True)
        self.export_excel_btn.setEnabled(True)
        self.export_pdf_btn.setEnabled(True)
    
    def on_analysis_error(self, error_message):
        """عند حدوث خطأ في التحليل"""
        QMessageBox.critical(self, "خطأ في التحليل", f"حدث خطأ أثناء تحليل الملف:\n{error_message}")
        self.status_label.setText("جاهز - اختر ملف ثلاثي الأبعاد للبدء")
        self.open_file_btn.setEnabled(True)
    
    def populate_table(self, components):
        """ملء الجدول بالبيانات"""
        self.results_table.setRowCount(len(components))
        
        for row, component in enumerate(components):
            # اسم المكون
            self.results_table.setItem(row, 0, QTableWidgetItem(component.get('name', f'مكون {row + 1}')))
            
            # الأبعاد
            dimensions = component.get('dimensions', {})
            self.results_table.setItem(row, 1, QTableWidgetItem(f"{dimensions.get('length', 0):.2f}"))
            self.results_table.setItem(row, 2, QTableWidgetItem(f"{dimensions.get('width', 0):.2f}"))
            self.results_table.setItem(row, 3, QTableWidgetItem(f"{dimensions.get('thickness', 0):.2f}"))
            
            # نوع المادة (افتراضي)
            self.results_table.setItem(row, 4, QTableWidgetItem("خشب"))
            
            # الكمية
            self.results_table.setItem(row, 5, QTableWidgetItem("1"))


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط الافتراضي لدعم العربية
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = CutListMainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
