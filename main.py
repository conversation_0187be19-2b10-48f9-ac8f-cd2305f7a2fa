#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CutList Desktop App
تطبيق سطح مكتب لتوليد جدول القطع من النماذج ثلاثية الأبعاد
"""

import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QPushButton, QTableWidget, QTableWidgetItem,
                             QFileDialog, QMessageBox, QLabel, QHeaderView, QMenuBar,
                             QMenu, QAction, QStatusBar, QProgressBar, QSplitter,
                             QTextEdit, QTabWidget, QGroupBox, QFormLayout, QLineEdit,
                             QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox, QDialog,
                             QDialogButtonBox, Q<PERSON>ist<PERSON>idget, QListWidgetItem)
from PyQt5.QtCore import Qt, QThr<PERSON>, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QPixmap

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), 'models'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'cutlist'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'export'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'materials'))

try:
    from models.model_reader import ModelReader
    from cutlist.cutlist_generator import CutListGenerator
    from export.export_excel import export_to_excel
    from export.export_pdf import export_to_pdf
    from materials.material_manager import MaterialManager
    from project_manager import ProjectManager, CutListProject
    ADVANCED_FEATURES = True
except ImportError as e:
    print(f"تحذير: بعض الميزات المتقدمة غير متاحة: {e}")
    ADVANCED_FEATURES = False


class ModelAnalysisThread(QThread):
    """خيط منفصل لتحليل النموذج ثلاثي الأبعاد"""
    analysis_complete = pyqtSignal(list)
    analysis_error = pyqtSignal(str)

    def __init__(self, file_path):
        super().__init__()
        self.file_path = file_path

    def run(self):
        try:
            reader = ModelReader()
            components = reader.analyze_model(self.file_path)
            self.analysis_complete.emit(components)
        except Exception as e:
            self.analysis_error.emit(str(e))


class CutListMainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق المتقدم"""

    def __init__(self):
        super().__init__()
        self.components_data = []
        self.current_project = None
        self.current_file_path = None

        # تهيئة المدراء
        if ADVANCED_FEATURES:
            self.material_manager = MaterialManager()
            self.project_manager = ProjectManager()

        self.init_ui()
        self.setup_menu_bar()
        self.setup_status_bar()

    def init_ui(self):
        """إعداد واجهة المستخدم المتقدمة"""
        self.setWindowTitle("CutList Desktop App - تطبيق جدول القطع المتقدم")
        self.setGeometry(100, 100, 1400, 800)

        # الويدجت الرئيسي مع تقسيم
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)

        # إنشاء مقسم أفقي
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # الجانب الأيسر - الجدول والأدوات الرئيسية
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # شريط الأزرار العلوي
        self.setup_toolbar(left_layout)

        # جدول النتائج مع تبويبات
        self.setup_main_tabs(left_layout)

        # الجانب الأيمن - اللوحة الجانبية
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        self.setup_sidebar(right_layout)

        # إضافة الويدجتات للمقسم
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([1000, 400])  # تحديد النسب

    def setup_toolbar(self, layout):
        """إعداد شريط الأدوات"""
        toolbar_layout = QHBoxLayout()

        # زر فتح الملف
        self.open_file_btn = QPushButton("فتح ملف ثلاثي الأبعاد")
        self.open_file_btn.setFont(QFont("Arial", 10))
        self.open_file_btn.clicked.connect(self.open_file)
        toolbar_layout.addWidget(self.open_file_btn)

        # زر إضافة مكون
        self.add_component_btn = QPushButton("إضافة مكون")
        self.add_component_btn.setFont(QFont("Arial", 10))
        self.add_component_btn.clicked.connect(self.add_component_manually)
        toolbar_layout.addWidget(self.add_component_btn)

        # زر تصدير إلى Excel
        self.export_excel_btn = QPushButton("تصدير Excel")
        self.export_excel_btn.setFont(QFont("Arial", 10))
        self.export_excel_btn.setEnabled(False)
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        toolbar_layout.addWidget(self.export_excel_btn)

        # زر تصدير إلى PDF
        self.export_pdf_btn = QPushButton("تصدير PDF")
        self.export_pdf_btn.setFont(QFont("Arial", 10))
        self.export_pdf_btn.setEnabled(False)
        self.export_pdf_btn.clicked.connect(self.export_to_pdf)
        toolbar_layout.addWidget(self.export_pdf_btn)

        toolbar_layout.addStretch()
        layout.addLayout(toolbar_layout)

    def setup_main_tabs(self, layout):
        """إعداد التبويبات الرئيسية"""
        self.tab_widget = QTabWidget()

        # تبويب جدول القطع
        cutlist_widget = QWidget()
        cutlist_layout = QVBoxLayout(cutlist_widget)

        # تسمية الجدول
        self.table_label = QLabel("جدول القطع - Cut List")
        self.table_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.table_label.setAlignment(Qt.AlignCenter)
        cutlist_layout.addWidget(self.table_label)

        # جدول النتائج
        self.results_table = QTableWidget()
        self.setup_table()
        cutlist_layout.addWidget(self.results_table)

        self.tab_widget.addTab(cutlist_widget, "جدول القطع")

        # تبويب الإحصائيات
        stats_widget = QWidget()
        stats_layout = QVBoxLayout(stats_widget)

        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setFont(QFont("Arial", 10))
        stats_layout.addWidget(self.stats_text)

        self.tab_widget.addTab(stats_widget, "الإحصائيات")

        layout.addWidget(self.tab_widget)

    def setup_sidebar(self, layout):
        """إعداد اللوحة الجانبية"""
        # معلومات المشروع
        project_group = QGroupBox("معلومات المشروع")
        project_layout = QFormLayout(project_group)

        self.project_name_edit = QLineEdit()
        self.project_name_edit.setPlaceholderText("اسم المشروع")
        project_layout.addRow("الاسم:", self.project_name_edit)

        self.project_desc_edit = QTextEdit()
        self.project_desc_edit.setMaximumHeight(80)
        self.project_desc_edit.setPlaceholderText("وصف المشروع")
        project_layout.addRow("الوصف:", self.project_desc_edit)

        layout.addWidget(project_group)

        # إدارة المواد
        if ADVANCED_FEATURES:
            materials_group = QGroupBox("إدارة المواد")
            materials_layout = QVBoxLayout(materials_group)

            self.materials_list = QListWidget()
            self.update_materials_list()
            materials_layout.addWidget(self.materials_list)

            materials_btn_layout = QHBoxLayout()
            add_material_btn = QPushButton("إضافة مادة")
            add_material_btn.clicked.connect(self.add_material)
            edit_material_btn = QPushButton("تعديل")
            edit_material_btn.clicked.connect(self.edit_material)

            materials_btn_layout.addWidget(add_material_btn)
            materials_btn_layout.addWidget(edit_material_btn)
            materials_layout.addLayout(materials_btn_layout)

            layout.addWidget(materials_group)

        # المشاريع المحفوظة
        if ADVANCED_FEATURES:
            projects_group = QGroupBox("المشاريع المحفوظة")
            projects_layout = QVBoxLayout(projects_group)

            self.projects_list = QListWidget()
            self.update_projects_list()
            projects_layout.addWidget(self.projects_list)

            projects_btn_layout = QHBoxLayout()
            load_project_btn = QPushButton("تحميل")
            load_project_btn.clicked.connect(self.load_project)
            save_project_btn = QPushButton("حفظ")
            save_project_btn.clicked.connect(self.save_project)

            projects_btn_layout.addWidget(load_project_btn)
            projects_btn_layout.addWidget(save_project_btn)
            projects_layout.addLayout(projects_btn_layout)

            layout.addWidget(projects_group)

        layout.addStretch()

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu('ملف')

        # مشروع جديد
        new_action = QAction('مشروع جديد', self)
        new_action.setShortcut('Ctrl+N')
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)

        # فتح مشروع
        open_action = QAction('فتح مشروع', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_project_file)
        file_menu.addAction(open_action)

        # حفظ مشروع
        save_action = QAction('حفظ مشروع', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_project)
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        # تصدير
        export_menu = file_menu.addMenu('تصدير')

        excel_action = QAction('تصدير إلى Excel', self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        pdf_action = QAction('تصدير إلى PDF', self)
        pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(pdf_action)

        file_menu.addSeparator()

        # خروج
        exit_action = QAction('خروج', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة الأدوات
        tools_menu = menubar.addMenu('أدوات')

        materials_action = QAction('إدارة المواد', self)
        materials_action.triggered.connect(self.open_materials_manager)
        tools_menu.addAction(materials_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')

        about_action = QAction('حول التطبيق', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = self.statusBar()

        # تسمية الحالة
        self.status_label = QLabel("جاهز - اختر ملف ثلاثي الأبعاد للبدء")
        self.status_bar.addWidget(self.status_label)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)

    def setup_table(self):
        """إعداد جدول النتائج المتقدم"""
        # تحديد الأعمدة
        headers = [
            "اسم المكون", "الطول (مم)", "العرض (مم)", "السماكة (مم)",
            "نوع المادة", "الكمية", "الحجم (سم³)", "الوزن (كغ)", "التكلفة (ريال)"
        ]
        self.results_table.setColumnCount(len(headers))
        self.results_table.setHorizontalHeaderLabels(headers)

        # تنسيق الجدول
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.results_table.setEditTriggers(QTableWidget.DoubleClicked)

        # تنسيق الخط
        font = QFont("Arial", 10)
        self.results_table.setFont(font)

        # ربط الأحداث
        self.results_table.cellChanged.connect(self.on_cell_changed)

    def open_file(self):
        """فتح ملف ثلاثي الأبعاد"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف ثلاثي الأبعاد",
            "",
            "3D Files (*.obj *.stl *.dae);;OBJ Files (*.obj);;STL Files (*.stl);;DAE Files (*.dae)"
        )

        if file_path:
            self.status_label.setText(f"جاري تحليل الملف: {os.path.basename(file_path)}")
            self.open_file_btn.setEnabled(False)

            # بدء تحليل النموذج في خيط منفصل
            self.analysis_thread = ModelAnalysisThread(file_path)
            self.analysis_thread.analysis_complete.connect(self.on_analysis_complete)
            self.analysis_thread.analysis_error.connect(self.on_analysis_error)
            self.analysis_thread.start()

    def on_analysis_complete(self, components):
        """عند اكتمال تحليل النموذج"""
        self.components_data = components
        self.populate_table(components)
        self.status_label.setText(f"تم تحليل النموذج بنجاح - تم العثور على {len(components)} مكون")
        self.open_file_btn.setEnabled(True)
        self.export_excel_btn.setEnabled(True)
        self.export_pdf_btn.setEnabled(True)

    def on_analysis_error(self, error_message):
        """عند حدوث خطأ في التحليل"""
        QMessageBox.critical(self, "خطأ في التحليل", f"حدث خطأ أثناء تحليل الملف:\n{error_message}")
        self.status_label.setText("جاهز - اختر ملف ثلاثي الأبعاد للبدء")
        self.open_file_btn.setEnabled(True)

    def populate_table(self, components):
        """ملء الجدول بالبيانات المتقدمة"""
        self.results_table.setRowCount(len(components))

        for row, component in enumerate(components):
            # اسم المكون
            self.results_table.setItem(row, 0, QTableWidgetItem(component.get('name', f'مكون {row + 1}')))

            # الأبعاد
            dimensions = component.get('dimensions', {})
            self.results_table.setItem(row, 1, QTableWidgetItem(f"{dimensions.get('length', 0):.2f}"))
            self.results_table.setItem(row, 2, QTableWidgetItem(f"{dimensions.get('width', 0):.2f}"))
            self.results_table.setItem(row, 3, QTableWidgetItem(f"{dimensions.get('thickness', 0):.2f}"))

            # نوع المادة
            material = component.get('material', 'خشب')
            if ADVANCED_FEATURES and self.material_manager:
                suggested = self.material_manager.suggest_material(dimensions)
                material = suggested
            self.results_table.setItem(row, 4, QTableWidgetItem(material))

            # الكمية
            quantity = component.get('quantity', 1)
            self.results_table.setItem(row, 5, QTableWidgetItem(str(quantity)))

            # الحجم
            volume = component.get('volume', 0) / 1000  # تحويل إلى سم³
            self.results_table.setItem(row, 6, QTableWidgetItem(f"{volume:.2f}"))

            # الوزن والتكلفة
            if ADVANCED_FEATURES and self.material_manager:
                volume_m3 = volume / 1000000  # تحويل إلى م³
                weight = self.material_manager.calculate_weight(material, volume_m3) * quantity
                cost = self.material_manager.calculate_cost(material, volume_m3) * quantity

                self.results_table.setItem(row, 7, QTableWidgetItem(f"{weight:.2f}"))
                self.results_table.setItem(row, 8, QTableWidgetItem(f"{cost:.2f}"))
            else:
                self.results_table.setItem(row, 7, QTableWidgetItem("0.00"))
                self.results_table.setItem(row, 8, QTableWidgetItem("0.00"))

        # تحديث الإحصائيات
        self.update_statistics()

    def update_statistics(self):
        """تحديث الإحصائيات"""
        if not self.components_data:
            self.stats_text.setText("لا توجد بيانات لعرضها")
            return

        # حساب الإحصائيات
        total_components = len(self.components_data)
        total_quantity = sum(comp.get('quantity', 1) for comp in self.components_data)
        total_volume = sum(comp.get('volume', 0) for comp in self.components_data) / 1000  # سم³

        materials_count = {}
        total_weight = 0
        total_cost = 0

        for comp in self.components_data:
            material = comp.get('material', 'خشب')
            quantity = comp.get('quantity', 1)
            materials_count[material] = materials_count.get(material, 0) + quantity

            if ADVANCED_FEATURES and self.material_manager:
                volume_m3 = comp.get('volume', 0) / 1000000000  # تحويل إلى م³
                total_weight += self.material_manager.calculate_weight(material, volume_m3) * quantity
                total_cost += self.material_manager.calculate_cost(material, volume_m3) * quantity

        # تنسيق النص
        stats_text = f"""
📊 إحصائيات المشروع

🔢 العدد والكمية:
   • إجمالي أنواع المكونات: {total_components}
   • إجمالي عدد القطع: {total_quantity}

📏 الأبعاد والحجم:
   • إجمالي الحجم: {total_volume:.2f} سم³
   • إجمالي الوزن المقدر: {total_weight:.2f} كغ

💰 التكلفة:
   • إجمالي التكلفة المقدرة: {total_cost:.2f} ريال

🏗️ المواد المستخدمة:
"""

        for material, count in materials_count.items():
            stats_text += f"   • {material}: {count} قطعة\n"

        # معلومات إضافية
        if self.current_project:
            stats_text += f"\n📁 معلومات المشروع:\n"
            stats_text += f"   • الاسم: {self.current_project.info.name}\n"
            stats_text += f"   • تاريخ الإنشاء: {self.current_project.info.created_date[:10]}\n"

        self.stats_text.setText(stats_text)

    # الطرق الجديدة للميزات المتقدمة
    def add_component_manually(self):
        """إضافة مكون يدوياً مع ميزات متقدمة"""
        dialog = QDialog(self)
        dialog.setWindowTitle("إضافة مكون جديد")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QFormLayout(dialog)

        # حقول الإدخال
        name_edit = QLineEdit()
        name_edit.setPlaceholderText("مثال: لوح علوي")

        length_spin = QDoubleSpinBox()
        length_spin.setRange(1, 10000)
        length_spin.setValue(1200)
        length_spin.setSuffix(" مم")

        width_spin = QDoubleSpinBox()
        width_spin.setRange(1, 10000)
        width_spin.setValue(600)
        width_spin.setSuffix(" مم")

        thickness_spin = QDoubleSpinBox()
        thickness_spin.setRange(0.1, 1000)
        thickness_spin.setValue(18)
        thickness_spin.setSuffix(" مم")

        material_combo = QComboBox()
        if ADVANCED_FEATURES and self.material_manager:
            materials = self.material_manager.get_material_names()
            material_combo.addItems(materials)
        else:
            material_combo.addItems(["خشب", "MDF", "خشب رقائقي", "ألمنيوم", "بلاستيك"])

        quantity_spin = QSpinBox()
        quantity_spin.setRange(1, 100)
        quantity_spin.setValue(1)

        # إضافة الحقول للتخطيط
        layout.addRow("اسم المكون:", name_edit)
        layout.addRow("الطول:", length_spin)
        layout.addRow("العرض:", width_spin)
        layout.addRow("السماكة:", thickness_spin)
        layout.addRow("نوع المادة:", material_combo)
        layout.addRow("الكمية:", quantity_spin)

        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addRow(buttons)

        if dialog.exec_() == QDialog.Accepted:
            # إنشاء المكون الجديد
            dimensions = {
                'length': length_spin.value(),
                'width': width_spin.value(),
                'thickness': thickness_spin.value()
            }

            volume = dimensions['length'] * dimensions['width'] * dimensions['thickness']  # مم³

            component = {
                'name': name_edit.text() or f"مكون {len(self.components_data) + 1}",
                'dimensions': dimensions,
                'material': material_combo.currentText(),
                'quantity': quantity_spin.value(),
                'volume': volume,
                'type': 'manual'
            }

            self.components_data.append(component)
            self.populate_table(self.components_data)
            self.status_label.setText(f"تم إضافة المكون: {component['name']}")

            # تفعيل أزرار التصدير
            self.export_excel_btn.setEnabled(True)
            self.export_pdf_btn.setEnabled(True)

    def export_to_excel(self):
        """تصدير إلى Excel"""
        if not self.components_data:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات للتصدير")
            return

        if not ADVANCED_FEATURES:
            QMessageBox.warning(self, "تحذير", "ميزة التصدير غير متاحة. يرجى تثبيت المكتبات المطلوبة.")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ ملف Excel", "cutlist.xlsx", "Excel Files (*.xlsx)"
        )

        if file_path:
            try:
                project_info = {
                    'title': 'جدول القطع',
                    'project_name': self.project_name_edit.text() or 'مشروع جديد',
                    'description': self.project_desc_edit.toPlainText()
                }

                success = export_to_excel(self.components_data, file_path, project_info)
                if success:
                    QMessageBox.information(self, "نجح", f"تم تصدير الملف بنجاح:\n{file_path}")
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في تصدير الملف")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")

    def export_to_pdf(self):
        """تصدير إلى PDF"""
        if not self.components_data:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات للتصدير")
            return

        if not ADVANCED_FEATURES:
            QMessageBox.warning(self, "تحذير", "ميزة التصدير غير متاحة. يرجى تثبيت المكتبات المطلوبة.")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ ملف PDF", "cutlist.pdf", "PDF Files (*.pdf)"
        )

        if file_path:
            try:
                project_info = {
                    'title': 'جدول القطع',
                    'project_name': self.project_name_edit.text() or 'مشروع جديد',
                    'description': self.project_desc_edit.toPlainText()
                }

                success = export_to_pdf(self.components_data, file_path, project_info)
                if success:
                    QMessageBox.information(self, "نجح", f"تم تصدير الملف بنجاح:\n{file_path}")
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في تصدير الملف")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")

    def save_project(self):
        """حفظ المشروع"""
        if not ADVANCED_FEATURES:
            QMessageBox.warning(self, "تحذير", "ميزة حفظ المشاريع غير متاحة.")
            return

        if not self.components_data:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات للحفظ")
            return

        # إنشاء أو تحديث المشروع
        if not self.current_project:
            project_name = self.project_name_edit.text() or f"مشروع جديد - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            description = self.project_desc_edit.toPlainText()

            self.current_project = self.project_manager.create_project_from_components(
                self.components_data, project_name, description
            )
        else:
            # تحديث المشروع الحالي
            self.current_project.components = self.components_data
            self.current_project.info.name = self.project_name_edit.text() or self.current_project.info.name
            self.current_project.info.description = self.project_desc_edit.toPlainText()

        # حفظ المشروع
        success = self.project_manager.save_project(self.current_project, self.current_file_path)

        if success:
            QMessageBox.information(self, "نجح", "تم حفظ المشروع بنجاح")
            self.update_projects_list()
            self.status_label.setText(f"تم حفظ المشروع: {self.current_project.info.name}")
        else:
            QMessageBox.critical(self, "خطأ", "فشل في حفظ المشروع")

    def load_project(self):
        """تحميل مشروع محفوظ"""
        if not ADVANCED_FEATURES:
            return

        current_item = self.projects_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مشروع للتحميل")
            return

        file_path = current_item.data(Qt.UserRole)
        project = self.project_manager.load_project(file_path)

        if project:
            self.current_project = project
            self.current_file_path = file_path
            self.components_data = project.components

            # تحديث الواجهة
            self.project_name_edit.setText(project.info.name)
            self.project_desc_edit.setText(project.info.description)
            self.populate_table(self.components_data)

            # تفعيل أزرار التصدير
            self.export_excel_btn.setEnabled(True)
            self.export_pdf_btn.setEnabled(True)

            self.status_label.setText(f"تم تحميل المشروع: {project.info.name}")
            QMessageBox.information(self, "نجح", f"تم تحميل المشروع بنجاح:\n{project.info.name}")
        else:
            QMessageBox.critical(self, "خطأ", "فشل في تحميل المشروع")

    def new_project(self):
        """مشروع جديد"""
        if self.components_data:
            reply = QMessageBox.question(
                self, "مشروع جديد",
                "هل تريد حفظ المشروع الحالي قبل إنشاء مشروع جديد؟",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
            )

            if reply == QMessageBox.Yes:
                self.save_project()
            elif reply == QMessageBox.Cancel:
                return

        # مسح البيانات الحالية
        self.components_data = []
        self.current_project = None
        self.current_file_path = None

        # مسح الواجهة
        self.results_table.setRowCount(0)
        self.project_name_edit.clear()
        self.project_desc_edit.clear()
        self.stats_text.clear()

        # تعطيل أزرار التصدير
        self.export_excel_btn.setEnabled(False)
        self.export_pdf_btn.setEnabled(False)

        self.status_label.setText("مشروع جديد - جاهز لإضافة المكونات")

    def open_project_file(self):
        """فتح ملف مشروع"""
        if not ADVANCED_FEATURES:
            return

        file_path, _ = QFileDialog.getOpenFileName(
            self, "فتح مشروع", "", "CutList Files (*.cutlist)"
        )

        if file_path:
            project = self.project_manager.load_project(file_path)
            if project:
                self.current_project = project
                self.current_file_path = file_path
                self.components_data = project.components

                # تحديث الواجهة
                self.project_name_edit.setText(project.info.name)
                self.project_desc_edit.setText(project.info.description)
                self.populate_table(self.components_data)

                self.status_label.setText(f"تم فتح المشروع: {project.info.name}")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في فتح المشروع")

    def update_materials_list(self):
        """تحديث قائمة المواد"""
        if not ADVANCED_FEATURES:
            return

        self.materials_list.clear()
        materials = self.material_manager.get_all_materials()

        for name, material in materials.items():
            item = QListWidgetItem(f"{name} ({material.category})")
            item.setData(Qt.UserRole, name)
            self.materials_list.addItem(item)

    def update_projects_list(self):
        """تحديث قائمة المشاريع"""
        if not ADVANCED_FEATURES:
            return

        self.projects_list.clear()
        projects = self.project_manager.get_project_list()

        for project in projects:
            item_text = f"{project['name']} ({project['components_count']} مكون)"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, project['file_path'])
            self.projects_list.addItem(item)

    def add_material(self):
        """إضافة مادة جديدة"""
        if not ADVANCED_FEATURES:
            return

        # هنا يمكن إضافة نافذة حوار لإضافة مادة جديدة
        QMessageBox.information(self, "قريباً", "ميزة إضافة المواد ستكون متاحة قريباً")

    def edit_material(self):
        """تعديل مادة"""
        if not ADVANCED_FEATURES:
            return

        QMessageBox.information(self, "قريباً", "ميزة تعديل المواد ستكون متاحة قريباً")

    def open_materials_manager(self):
        """فتح مدير المواد"""
        if not ADVANCED_FEATURES:
            return

        QMessageBox.information(self, "قريباً", "مدير المواد المتقدم سيكون متاحاً قريباً")

    def show_about(self):
        """عرض معلومات التطبيق"""
        about_text = """
        <h2>CutList Desktop App</h2>
        <p><b>تطبيق جدول القطع المتقدم</b></p>
        <p>الإصدار: 2.0</p>
        <p>تطبيق سطح مكتب لتوليد جدول القطع من النماذج ثلاثية الأبعاد</p>

        <h3>الميزات:</h3>
        <ul>
        <li>تحليل النماذج ثلاثية الأبعاد (.obj, .stl, .dae)</li>
        <li>إدارة متقدمة للمواد</li>
        <li>تصدير إلى Excel و PDF</li>
        <li>حفظ وتحميل المشاريع</li>
        <li>إحصائيات مفصلة</li>
        <li>واجهة عربية متقدمة</li>
        </ul>

        <p><small>تم التطوير باستخدام Python و PyQt5</small></p>
        """

        QMessageBox.about(self, "حول التطبيق", about_text)

    def on_cell_changed(self, row, column):
        """عند تغيير خلية في الجدول"""
        if row < len(self.components_data):
            item = self.results_table.item(row, column)
            if item:
                value = item.text()

                # تحديث البيانات حسب العمود
                if column == 0:  # اسم المكون
                    self.components_data[row]['name'] = value
                elif column == 1:  # الطول
                    try:
                        self.components_data[row]['dimensions']['length'] = float(value)
                        self._recalculate_component(row)
                    except ValueError:
                        pass
                elif column == 2:  # العرض
                    try:
                        self.components_data[row]['dimensions']['width'] = float(value)
                        self._recalculate_component(row)
                    except ValueError:
                        pass
                elif column == 3:  # السماكة
                    try:
                        self.components_data[row]['dimensions']['thickness'] = float(value)
                        self._recalculate_component(row)
                    except ValueError:
                        pass
                elif column == 4:  # المادة
                    self.components_data[row]['material'] = value
                    self._recalculate_component(row)
                elif column == 5:  # الكمية
                    try:
                        self.components_data[row]['quantity'] = int(value)
                        self._recalculate_component(row)
                    except ValueError:
                        pass

                # تحديث الإحصائيات
                self.update_statistics()

    def _recalculate_component(self, row):
        """إعادة حساب بيانات المكون"""
        if row >= len(self.components_data):
            return

        component = self.components_data[row]
        dimensions = component['dimensions']

        # إعادة حساب الحجم
        volume = dimensions['length'] * dimensions['width'] * dimensions['thickness']
        component['volume'] = volume

        # تحديث الجدول
        self.results_table.setItem(row, 6, QTableWidgetItem(f"{volume/1000:.2f}"))

        # إعادة حساب الوزن والتكلفة
        if ADVANCED_FEATURES and self.material_manager:
            material = component.get('material', 'خشب')
            quantity = component.get('quantity', 1)
            volume_m3 = volume / 1000000000  # تحويل إلى م³

            weight = self.material_manager.calculate_weight(material, volume_m3) * quantity
            cost = self.material_manager.calculate_cost(material, volume_m3) * quantity

            self.results_table.setItem(row, 7, QTableWidgetItem(f"{weight:.2f}"))
            self.results_table.setItem(row, 8, QTableWidgetItem(f"{cost:.2f}"))


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)

    # تعيين الخط الافتراضي لدعم العربية
    font = QFont("Arial", 10)
    app.setFont(font)

    # تعيين معلومات التطبيق
    app.setApplicationName("CutList Desktop App")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("CutList Development")

    # إنشاء النافذة الرئيسية
    try:
        window = CutListMainWindow()
        window.show()

        # عرض رسالة ترحيب
        if ADVANCED_FEATURES:
            window.status_label.setText("تطبيق جدول القطع المتقدم - جاهز للاستخدام")
        else:
            window.status_label.setText("تطبيق جدول القطع الأساسي - بعض الميزات غير متاحة")
            QMessageBox.information(
                window,
                "معلومات",
                "بعض الميزات المتقدمة غير متاحة.\nلتفعيل جميع الميزات، يرجى تثبيت:\npip install trimesh pandas openpyxl reportlab arabic-reshaper python-bidi"
            )

        sys.exit(app.exec_())

    except Exception as e:
        QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء تشغيل التطبيق:\n{str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
