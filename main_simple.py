#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CutList Desktop App - إصدار مبسط
تطبيق سطح مكتب لتوليد جدول القطع من النماذج ثلاثية الأبعاد
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QTableWidget, QTableWidgetItem,
                             QFileDialog, QMessageBox, QLabel, QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont


class CutListMainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق - إصدار مبسط"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.components_data = []
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("CutList Desktop App - تطبيق جدول القطع")
        self.setGeometry(100, 100, 1000, 600)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # شريط الأزرار العلوي
        button_layout = QHBoxLayout()
        
        # زر فتح الملف
        self.open_file_btn = QPushButton("فتح ملف ثلاثي الأبعاد")
        self.open_file_btn.setFont(QFont("Arial", 12))
        self.open_file_btn.clicked.connect(self.open_file)
        button_layout.addWidget(self.open_file_btn)
        
        # زر إضافة مكون يدوياً
        self.add_component_btn = QPushButton("إضافة مكون يدوياً")
        self.add_component_btn.setFont(QFont("Arial", 12))
        self.add_component_btn.clicked.connect(self.add_component_manually)
        button_layout.addWidget(self.add_component_btn)
        
        # زر مسح الجدول
        self.clear_btn = QPushButton("مسح الجدول")
        self.clear_btn.setFont(QFont("Arial", 12))
        self.clear_btn.clicked.connect(self.clear_table)
        button_layout.addWidget(self.clear_btn)
        
        button_layout.addStretch()
        main_layout.addLayout(button_layout)
        
        # تسمية الجدول
        self.table_label = QLabel("جدول القطع - Cut List")
        self.table_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.table_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.table_label)
        
        # جدول النتائج
        self.results_table = QTableWidget()
        self.setup_table()
        main_layout.addWidget(self.results_table)
        
        # شريط الحالة
        self.status_label = QLabel("جاهز - اختر ملف ثلاثي الأبعاد أو أضف مكونات يدوياً")
        self.status_label.setFont(QFont("Arial", 10))
        main_layout.addWidget(self.status_label)
        
        # إضافة بعض البيانات التجريبية
        self.add_sample_data()
        
    def setup_table(self):
        """إعداد جدول النتائج"""
        # تحديد الأعمدة
        headers = ["اسم المكون", "الطول (مم)", "العرض (مم)", "السماكة (مم)", "نوع المادة", "الكمية"]
        self.results_table.setColumnCount(len(headers))
        self.results_table.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # تنسيق الخط
        font = QFont("Arial", 10)
        self.results_table.setFont(font)
        
    def open_file(self):
        """فتح ملف ثلاثي الأبعاد"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف ثلاثي الأبعاد",
            "",
            "3D Files (*.obj *.stl *.dae);;OBJ Files (*.obj);;STL Files (*.stl);;DAE Files (*.dae)"
        )
        
        if file_path:
            # في الوقت الحالي، سنعرض رسالة أن الملف تم اختياره
            # وسنضيف مكونات تجريبية
            file_name = os.path.basename(file_path)
            self.status_label.setText(f"تم اختيار الملف: {file_name}")
            
            # إضافة مكونات تجريبية بناءً على اسم الملف
            self.add_demo_components_from_file(file_name)
    
    def add_demo_components_from_file(self, file_name):
        """إضافة مكونات تجريبية بناءً على الملف المختار"""
        # مسح الجدول الحالي
        self.results_table.setRowCount(0)
        
        # إضافة مكونات تجريبية
        demo_components = [
            {"name": f"لوح علوي - {file_name}", "length": 1200, "width": 600, "thickness": 18, "material": "خشب", "quantity": 1},
            {"name": f"لوح جانبي - {file_name}", "length": 800, "width": 400, "thickness": 18, "material": "خشب", "quantity": 2},
            {"name": f"لوح خلفي - {file_name}", "length": 1180, "width": 780, "thickness": 6, "material": "خشب رقائقي", "quantity": 1},
            {"name": f"رف داخلي - {file_name}", "length": 1160, "width": 380, "thickness": 18, "material": "MDF", "quantity": 3}
        ]
        
        for component in demo_components:
            self.add_component_to_table(component)
        
        self.status_label.setText(f"تم تحليل الملف: {file_name} - تم العثور على {len(demo_components)} مكون")
    
    def add_component_manually(self):
        """إضافة مكون يدوياً"""
        from PyQt5.QtWidgets import QDialog, QFormLayout, QLineEdit, QComboBox, QSpinBox, QDialogButtonBox
        
        dialog = QDialog(self)
        dialog.setWindowTitle("إضافة مكون جديد")
        dialog.setModal(True)
        
        layout = QFormLayout(dialog)
        
        # حقول الإدخال
        name_edit = QLineEdit()
        name_edit.setPlaceholderText("مثال: لوح علوي")
        
        length_spin = QSpinBox()
        length_spin.setRange(1, 10000)
        length_spin.setValue(1200)
        length_spin.setSuffix(" مم")
        
        width_spin = QSpinBox()
        width_spin.setRange(1, 10000)
        width_spin.setValue(600)
        width_spin.setSuffix(" مم")
        
        thickness_spin = QSpinBox()
        thickness_spin.setRange(1, 1000)
        thickness_spin.setValue(18)
        thickness_spin.setSuffix(" مم")
        
        material_combo = QComboBox()
        material_combo.addItems(["خشب", "MDF", "خشب رقائقي", "ألمنيوم", "بلاستيك"])
        
        quantity_spin = QSpinBox()
        quantity_spin.setRange(1, 100)
        quantity_spin.setValue(1)
        
        # إضافة الحقول للتخطيط
        layout.addRow("اسم المكون:", name_edit)
        layout.addRow("الطول:", length_spin)
        layout.addRow("العرض:", width_spin)
        layout.addRow("السماكة:", thickness_spin)
        layout.addRow("نوع المادة:", material_combo)
        layout.addRow("الكمية:", quantity_spin)
        
        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addRow(buttons)
        
        if dialog.exec_() == QDialog.Accepted:
            component = {
                "name": name_edit.text() or f"مكون {self.results_table.rowCount() + 1}",
                "length": length_spin.value(),
                "width": width_spin.value(),
                "thickness": thickness_spin.value(),
                "material": material_combo.currentText(),
                "quantity": quantity_spin.value()
            }
            
            self.add_component_to_table(component)
            self.status_label.setText(f"تم إضافة المكون: {component['name']}")
    
    def add_component_to_table(self, component):
        """إضافة مكون إلى الجدول"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)
        
        # إضافة البيانات
        self.results_table.setItem(row, 0, QTableWidgetItem(component["name"]))
        self.results_table.setItem(row, 1, QTableWidgetItem(str(component["length"])))
        self.results_table.setItem(row, 2, QTableWidgetItem(str(component["width"])))
        self.results_table.setItem(row, 3, QTableWidgetItem(str(component["thickness"])))
        self.results_table.setItem(row, 4, QTableWidgetItem(component["material"]))
        self.results_table.setItem(row, 5, QTableWidgetItem(str(component["quantity"])))
    
    def clear_table(self):
        """مسح جميع البيانات من الجدول"""
        reply = QMessageBox.question(self, "تأكيد المسح", 
                                   "هل أنت متأكد من مسح جميع البيانات؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.results_table.setRowCount(0)
            self.status_label.setText("تم مسح الجدول - جاهز لإدخال بيانات جديدة")
    
    def add_sample_data(self):
        """إضافة بيانات تجريبية للاختبار"""
        sample_components = [
            {"name": "لوح علوي", "length": 1200, "width": 600, "thickness": 18, "material": "خشب", "quantity": 1},
            {"name": "لوح جانبي", "length": 800, "width": 400, "thickness": 18, "material": "خشب", "quantity": 2},
            {"name": "لوح خلفي", "length": 1180, "width": 780, "thickness": 6, "material": "خشب رقائقي", "quantity": 1},
            {"name": "رف داخلي", "length": 1160, "width": 380, "thickness": 18, "material": "MDF", "quantity": 2}
        ]
        
        for component in sample_components:
            self.add_component_to_table(component)


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط الافتراضي لدعم العربية
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = CutListMainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
