#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CutList Pro - Real Furniture Designer Application
تطبيق مصمم الأثاث الحقيقي والمتكامل
"""

import sys
import os
import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

# PyQt5 imports
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QTabWidget, QTableWidget, QTableWidgetItem, QTreeWidget,
    QTreeWidgetItem, QListWidget, QListWidgetItem, QPushButton, QLabel,
    QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox,
    QGroupBox, QFormLayout, QGridLayout, QScrollArea, Q<PERSON>rame,
    QProgressBar, QStatusBar, QMenuBar, QToolBar, QAction, QFileDialog,
    QMessageBox, QDialog, QDialogButtonBox, QHeaderView, QSizePolicy
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize
from PyQt5.QtGui import QFont, QIcon, QPixmap, QPainter, QPen, QBrush, QColor


@dataclass
class Component:
    """مكون أثاث"""
    id: str
    name: str
    length: float
    width: float
    thickness: float
    material: str
    quantity: int
    edge_banding: str = ""
    notes: str = ""
    cost_per_unit: float = 0.0

    @property
    def volume_m3(self) -> float:
        """الحجم بالمتر المكعب"""
        return (self.length * self.width * self.thickness) / 1000000000

    @property
    def area_m2(self) -> float:
        """المساحة بالمتر المربع"""
        return (self.length * self.width) / 1000000

    @property
    def total_cost(self) -> float:
        """التكلفة الإجمالية"""
        return self.cost_per_unit * self.quantity


@dataclass
class Project:
    """مشروع أثاث"""
    id: str
    name: str
    client_name: str
    client_phone: str
    description: str
    components: List[Component]
    created_date: str
    status: str = "جديد"
    total_cost: float = 0.0

    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()
        self.calculate_total_cost()

    def calculate_total_cost(self):
        """حساب التكلفة الإجمالية"""
        self.total_cost = sum(comp.total_cost for comp in self.components)


class DatabaseManager:
    """مدير قاعدة البيانات"""

    def __init__(self, db_path: str = "cutlist_pro.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """تهيئة قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # جدول المشاريع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS projects (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                client_name TEXT,
                client_phone TEXT,
                description TEXT,
                created_date TEXT,
                status TEXT,
                total_cost REAL
            )
        ''')

        # جدول المكونات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS components (
                id TEXT PRIMARY KEY,
                project_id TEXT,
                name TEXT NOT NULL,
                length REAL,
                width REAL,
                thickness REAL,
                material TEXT,
                quantity INTEGER,
                edge_banding TEXT,
                notes TEXT,
                cost_per_unit REAL,
                FOREIGN KEY (project_id) REFERENCES projects (id)
            )
        ''')

        # جدول المواد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS materials (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                cost_per_m3 REAL,
                cost_per_m2 REAL,
                density REAL,
                supplier TEXT,
                notes TEXT
            )
        ''')

        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                notes TEXT,
                created_date TEXT
            )
        ''')

        conn.commit()
        conn.close()

        # إضافة مواد افتراضية
        self.add_default_materials()

    def add_default_materials(self):
        """إضافة مواد افتراضية"""
        default_materials = [
            ("MAT001", "خشب طبيعي", 800, 20, 0.6, "مورد محلي", "خشب عالي الجودة"),
            ("MAT002", "MDF", 400, 15, 0.7, "مورد محلي", "ألواح ليفية متوسطة الكثافة"),
            ("MAT003", "خشب رقائقي", 600, 18, 0.65, "مورد محلي", "خشب رقائقي متعدد الطبقات"),
            ("MAT004", "ألمنيوم", 2000, 50, 2.7, "مورد معادن", "ألمنيوم خفيف الوزن"),
            ("MAT005", "بلاستيك", 300, 10, 0.9, "مورد بلاستيك", "بلاستيك عالي الجودة"),
        ]

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for material in default_materials:
            cursor.execute('''
                INSERT OR IGNORE INTO materials
                (id, name, cost_per_m3, cost_per_m2, density, supplier, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', material)

        conn.commit()
        conn.close()

    def save_project(self, project: Project) -> bool:
        """حفظ مشروع"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حفظ المشروع
            cursor.execute('''
                INSERT OR REPLACE INTO projects
                (id, name, client_name, client_phone, description, created_date, status, total_cost)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (project.id, project.name, project.client_name, project.client_phone,
                  project.description, project.created_date, project.status, project.total_cost))

            # حذف المكونات القديمة
            cursor.execute('DELETE FROM components WHERE project_id = ?', (project.id,))

            # حفظ المكونات
            for component in project.components:
                cursor.execute('''
                    INSERT INTO components
                    (id, project_id, name, length, width, thickness, material, quantity,
                     edge_banding, notes, cost_per_unit)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (component.id, project.id, component.name, component.length, component.width,
                      component.thickness, component.material, component.quantity,
                      component.edge_banding, component.notes, component.cost_per_unit))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في حفظ المشروع: {e}")
            return False

    def load_project(self, project_id: str) -> Optional[Project]:
        """تحميل مشروع"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # تحميل المشروع
            cursor.execute('SELECT * FROM projects WHERE id = ?', (project_id,))
            project_data = cursor.fetchone()

            if not project_data:
                return None

            # تحميل المكونات
            cursor.execute('SELECT * FROM components WHERE project_id = ?', (project_id,))
            components_data = cursor.fetchall()

            components = []
            for comp_data in components_data:
                component = Component(
                    id=comp_data[0],
                    name=comp_data[2],
                    length=comp_data[3],
                    width=comp_data[4],
                    thickness=comp_data[5],
                    material=comp_data[6],
                    quantity=comp_data[7],
                    edge_banding=comp_data[8] or "",
                    notes=comp_data[9] or "",
                    cost_per_unit=comp_data[10] or 0.0
                )
                components.append(component)

            project = Project(
                id=project_data[0],
                name=project_data[1],
                client_name=project_data[2] or "",
                client_phone=project_data[3] or "",
                description=project_data[4] or "",
                components=components,
                created_date=project_data[5],
                status=project_data[6],
                total_cost=project_data[7] or 0.0
            )

            conn.close()
            return project

        except Exception as e:
            print(f"خطأ في تحميل المشروع: {e}")
            return None

    def get_all_projects(self) -> List[Dict[str, Any]]:
        """الحصول على جميع المشاريع"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM projects ORDER BY created_date DESC')
            projects_data = cursor.fetchall()

            projects = []
            for project_data in projects_data:
                projects.append({
                    'id': project_data[0],
                    'name': project_data[1],
                    'client_name': project_data[2] or "",
                    'client_phone': project_data[3] or "",
                    'description': project_data[4] or "",
                    'created_date': project_data[5],
                    'status': project_data[6],
                    'total_cost': project_data[7] or 0.0
                })

            conn.close()
            return projects

        except Exception as e:
            print(f"خطأ في تحميل المشاريع: {e}")
            return []

    def get_materials(self) -> List[Dict[str, Any]]:
        """الحصول على جميع المواد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM materials')
            materials_data = cursor.fetchall()

            materials = []
            for material_data in materials_data:
                materials.append({
                    'id': material_data[0],
                    'name': material_data[1],
                    'cost_per_m3': material_data[2] or 0.0,
                    'cost_per_m2': material_data[3] or 0.0,
                    'density': material_data[4] or 0.0,
                    'supplier': material_data[5] or "",
                    'notes': material_data[6] or ""
                })

            conn.close()
            return materials

        except Exception as e:
            print(f"خطأ في تحميل المواد: {e}")
            return []


class CuttingOptimizer:
    """محسن القطع الحقيقي"""

    def __init__(self):
        self.kerf_width = 3.0  # عرض القطع
        self.edge_margin = 5.0  # هامش الحافة

    def optimize_cutting(self, components: List[Component], sheet_length: float = 2440,
                        sheet_width: float = 1220) -> Dict[str, Any]:
        """تحسين خطة القطع"""

        # تجميع المكونات حسب السماكة والمادة
        grouped_components = {}
        for comp in components:
            key = (comp.material, comp.thickness)
            if key not in grouped_components:
                grouped_components[key] = []

            # إضافة كل قطعة حسب الكمية
            for _ in range(comp.quantity):
                grouped_components[key].append({
                    'name': comp.name,
                    'length': comp.length,
                    'width': comp.width,
                    'area': comp.length * comp.width
                })

        cutting_plans = []
        total_efficiency = 0.0
        total_waste = 0.0

        for (material, thickness), pieces in grouped_components.items():
            # ترتيب القطع حسب المساحة (الأكبر أولاً)
            pieces.sort(key=lambda x: x['area'], reverse=True)

            sheets_needed = 0
            current_sheet_pieces = []
            current_sheet_area = 0
            sheet_area = sheet_length * sheet_width

            for piece in pieces:
                piece_area = piece['area']

                # فحص إذا كانت القطعة تتسع في اللوح الحالي
                if (current_sheet_area + piece_area <= sheet_area * 0.85):  # 85% كفاءة قصوى
                    current_sheet_pieces.append(piece)
                    current_sheet_area += piece_area
                else:
                    # إنهاء اللوح الحالي وبدء لوح جديد
                    if current_sheet_pieces:
                        efficiency = current_sheet_area / sheet_area
                        waste = sheet_area - current_sheet_area

                        cutting_plans.append({
                            'sheet_number': sheets_needed + 1,
                            'material': material,
                            'thickness': thickness,
                            'pieces': current_sheet_pieces.copy(),
                            'efficiency': efficiency,
                            'waste_area': waste,
                            'used_area': current_sheet_area
                        })

                        total_efficiency += efficiency
                        total_waste += waste
                        sheets_needed += 1

                    # بدء لوح جديد
                    current_sheet_pieces = [piece]
                    current_sheet_area = piece_area

            # إضافة اللوح الأخير
            if current_sheet_pieces:
                efficiency = current_sheet_area / sheet_area
                waste = sheet_area - current_sheet_area

                cutting_plans.append({
                    'sheet_number': sheets_needed + 1,
                    'material': material,
                    'thickness': thickness,
                    'pieces': current_sheet_pieces.copy(),
                    'efficiency': efficiency,
                    'waste_area': waste,
                    'used_area': current_sheet_area
                })

                total_efficiency += efficiency
                total_waste += waste
                sheets_needed += 1

        # حساب الإحصائيات الإجمالية
        avg_efficiency = total_efficiency / len(cutting_plans) if cutting_plans else 0

        return {
            'cutting_plans': cutting_plans,
            'total_sheets': len(cutting_plans),
            'average_efficiency': avg_efficiency,
            'total_waste': total_waste,
            'total_components': len(components)
        }


class PricingCalculator:
    """حاسبة التسعير الحقيقية"""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.materials_cache = {}
        self.load_materials()

    def load_materials(self):
        """تحميل أسعار المواد"""
        materials = self.db_manager.get_materials()
        for material in materials:
            self.materials_cache[material['name']] = material

    def calculate_component_cost(self, component: Component) -> float:
        """حساب تكلفة مكون واحد"""
        material_info = self.materials_cache.get(component.material)
        if not material_info:
            return 0.0

        # حساب التكلفة بناءً على الحجم
        volume_m3 = component.volume_m3
        cost_per_m3 = material_info['cost_per_m3']

        base_cost = volume_m3 * cost_per_m3

        # إضافة تكلفة التشطيب والحواف
        edge_cost = 0.0
        if component.edge_banding:
            perimeter = 2 * (component.length + component.width) / 1000  # متر
            edge_cost = perimeter * 5  # 5 ريال للمتر

        total_cost = (base_cost + edge_cost) * component.quantity
        return total_cost

    def calculate_project_pricing(self, project: Project) -> Dict[str, float]:
        """حساب تسعير المشروع الكامل"""
        materials_cost = 0.0

        # حساب تكلفة المواد
        for component in project.components:
            component.cost_per_unit = self.calculate_component_cost(component) / component.quantity
            materials_cost += component.total_cost

        # حساب تكلفة العمالة (40% من تكلفة المواد)
        labor_cost = materials_cost * 0.4

        # المصاريف العامة (15% من التكلفة الأساسية)
        overhead_cost = (materials_cost + labor_cost) * 0.15

        # التكلفة الأساسية
        base_cost = materials_cost + labor_cost + overhead_cost

        # هامش الربح (25%)
        profit_margin = base_cost * 0.25

        # السعر النهائي
        final_price = base_cost + profit_margin

        return {
            'materials_cost': materials_cost,
            'labor_cost': labor_cost,
            'overhead_cost': overhead_cost,
            'profit_margin': profit_margin,
            'base_cost': base_cost,
            'final_price': final_price
        }


class CutListProMainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق الحقيقي"""

    def __init__(self):
        super().__init__()

        # تهيئة المكونات الأساسية
        self.db_manager = DatabaseManager()
        self.cutting_optimizer = CuttingOptimizer()
        self.pricing_calculator = PricingCalculator(self.db_manager)

        # المشروع الحالي
        self.current_project = None

        # إعداد الواجهة
        self.init_ui()
        self.apply_modern_style()
        self.load_initial_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("CutList Pro - مصمم الأثاث الاحترافي")
        self.setGeometry(100, 100, 1400, 900)

        # إنشاء القوائم
        self.create_menu_bar()

        # إنشاء شريط الأدوات
        self.create_toolbar()

        # إنشاء الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)

        # المقسم الرئيسي
        main_splitter = QSplitter(Qt.Horizontal)

        # اللوحة اليسرى - المشاريع والمواد
        left_panel = self.create_left_panel()
        main_splitter.addWidget(left_panel)

        # اللوحة الوسطى - التصميم والمكونات
        center_panel = self.create_center_panel()
        main_splitter.addWidget(center_panel)

        # اللوحة اليمنى - التحليل والتسعير
        right_panel = self.create_right_panel()
        main_splitter.addWidget(right_panel)

        # تعيين النسب
        main_splitter.setSizes([300, 700, 400])

        main_layout.addWidget(main_splitter)

        # شريط الحالة
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("جاهز - مرحباً بك في CutList Pro")

    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()

        # قائمة ملف
        file_menu = menubar.addMenu('ملف')

        new_action = QAction('مشروع جديد', self)
        new_action.setShortcut('Ctrl+N')
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)

        open_action = QAction('فتح مشروع', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)

        save_action = QAction('حفظ', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_project)
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        export_excel_action = QAction('تصدير Excel', self)
        export_excel_action.triggered.connect(self.export_to_excel)
        file_menu.addAction(export_excel_action)

        file_menu.addSeparator()

        exit_action = QAction('خروج', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة تحرير
        edit_menu = menubar.addMenu('تحرير')

        add_component_action = QAction('إضافة مكون', self)
        add_component_action.setShortcut('Ctrl+A')
        add_component_action.triggered.connect(self.add_component)
        edit_menu.addAction(add_component_action)

        delete_component_action = QAction('حذف مكون', self)
        delete_component_action.setShortcut('Delete')
        delete_component_action.triggered.connect(self.delete_component)
        edit_menu.addAction(delete_component_action)

        # قائمة أدوات
        tools_menu = menubar.addMenu('أدوات')

        optimize_action = QAction('تحسين القطع', self)
        optimize_action.triggered.connect(self.optimize_cutting)
        tools_menu.addAction(optimize_action)

        calculate_pricing_action = QAction('حساب التسعير', self)
        calculate_pricing_action.triggered.connect(self.calculate_pricing)
        tools_menu.addAction(calculate_pricing_action)

        # قائمة مساعدة
        help_menu = menubar.addMenu('مساعدة')

        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar('الأدوات الرئيسية')
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # أزرار المشروع
        new_btn = QPushButton('🆕 جديد')
        new_btn.clicked.connect(self.new_project)
        toolbar.addWidget(new_btn)

        open_btn = QPushButton('📂 فتح')
        open_btn.clicked.connect(self.open_project)
        toolbar.addWidget(open_btn)

        save_btn = QPushButton('💾 حفظ')
        save_btn.clicked.connect(self.save_project)
        toolbar.addWidget(save_btn)

        toolbar.addSeparator()

        # أزرار المكونات
        add_comp_btn = QPushButton('➕ إضافة مكون')
        add_comp_btn.clicked.connect(self.add_component)
        toolbar.addWidget(add_comp_btn)

        del_comp_btn = QPushButton('🗑️ حذف')
        del_comp_btn.clicked.connect(self.delete_component)
        toolbar.addWidget(del_comp_btn)

        toolbar.addSeparator()

        # أزرار التحليل
        optimize_btn = QPushButton('⚡ تحسين القطع')
        optimize_btn.clicked.connect(self.optimize_cutting)
        toolbar.addWidget(optimize_btn)

        pricing_btn = QPushButton('💰 حساب التسعير')
        pricing_btn.clicked.connect(self.calculate_pricing)
        toolbar.addWidget(pricing_btn)

        toolbar.addSeparator()

        # أزرار التصدير
        excel_btn = QPushButton('📊 تصدير Excel')
        excel_btn.clicked.connect(self.export_to_excel)
        toolbar.addWidget(excel_btn)

    def create_left_panel(self):
        """إنشاء اللوحة اليسرى"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # تبويبات اللوحة اليسرى
        left_tabs = QTabWidget()

        # تبويب المشاريع
        projects_tab = self.create_projects_tab()
        left_tabs.addTab(projects_tab, "المشاريع")

        # تبويب المواد
        materials_tab = self.create_materials_tab()
        left_tabs.addTab(materials_tab, "المواد")

        layout.addWidget(left_tabs)
        return panel

    def create_center_panel(self):
        """إنشاء اللوحة الوسطى"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # معلومات المشروع
        project_info_group = QGroupBox("معلومات المشروع")
        project_info_layout = QFormLayout(project_info_group)

        self.project_name_edit = QLineEdit()
        self.client_name_edit = QLineEdit()
        self.client_phone_edit = QLineEdit()
        self.project_desc_edit = QTextEdit()
        self.project_desc_edit.setMaximumHeight(60)

        project_info_layout.addRow("اسم المشروع:", self.project_name_edit)
        project_info_layout.addRow("اسم العميل:", self.client_name_edit)
        project_info_layout.addRow("هاتف العميل:", self.client_phone_edit)
        project_info_layout.addRow("الوصف:", self.project_desc_edit)

        layout.addWidget(project_info_group)

        # جدول المكونات
        components_group = QGroupBox("مكونات المشروع")
        components_layout = QVBoxLayout(components_group)

        # أزرار إدارة المكونات
        components_buttons = QHBoxLayout()

        add_comp_btn = QPushButton("➕ إضافة مكون")
        add_comp_btn.clicked.connect(self.add_component)

        edit_comp_btn = QPushButton("✏️ تعديل")
        edit_comp_btn.clicked.connect(self.edit_component)

        delete_comp_btn = QPushButton("🗑️ حذف")
        delete_comp_btn.clicked.connect(self.delete_component)

        duplicate_comp_btn = QPushButton("📋 نسخ")
        duplicate_comp_btn.clicked.connect(self.duplicate_component)

        components_buttons.addWidget(add_comp_btn)
        components_buttons.addWidget(edit_comp_btn)
        components_buttons.addWidget(delete_comp_btn)
        components_buttons.addWidget(duplicate_comp_btn)
        components_buttons.addStretch()

        components_layout.addLayout(components_buttons)

        # جدول المكونات
        self.components_table = QTableWidget()
        self.components_table.setColumnCount(8)
        self.components_table.setHorizontalHeaderLabels([
            "المكون", "الطول (مم)", "العرض (مم)", "السماكة (مم)",
            "المادة", "الكمية", "الحواف", "التكلفة"
        ])

        # تعديل عرض الأعمدة
        header = self.components_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)

        self.components_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.components_table.setAlternatingRowColors(True)

        components_layout.addWidget(self.components_table)

        layout.addWidget(components_group)

        return panel

    def create_right_panel(self):
        """إنشاء اللوحة اليمنى"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # تبويبات اللوحة اليمنى
        right_tabs = QTabWidget()

        # تبويب التسعير
        pricing_tab = self.create_pricing_tab()
        right_tabs.addTab(pricing_tab, "التسعير")

        # تبويب تحسين القطع
        optimization_tab = self.create_optimization_tab()
        right_tabs.addTab(optimization_tab, "تحسين القطع")

        # تبويب الإحصائيات
        statistics_tab = self.create_statistics_tab()
        right_tabs.addTab(statistics_tab, "الإحصائيات")

        layout.addWidget(right_tabs)
        return panel

    def create_projects_tab(self):
        """إنشاء تبويب المشاريع"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # أزرار إدارة المشاريع
        projects_buttons = QHBoxLayout()

        new_project_btn = QPushButton("🆕 مشروع جديد")
        new_project_btn.clicked.connect(self.new_project)

        load_project_btn = QPushButton("📂 تحميل مشروع")
        load_project_btn.clicked.connect(self.load_project_dialog)

        projects_buttons.addWidget(new_project_btn)
        projects_buttons.addWidget(load_project_btn)

        layout.addLayout(projects_buttons)

        # قائمة المشاريع
        self.projects_list = QListWidget()
        self.projects_list.itemDoubleClicked.connect(self.load_selected_project)
        layout.addWidget(self.projects_list)

        return widget

    def create_materials_tab(self):
        """إنشاء تبويب المواد"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # قائمة المواد
        self.materials_list = QListWidget()
        layout.addWidget(self.materials_list)

        # معلومات المادة المختارة
        material_info_group = QGroupBox("معلومات المادة")
        material_info_layout = QFormLayout(material_info_group)

        self.material_name_label = QLabel("-")
        self.material_cost_m3_label = QLabel("-")
        self.material_cost_m2_label = QLabel("-")
        self.material_supplier_label = QLabel("-")

        material_info_layout.addRow("الاسم:", self.material_name_label)
        material_info_layout.addRow("السعر/م³:", self.material_cost_m3_label)
        material_info_layout.addRow("السعر/م²:", self.material_cost_m2_label)
        material_info_layout.addRow("المورد:", self.material_supplier_label)

        layout.addWidget(material_info_group)

        # ربط الأحداث
        self.materials_list.itemClicked.connect(self.on_material_selected)

        return widget

    def create_pricing_tab(self):
        """إنشاء تبويب التسعير"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # أزرار التسعير
        pricing_buttons = QHBoxLayout()

        calculate_btn = QPushButton("💰 حساب التسعير")
        calculate_btn.clicked.connect(self.calculate_pricing)

        update_costs_btn = QPushButton("🔄 تحديث التكاليف")
        update_costs_btn.clicked.connect(self.update_component_costs)

        pricing_buttons.addWidget(calculate_btn)
        pricing_buttons.addWidget(update_costs_btn)

        layout.addLayout(pricing_buttons)

        # تفاصيل التسعير
        pricing_group = QGroupBox("تفاصيل التسعير")
        pricing_layout = QFormLayout(pricing_group)

        self.materials_cost_label = QLabel("0.00 ريال")
        self.labor_cost_label = QLabel("0.00 ريال")
        self.overhead_cost_label = QLabel("0.00 ريال")
        self.profit_margin_label = QLabel("0.00 ريال")
        self.final_price_label = QLabel("0.00 ريال")

        # تنسيق التسميات
        self.final_price_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #007bff;")

        pricing_layout.addRow("تكلفة المواد:", self.materials_cost_label)
        pricing_layout.addRow("تكلفة العمالة:", self.labor_cost_label)
        pricing_layout.addRow("المصاريف العامة:", self.overhead_cost_label)
        pricing_layout.addRow("هامش الربح:", self.profit_margin_label)
        pricing_layout.addRow("السعر النهائي:", self.final_price_label)

        layout.addWidget(pricing_group)

        # إعدادات التسعير
        settings_group = QGroupBox("إعدادات التسعير")
        settings_layout = QFormLayout(settings_group)

        self.profit_margin_spin = QDoubleSpinBox()
        self.profit_margin_spin.setRange(0.0, 1.0)
        self.profit_margin_spin.setValue(0.25)
        self.profit_margin_spin.setSingleStep(0.05)
        self.profit_margin_spin.setSuffix("%")
        self.profit_margin_spin.valueChanged.connect(self.calculate_pricing)

        self.labor_rate_spin = QDoubleSpinBox()
        self.labor_rate_spin.setRange(0.0, 2.0)
        self.labor_rate_spin.setValue(0.4)
        self.labor_rate_spin.setSingleStep(0.1)
        self.labor_rate_spin.setSuffix("%")

        self.overhead_rate_spin = QDoubleSpinBox()
        self.overhead_rate_spin.setRange(0.0, 1.0)
        self.overhead_rate_spin.setValue(0.15)
        self.overhead_rate_spin.setSingleStep(0.05)
        self.overhead_rate_spin.setSuffix("%")

        settings_layout.addRow("هامش الربح:", self.profit_margin_spin)
        settings_layout.addRow("معدل العمالة:", self.labor_rate_spin)
        settings_layout.addRow("المصاريف العامة:", self.overhead_rate_spin)

        layout.addWidget(settings_group)
        layout.addStretch()

        return widget

    def create_optimization_tab(self):
        """إنشاء تبويب تحسين القطع"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # أزرار التحسين
        optimization_buttons = QHBoxLayout()

        optimize_btn = QPushButton("⚡ تحسين القطع")
        optimize_btn.clicked.connect(self.optimize_cutting)

        clear_btn = QPushButton("🗑️ مسح النتائج")
        clear_btn.clicked.connect(self.clear_optimization_results)

        optimization_buttons.addWidget(optimize_btn)
        optimization_buttons.addWidget(clear_btn)

        layout.addLayout(optimization_buttons)

        # إعدادات التحسين
        settings_group = QGroupBox("إعدادات التحسين")
        settings_layout = QFormLayout(settings_group)

        self.sheet_length_spin = QDoubleSpinBox()
        self.sheet_length_spin.setRange(100, 5000)
        self.sheet_length_spin.setValue(2440)
        self.sheet_length_spin.setSuffix(" مم")

        self.sheet_width_spin = QDoubleSpinBox()
        self.sheet_width_spin.setRange(100, 3000)
        self.sheet_width_spin.setValue(1220)
        self.sheet_width_spin.setSuffix(" مم")

        self.kerf_width_spin = QDoubleSpinBox()
        self.kerf_width_spin.setRange(1.0, 10.0)
        self.kerf_width_spin.setValue(3.0)
        self.kerf_width_spin.setSuffix(" مم")

        settings_layout.addRow("طول اللوح:", self.sheet_length_spin)
        settings_layout.addRow("عرض اللوح:", self.sheet_width_spin)
        settings_layout.addRow("عرض القطع:", self.kerf_width_spin)

        layout.addWidget(settings_group)

        # نتائج التحسين
        results_group = QGroupBox("نتائج التحسين")
        results_layout = QVBoxLayout(results_group)

        # إحصائيات سريعة
        stats_layout = QHBoxLayout()

        self.total_sheets_label = QLabel("الألواح: 0")
        self.avg_efficiency_label = QLabel("الكفاءة: 0%")
        self.total_waste_label = QLabel("الهدر: 0 مم²")

        stats_layout.addWidget(self.total_sheets_label)
        stats_layout.addWidget(self.avg_efficiency_label)
        stats_layout.addWidget(self.total_waste_label)

        results_layout.addLayout(stats_layout)

        # جدول نتائج التحسين
        self.optimization_table = QTableWidget()
        self.optimization_table.setColumnCount(6)
        self.optimization_table.setHorizontalHeaderLabels([
            "اللوح", "المادة", "السماكة", "القطع", "الكفاءة", "الهدر"
        ])

        results_layout.addWidget(self.optimization_table)

        layout.addWidget(results_group)

        return widget

    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إحصائيات المشروع
        project_stats_group = QGroupBox("إحصائيات المشروع")
        project_stats_layout = QFormLayout(project_stats_group)

        self.total_components_label = QLabel("0")
        self.total_pieces_label = QLabel("0")
        self.total_volume_label = QLabel("0.000 م³")
        self.total_area_label = QLabel("0.00 م²")
        self.estimated_weight_label = QLabel("0.0 كجم")

        project_stats_layout.addRow("عدد المكونات:", self.total_components_label)
        project_stats_layout.addRow("إجمالي القطع:", self.total_pieces_label)
        project_stats_layout.addRow("الحجم الإجمالي:", self.total_volume_label)
        project_stats_layout.addRow("المساحة الإجمالية:", self.total_area_label)
        project_stats_layout.addRow("الوزن المقدر:", self.estimated_weight_label)

        layout.addWidget(project_stats_group)

        # إحصائيات المواد
        materials_stats_group = QGroupBox("إحصائيات المواد")
        materials_stats_layout = QVBoxLayout(materials_stats_group)

        self.materials_breakdown_list = QListWidget()
        materials_stats_layout.addWidget(self.materials_breakdown_list)

        layout.addWidget(materials_stats_group)

        layout.addStretch()

        return widget

    def apply_modern_style(self):
        """تطبيق النمط الحديث"""
        style = """
        QMainWindow {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #f8f9fa, stop: 1 #e9ecef);
            color: #212529;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
        }

        QTabWidget::pane {
            border: 2px solid #dee2e6;
            background-color: white;
            border-radius: 8px;
            margin-top: 5px;
        }

        QTabBar::tab {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #ffffff, stop: 1 #f8f9fa);
            border: 2px solid #dee2e6;
            padding: 12px 20px;
            margin-right: 3px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            font-weight: 600;
            color: #495057;
            min-width: 80px;
        }

        QTabBar::tab:selected {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #007bff, stop: 1 #0056b3);
            color: white;
            border-bottom-color: white;
            font-weight: bold;
        }

        QTabBar::tab:hover:!selected {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #e3f2fd, stop: 1 #bbdefb);
            color: #1976d2;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            margin-top: 1ex;
            padding-top: 15px;
            background-color: white;
            color: #495057;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            background-color: white;
            color: #007bff;
            font-size: 14px;
            font-weight: bold;
        }

        QPushButton {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #007bff, stop: 1 #0056b3);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 13px;
            min-height: 20px;
        }

        QPushButton:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #0056b3, stop: 1 #004085);
        }

        QPushButton:pressed {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #004085, stop: 1 #002752);
        }

        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            selection-background-color: #007bff;
        }

        QHeaderView::section {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #f8f9fa, stop: 1 #e9ecef);
            padding: 8px;
            border: 1px solid #dee2e6;
            font-weight: bold;
            color: #495057;
        }

        QListWidget {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: white;
            alternate-background-color: #f8f9fa;
            selection-background-color: #007bff;
            padding: 5px;
        }

        QListWidget::item {
            padding: 8px;
            border-radius: 4px;
            margin: 2px;
        }

        QListWidget::item:hover {
            background-color: #e3f2fd;
        }

        QListWidget::item:selected {
            background-color: #007bff;
            color: white;
        }

        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px;
            background-color: white;
            font-size: 13px;
        }

        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border-color: #007bff;
            outline: none;
        }

        QStatusBar {
            background-color: #343a40;
            color: white;
            border-top: 1px solid #495057;
            padding: 5px;
        }

        QMenuBar {
            background-color: #343a40;
            color: white;
            border-bottom: 1px solid #495057;
        }

        QMenuBar::item {
            padding: 8px 16px;
            background-color: transparent;
        }

        QMenuBar::item:selected {
            background-color: #495057;
        }

        QToolBar {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #ffffff, stop: 1 #f8f9fa);
            border-bottom: 1px solid #dee2e6;
            spacing: 5px;
            padding: 5px;
        }
        """
        self.setStyleSheet(style)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        # تحميل قائمة المشاريع
        self.refresh_projects_list()

        # تحميل قائمة المواد
        self.refresh_materials_list()

        # إنشاء مشروع جديد افتراضي
        self.new_project()

    def refresh_projects_list(self):
        """تحديث قائمة المشاريع"""
        self.projects_list.clear()
        projects = self.db_manager.get_all_projects()

        for project in projects:
            item_text = f"{project['name']} - {project['client_name']} ({project['created_date'][:10]})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, project['id'])
            self.projects_list.addItem(item)

    def refresh_materials_list(self):
        """تحديث قائمة المواد"""
        self.materials_list.clear()
        materials = self.db_manager.get_materials()

        for material in materials:
            item_text = f"{material['name']} - {material['cost_per_m3']:.0f} ريال/م³"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, material)
            self.materials_list.addItem(item)

    def new_project(self):
        """إنشاء مشروع جديد"""
        # إنشاء مشروع فارغ
        project_id = f"PRJ_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.current_project = Project(
            id=project_id,
            name="مشروع جديد",
            client_name="",
            client_phone="",
            description="",
            components=[],
            created_date=datetime.now().isoformat()
        )

        # تحديث الواجهة
        self.update_project_ui()
        self.status_bar.showMessage("تم إنشاء مشروع جديد")

    def save_project(self):
        """حفظ المشروع الحالي"""
        if not self.current_project:
            QMessageBox.warning(self, "تحذير", "لا يوجد مشروع للحفظ")
            return

        # تحديث بيانات المشروع من الواجهة
        self.update_project_from_ui()

        # حفظ في قاعدة البيانات
        if self.db_manager.save_project(self.current_project):
            self.status_bar.showMessage(f"تم حفظ المشروع: {self.current_project.name}")
            self.refresh_projects_list()
        else:
            QMessageBox.critical(self, "خطأ", "فشل في حفظ المشروع")

    def open_project(self):
        """فتح مشروع موجود"""
        self.load_project_dialog()

    def load_project_dialog(self):
        """حوار تحميل مشروع"""
        projects = self.db_manager.get_all_projects()
        if not projects:
            QMessageBox.information(self, "معلومات", "لا توجد مشاريع محفوظة")
            return

        # إنشاء حوار الاختيار
        dialog = QDialog(self)
        dialog.setWindowTitle("اختيار مشروع")
        dialog.setModal(True)
        dialog.resize(500, 400)

        layout = QVBoxLayout(dialog)

        # قائمة المشاريع
        projects_list = QListWidget()
        for project in projects:
            item_text = f"{project['name']} - {project['client_name']} ({project['created_date'][:10]})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, project['id'])
            projects_list.addItem(item)

        layout.addWidget(projects_list)

        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        # عرض الحوار
        if dialog.exec_() == QDialog.Accepted:
            current_item = projects_list.currentItem()
            if current_item:
                project_id = current_item.data(Qt.UserRole)
                self.load_project(project_id)

    def load_project(self, project_id: str):
        """تحميل مشروع محدد"""
        project = self.db_manager.load_project(project_id)
        if project:
            self.current_project = project
            self.update_project_ui()
            self.status_bar.showMessage(f"تم تحميل المشروع: {project.name}")
        else:
            QMessageBox.critical(self, "خطأ", "فشل في تحميل المشروع")

    def load_selected_project(self, item):
        """تحميل المشروع المختار من القائمة"""
        project_id = item.data(Qt.UserRole)
        if project_id:
            self.load_project(project_id)

    def update_project_ui(self):
        """تحديث واجهة المشروع"""
        if not self.current_project:
            return

        # تحديث حقول المشروع
        self.project_name_edit.setText(self.current_project.name)
        self.client_name_edit.setText(self.current_project.client_name)
        self.client_phone_edit.setText(self.current_project.client_phone)
        self.project_desc_edit.setPlainText(self.current_project.description)

        # تحديث جدول المكونات
        self.refresh_components_table()

        # تحديث الإحصائيات
        self.update_statistics()

        # تحديث التسعير
        self.calculate_pricing()

    def update_project_from_ui(self):
        """تحديث بيانات المشروع من الواجهة"""
        if not self.current_project:
            return

        self.current_project.name = self.project_name_edit.text()
        self.current_project.client_name = self.client_name_edit.text()
        self.current_project.client_phone = self.client_phone_edit.text()
        self.current_project.description = self.project_desc_edit.toPlainText()

    def refresh_components_table(self):
        """تحديث جدول المكونات"""
        if not self.current_project:
            return

        self.components_table.setRowCount(len(self.current_project.components))

        for row, component in enumerate(self.current_project.components):
            self.components_table.setItem(row, 0, QTableWidgetItem(component.name))
            self.components_table.setItem(row, 1, QTableWidgetItem(str(component.length)))
            self.components_table.setItem(row, 2, QTableWidgetItem(str(component.width)))
            self.components_table.setItem(row, 3, QTableWidgetItem(str(component.thickness)))
            self.components_table.setItem(row, 4, QTableWidgetItem(component.material))
            self.components_table.setItem(row, 5, QTableWidgetItem(str(component.quantity)))
            self.components_table.setItem(row, 6, QTableWidgetItem(component.edge_banding))
            self.components_table.setItem(row, 7, QTableWidgetItem(f"{component.total_cost:.2f}"))

    def add_component(self):
        """إضافة مكون جديد"""
        if not self.current_project:
            QMessageBox.warning(self, "تحذير", "يجب إنشاء مشروع أولاً")
            return

        # إنشاء حوار إضافة مكون
        dialog = ComponentDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            component_data = dialog.get_component_data()

            # إنشاء مكون جديد
            component_id = f"COMP_{len(self.current_project.components) + 1:03d}"
            component = Component(
                id=component_id,
                name=component_data['name'],
                length=component_data['length'],
                width=component_data['width'],
                thickness=component_data['thickness'],
                material=component_data['material'],
                quantity=component_data['quantity'],
                edge_banding=component_data['edge_banding'],
                notes=component_data['notes']
            )

            # حساب التكلفة
            component.cost_per_unit = self.pricing_calculator.calculate_component_cost(component) / component.quantity

            # إضافة للمشروع
            self.current_project.components.append(component)

            # تحديث الواجهة
            self.refresh_components_table()
            self.update_statistics()
            self.calculate_pricing()

            self.status_bar.showMessage(f"تم إضافة المكون: {component.name}")

    def edit_component(self):
        """تعديل مكون موجود"""
        current_row = self.components_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مكون للتعديل")
            return

        component = self.current_project.components[current_row]

        # إنشاء حوار التعديل
        dialog = ComponentDialog(self, component)
        if dialog.exec_() == QDialog.Accepted:
            component_data = dialog.get_component_data()

            # تحديث بيانات المكون
            component.name = component_data['name']
            component.length = component_data['length']
            component.width = component_data['width']
            component.thickness = component_data['thickness']
            component.material = component_data['material']
            component.quantity = component_data['quantity']
            component.edge_banding = component_data['edge_banding']
            component.notes = component_data['notes']

            # إعادة حساب التكلفة
            component.cost_per_unit = self.pricing_calculator.calculate_component_cost(component) / component.quantity

            # تحديث الواجهة
            self.refresh_components_table()
            self.update_statistics()
            self.calculate_pricing()

            self.status_bar.showMessage(f"تم تعديل المكون: {component.name}")

    def delete_component(self):
        """حذف مكون"""
        current_row = self.components_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مكون للحذف")
            return

        component = self.current_project.components[current_row]

        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المكون: {component.name}؟",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # حذف المكون
            del self.current_project.components[current_row]

            # تحديث الواجهة
            self.refresh_components_table()
            self.update_statistics()
            self.calculate_pricing()

            self.status_bar.showMessage(f"تم حذف المكون: {component.name}")

    def duplicate_component(self):
        """نسخ مكون"""
        current_row = self.components_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مكون للنسخ")
            return

        original_component = self.current_project.components[current_row]

        # إنشاء نسخة
        component_id = f"COMP_{len(self.current_project.components) + 1:03d}"
        new_component = Component(
            id=component_id,
            name=f"{original_component.name} - نسخة",
            length=original_component.length,
            width=original_component.width,
            thickness=original_component.thickness,
            material=original_component.material,
            quantity=original_component.quantity,
            edge_banding=original_component.edge_banding,
            notes=original_component.notes,
            cost_per_unit=original_component.cost_per_unit
        )

        # إضافة للمشروع
        self.current_project.components.append(new_component)

        # تحديث الواجهة
        self.refresh_components_table()
        self.update_statistics()
        self.calculate_pricing()

        self.status_bar.showMessage(f"تم نسخ المكون: {new_component.name}")

    def calculate_pricing(self):
        """حساب التسعير"""
        if not self.current_project or not self.current_project.components:
            # مسح التسعير
            self.materials_cost_label.setText("0.00 ريال")
            self.labor_cost_label.setText("0.00 ريال")
            self.overhead_cost_label.setText("0.00 ريال")
            self.profit_margin_label.setText("0.00 ريال")
            self.final_price_label.setText("0.00 ريال")
            return

        # حساب التسعير
        pricing = self.pricing_calculator.calculate_project_pricing(self.current_project)

        # تحديث التسميات
        self.materials_cost_label.setText(f"{pricing['materials_cost']:.2f} ريال")
        self.labor_cost_label.setText(f"{pricing['labor_cost']:.2f} ريال")
        self.overhead_cost_label.setText(f"{pricing['overhead_cost']:.2f} ريال")
        self.profit_margin_label.setText(f"{pricing['profit_margin']:.2f} ريال")
        self.final_price_label.setText(f"{pricing['final_price']:.2f} ريال")

        # تحديث تكلفة المشروع
        self.current_project.total_cost = pricing['final_price']

    def update_component_costs(self):
        """تحديث تكاليف المكونات"""
        if not self.current_project:
            return

        # إعادة تحميل أسعار المواد
        self.pricing_calculator.load_materials()

        # إعادة حساب تكلفة كل مكون
        for component in self.current_project.components:
            component.cost_per_unit = self.pricing_calculator.calculate_component_cost(component) / component.quantity

        # تحديث الواجهة
        self.refresh_components_table()
        self.calculate_pricing()

        self.status_bar.showMessage("تم تحديث تكاليف المكونات")

    def optimize_cutting(self):
        """تحسين القطع"""
        if not self.current_project or not self.current_project.components:
            QMessageBox.warning(self, "تحذير", "لا توجد مكونات لتحسين القطع")
            return

        # الحصول على إعدادات التحسين
        sheet_length = self.sheet_length_spin.value()
        sheet_width = self.sheet_width_spin.value()

        # تحسين القطع
        optimization_result = self.cutting_optimizer.optimize_cutting(
            self.current_project.components, sheet_length, sheet_width
        )

        # عرض النتائج
        self.display_optimization_results(optimization_result)

        self.status_bar.showMessage(f"تم تحسين القطع - {optimization_result['total_sheets']} لوح، كفاءة {optimization_result['average_efficiency']:.1%}")

    def display_optimization_results(self, result):
        """عرض نتائج التحسين"""
        # تحديث الإحصائيات
        self.total_sheets_label.setText(f"الألواح: {result['total_sheets']}")
        self.avg_efficiency_label.setText(f"الكفاءة: {result['average_efficiency']:.1%}")
        self.total_waste_label.setText(f"الهدر: {result['total_waste']:.0f} مم²")

        # تحديث الجدول
        cutting_plans = result['cutting_plans']
        self.optimization_table.setRowCount(len(cutting_plans))

        for row, plan in enumerate(cutting_plans):
            self.optimization_table.setItem(row, 0, QTableWidgetItem(f"لوح {plan['sheet_number']}"))
            self.optimization_table.setItem(row, 1, QTableWidgetItem(plan['material']))
            self.optimization_table.setItem(row, 2, QTableWidgetItem(f"{plan['thickness']} مم"))
            self.optimization_table.setItem(row, 3, QTableWidgetItem(str(len(plan['pieces']))))
            self.optimization_table.setItem(row, 4, QTableWidgetItem(f"{plan['efficiency']:.1%}"))
            self.optimization_table.setItem(row, 5, QTableWidgetItem(f"{plan['waste_area']:.0f} مم²"))

    def clear_optimization_results(self):
        """مسح نتائج التحسين"""
        self.total_sheets_label.setText("الألواح: 0")
        self.avg_efficiency_label.setText("الكفاءة: 0%")
        self.total_waste_label.setText("الهدر: 0 مم²")
        self.optimization_table.setRowCount(0)

        self.status_bar.showMessage("تم مسح نتائج التحسين")

    def update_statistics(self):
        """تحديث الإحصائيات"""
        if not self.current_project:
            return

        components = self.current_project.components

        # إحصائيات أساسية
        total_components = len(components)
        total_pieces = sum(comp.quantity for comp in components)
        total_volume = sum(comp.volume_m3 * comp.quantity for comp in components)
        total_area = sum(comp.area_m2 * comp.quantity for comp in components)

        # تقدير الوزن (متوسط كثافة 0.65 للخشب)
        estimated_weight = total_volume * 650  # كجم

        # تحديث التسميات
        self.total_components_label.setText(str(total_components))
        self.total_pieces_label.setText(str(total_pieces))
        self.total_volume_label.setText(f"{total_volume:.3f} م³")
        self.total_area_label.setText(f"{total_area:.2f} م²")
        self.estimated_weight_label.setText(f"{estimated_weight:.1f} كجم")

        # إحصائيات المواد
        materials_breakdown = {}
        for comp in components:
            material = comp.material
            if material not in materials_breakdown:
                materials_breakdown[material] = {
                    'count': 0,
                    'volume': 0.0,
                    'cost': 0.0
                }

            materials_breakdown[material]['count'] += comp.quantity
            materials_breakdown[material]['volume'] += comp.volume_m3 * comp.quantity
            materials_breakdown[material]['cost'] += comp.total_cost

        # تحديث قائمة المواد
        self.materials_breakdown_list.clear()
        for material, data in materials_breakdown.items():
            item_text = f"{material}: {data['count']} قطعة، {data['volume']:.3f} م³، {data['cost']:.2f} ريال"
            self.materials_breakdown_list.addItem(item_text)

    def on_material_selected(self, item):
        """عند اختيار مادة"""
        material_data = item.data(Qt.UserRole)
        if material_data:
            self.material_name_label.setText(material_data['name'])
            self.material_cost_m3_label.setText(f"{material_data['cost_per_m3']:.2f} ريال")
            self.material_cost_m2_label.setText(f"{material_data['cost_per_m2']:.2f} ريال")
            self.material_supplier_label.setText(material_data['supplier'])

    def export_to_excel(self):
        """تصدير إلى Excel"""
        if not self.current_project or not self.current_project.components:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات للتصدير")
            return

        # اختيار مكان الحفظ
        filename, _ = QFileDialog.getSaveFileName(
            self, "حفظ ملف Excel",
            f"{self.current_project.name}_cutlist.xlsx",
            "Excel Files (*.xlsx)"
        )

        if filename:
            try:
                # تصدير باستخدام openpyxl
                import openpyxl
                from openpyxl.styles import Font, Alignment, Border, Side, PatternFill

                # إنشاء مصنف جديد
                wb = openpyxl.Workbook()
                ws = wb.active
                ws.title = "جدول القطع"

                # العناوين
                headers = ["المكون", "الطول (مم)", "العرض (مم)", "السماكة (مم)",
                          "المادة", "الكمية", "الحواف", "الملاحظات", "التكلفة"]

                # إضافة العناوين
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=1, col=col, value=header)
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")

                # إضافة البيانات
                for row, component in enumerate(self.current_project.components, 2):
                    ws.cell(row=row, col=1, value=component.name)
                    ws.cell(row=row, col=2, value=component.length)
                    ws.cell(row=row, col=3, value=component.width)
                    ws.cell(row=row, col=4, value=component.thickness)
                    ws.cell(row=row, col=5, value=component.material)
                    ws.cell(row=row, col=6, value=component.quantity)
                    ws.cell(row=row, col=7, value=component.edge_banding)
                    ws.cell(row=row, col=8, value=component.notes)
                    ws.cell(row=row, col=9, value=f"{component.total_cost:.2f}")

                # تعديل عرض الأعمدة
                column_widths = [20, 12, 12, 12, 15, 8, 15, 25, 12]
                for col, width in enumerate(column_widths, 1):
                    ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width

                # إضافة معلومات المشروع
                info_row = len(self.current_project.components) + 3
                ws.cell(row=info_row, col=1, value="معلومات المشروع:").font = Font(bold=True)
                ws.cell(row=info_row + 1, col=1, value=f"اسم المشروع: {self.current_project.name}")
                ws.cell(row=info_row + 2, col=1, value=f"العميل: {self.current_project.client_name}")
                ws.cell(row=info_row + 3, col=1, value=f"التاريخ: {self.current_project.created_date[:10]}")
                ws.cell(row=info_row + 4, col=1, value=f"إجمالي المكونات: {len(self.current_project.components)}")
                ws.cell(row=info_row + 5, col=1, value=f"التكلفة الإجمالية: {self.current_project.total_cost:.2f} ريال")

                # حفظ الملف
                wb.save(filename)

                QMessageBox.information(self, "نجح", f"تم تصدير الملف بنجاح:\n{filename}")
                self.status_bar.showMessage(f"تم تصدير Excel: {filename}")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تصدير الملف:\n{str(e)}")

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(self, "حول البرنامج",
                         "CutList Pro - مصمم الأثاث الاحترافي\n\n"
                         "الإصدار 1.0\n"
                         "تطبيق شامل لتصميم الأثاث وتحسين القطع\n\n"
                         "الميزات:\n"
                         "• إدارة المشاريع والمكونات\n"
                         "• حساب التسعير التلقائي\n"
                         "• تحسين خطط القطع\n"
                         "• تصدير إلى Excel\n"
                         "• قاعدة بيانات متكاملة\n\n"
                         "تطوير: فريق التطوير الاحترافي")


class ComponentDialog(QDialog):
    """حوار إضافة/تعديل مكون"""

    def __init__(self, parent=None, component=None):
        super().__init__(parent)
        self.component = component
        self.init_ui()

        if component:
            self.load_component_data()

    def init_ui(self):
        """تهيئة واجهة الحوار"""
        self.setWindowTitle("إضافة مكون" if not self.component else "تعديل مكون")
        self.setModal(True)
        self.resize(400, 500)

        layout = QVBoxLayout(self)

        # معلومات المكون
        form_layout = QFormLayout()

        # اسم المكون
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم المكون")
        form_layout.addRow("اسم المكون:", self.name_edit)

        # الأبعاد
        dimensions_group = QGroupBox("الأبعاد (مم)")
        dimensions_layout = QFormLayout(dimensions_group)

        self.length_spin = QDoubleSpinBox()
        self.length_spin.setRange(1, 10000)
        self.length_spin.setValue(500)
        self.length_spin.setSuffix(" مم")

        self.width_spin = QDoubleSpinBox()
        self.width_spin.setRange(1, 10000)
        self.width_spin.setValue(300)
        self.width_spin.setSuffix(" مم")

        self.thickness_spin = QDoubleSpinBox()
        self.thickness_spin.setRange(1, 200)
        self.thickness_spin.setValue(18)
        self.thickness_spin.setSuffix(" مم")

        dimensions_layout.addRow("الطول:", self.length_spin)
        dimensions_layout.addRow("العرض:", self.width_spin)
        dimensions_layout.addRow("السماكة:", self.thickness_spin)

        layout.addWidget(dimensions_group)

        # المادة والكمية
        material_group = QGroupBox("المادة والكمية")
        material_layout = QFormLayout(material_group)

        self.material_combo = QComboBox()
        self.material_combo.addItems([
            "خشب طبيعي", "MDF", "خشب رقائقي", "ألمنيوم", "بلاستيك"
        ])

        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 100)
        self.quantity_spin.setValue(1)

        material_layout.addRow("المادة:", self.material_combo)
        material_layout.addRow("الكمية:", self.quantity_spin)

        layout.addWidget(material_group)

        # الحواف والملاحظات
        details_group = QGroupBox("تفاصيل إضافية")
        details_layout = QFormLayout(details_group)

        self.edge_banding_edit = QLineEdit()
        self.edge_banding_edit.setPlaceholderText("مثال: جميع الحواف، الحواف الطولية فقط")

        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")

        details_layout.addRow("الحواف:", self.edge_banding_edit)
        details_layout.addRow("الملاحظات:", self.notes_edit)

        layout.addWidget(details_group)

        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تخصيص النصوص
        buttons.button(QDialogButtonBox.Ok).setText("موافق")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

        # التحقق من صحة البيانات
        buttons.button(QDialogButtonBox.Ok).clicked.connect(self.validate_data)

    def load_component_data(self):
        """تحميل بيانات المكون للتعديل"""
        if not self.component:
            return

        self.name_edit.setText(self.component.name)
        self.length_spin.setValue(self.component.length)
        self.width_spin.setValue(self.component.width)
        self.thickness_spin.setValue(self.component.thickness)

        # تعيين المادة
        material_index = self.material_combo.findText(self.component.material)
        if material_index >= 0:
            self.material_combo.setCurrentIndex(material_index)

        self.quantity_spin.setValue(self.component.quantity)
        self.edge_banding_edit.setText(self.component.edge_banding)
        self.notes_edit.setPlainText(self.component.notes)

    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المكون")
            self.name_edit.setFocus()
            return False

        return True

    def get_component_data(self):
        """الحصول على بيانات المكون"""
        return {
            'name': self.name_edit.text().strip(),
            'length': self.length_spin.value(),
            'width': self.width_spin.value(),
            'thickness': self.thickness_spin.value(),
            'material': self.material_combo.currentText(),
            'quantity': self.quantity_spin.value(),
            'edge_banding': self.edge_banding_edit.text().strip(),
            'notes': self.notes_edit.toPlainText().strip()
        }


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)

    # تعيين معلومات التطبيق
    app.setApplicationName("CutList Pro")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Professional Furniture Design")

    # إنشاء النافذة الرئيسية
    window = CutListProMainWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()