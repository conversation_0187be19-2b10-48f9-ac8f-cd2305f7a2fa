#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Label Printer
طابع الملصقات والباركود
"""

import os
from typing import List, Dict, Any, Optional
from datetime import datetime
import qrcode
from PIL import Image, ImageDraw, ImageFont
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import mm, inch
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont


class LabelPrinter:
    """طابع الملصقات والباركود"""
    
    def __init__(self):
        self.label_templates = {
            'component': {
                'width': 100,  # mm
                'height': 50,  # mm
                'font_size': 10
            },
            'sheet': {
                'width': 150,  # mm
                'height': 100,  # mm
                'font_size': 12
            },
            'project': {
                'width': 200,  # mm
                'height': 100,  # mm
                'font_size': 14
            }
        }
        
        # محاولة تحميل خط عربي
        self.arabic_font = None
        self._load_arabic_font()
    
    def _load_arabic_font(self):
        """تحميل خط عربي"""
        try:
            # البحث عن خط عربي في النظام
            font_paths = [
                'C:/Windows/Fonts/arial.ttf',  # Windows
                '/System/Library/Fonts/Arial.ttf',  # macOS
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    self.arabic_font = 'Arabic'
                    break
            
            if not self.arabic_font:
                self.arabic_font = 'Helvetica'  # خط افتراضي
                
        except Exception as e:
            print(f"تحذير: لا يمكن تحميل الخط العربي: {e}")
            self.arabic_font = 'Helvetica'
    
    def create_component_labels(self, components: List[Dict[str, Any]], 
                               output_path: str = "component_labels.pdf") -> str:
        """إنشاء ملصقات المكونات"""
        
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=10*mm,
            leftMargin=10*mm,
            topMargin=10*mm,
            bottomMargin=10*mm
        )
        
        story = []
        
        # عنوان الصفحة
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontName=self.arabic_font,
            fontSize=16,
            alignment=1,  # وسط
            spaceAfter=20
        )
        
        title = Paragraph("ملصقات المكونات", title_style)
        story.append(title)
        
        # إنشاء جدول الملصقات (3 ملصقات في كل صف)
        labels_per_row = 3
        table_data = []
        current_row = []
        
        for i, component in enumerate(components):
            label_content = self._create_component_label_content(component)
            current_row.append(label_content)
            
            if len(current_row) == labels_per_row:
                table_data.append(current_row)
                current_row = []
        
        # إضافة الصف الأخير إذا لم يكن مكتملاً
        if current_row:
            while len(current_row) < labels_per_row:
                current_row.append("")
            table_data.append(current_row)
        
        if table_data:
            # إنشاء الجدول
            table = Table(table_data, colWidths=[60*mm]*labels_per_row)
            table.setStyle(TableStyle([
                ('BORDER', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('ROWBACKGROUNDS', (0, 0), (-1, -1), [colors.white, colors.lightgrey])
            ]))
            
            story.append(table)
        
        # بناء المستند
        doc.build(story)
        return output_path
    
    def _create_component_label_content(self, component: Dict[str, Any]) -> str:
        """إنشاء محتوى ملصق المكون"""
        name = component.get('name', 'مكون')
        dimensions = component.get('dimensions', {})
        material = component.get('material', 'خشب')
        quantity = component.get('quantity', 1)
        
        # تنسيق الأبعاد
        length = dimensions.get('length', 0)
        width = dimensions.get('width', 0)
        thickness = dimensions.get('thickness', 0)
        
        content = f"""
<b>{name}</b><br/>
الأبعاد: {length:.0f} × {width:.0f} × {thickness:.0f} مم<br/>
المادة: {material}<br/>
الكمية: {quantity}<br/>
التاريخ: {datetime.now().strftime('%Y-%m-%d')}
        """
        
        return content.strip()
    
    def create_cutting_layout_labels(self, layout: Dict[str, Any],
                                   output_path: str = "cutting_labels.pdf") -> str:
        """إنشاء ملصقات تخطيط القطع"""
        
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=10*mm,
            leftMargin=10*mm,
            topMargin=10*mm,
            bottomMargin=10*mm
        )
        
        story = []
        
        # عنوان
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontName=self.arabic_font,
            fontSize=16,
            alignment=1
        )
        
        title = Paragraph("ملصق تخطيط القطع", title_style)
        story.append(title)
        story.append(Spacer(1, 20))
        
        # معلومات اللوح
        sheet = layout.get('sheet', {})
        sheet_info = [
            ['اللوح:', f"{sheet.get('material', 'خشب')} - {sheet.get('thickness', 18)} مم"],
            ['الأبعاد:', f"{sheet.get('length', 0):.0f} × {sheet.get('width', 0):.0f} مم"],
            ['الكفاءة:', f"{layout.get('efficiency', 0):.1%}"],
            ['عدد القطع:', str(len(layout.get('pieces', [])))]
        ]
        
        sheet_table = Table(sheet_info, colWidths=[40*mm, 100*mm])
        sheet_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BORDER', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey)
        ]))
        
        story.append(sheet_table)
        story.append(Spacer(1, 20))
        
        # قائمة القطع
        pieces_title = Paragraph("قائمة القطع:", styles['Heading2'])
        story.append(pieces_title)
        
        pieces_data = [['#', 'اسم القطعة', 'الأبعاد', 'الموضع']]
        
        for i, piece_info in enumerate(layout.get('pieces', []), 1):
            piece = piece_info.get('piece', {})
            x = piece_info.get('x', 0)
            y = piece_info.get('y', 0)
            length = piece_info.get('length', 0)
            width = piece_info.get('width', 0)
            
            pieces_data.append([
                str(i),
                piece.get('name', f'قطعة {i}'),
                f"{length:.0f} × {width:.0f}",
                f"({x:.0f}, {y:.0f})"
            ])
        
        pieces_table = Table(pieces_data, colWidths=[15*mm, 60*mm, 40*mm, 40*mm])
        pieces_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BORDER', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
        ]))
        
        story.append(pieces_table)
        
        # بناء المستند
        doc.build(story)
        return output_path
    
    def create_qr_code(self, data: str, output_path: str = "qr_code.png") -> str:
        """إنشاء رمز QR"""
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            
            qr.add_data(data)
            qr.make(fit=True)
            
            # إنشاء الصورة
            img = qr.make_image(fill_color="black", back_color="white")
            img.save(output_path)
            
            return output_path
            
        except Exception as e:
            print(f"خطأ في إنشاء رمز QR: {e}")
            return ""
    
    def create_project_summary_label(self, project_info: Dict[str, Any],
                                   output_path: str = "project_label.pdf") -> str:
        """إنشاء ملصق ملخص المشروع"""
        
        doc = SimpleDocTemplate(
            output_path,
            pagesize=(200*mm, 150*mm),  # حجم مخصص للملصق
            rightMargin=10*mm,
            leftMargin=10*mm,
            topMargin=10*mm,
            bottomMargin=10*mm
        )
        
        story = []
        styles = getSampleStyleSheet()
        
        # عنوان المشروع
        title_style = ParagraphStyle(
            'ProjectTitle',
            parent=styles['Heading1'],
            fontName=self.arabic_font,
            fontSize=18,
            alignment=1,
            spaceAfter=15
        )
        
        project_name = project_info.get('name', 'مشروع')
        title = Paragraph(f"مشروع: {project_name}", title_style)
        story.append(title)
        
        # معلومات المشروع
        info_data = [
            ['العميل:', project_info.get('client_name', '-')],
            ['التاريخ:', project_info.get('date', datetime.now().strftime('%Y-%m-%d'))],
            ['عدد المكونات:', str(project_info.get('components_count', 0))],
            ['التكلفة المقدرة:', f"{project_info.get('estimated_cost', 0):.2f} ريال"],
            ['وقت الإنتاج:', f"{project_info.get('production_time', 0)} ساعة"],
            ['الحالة:', project_info.get('status', 'جديد')]
        ]
        
        info_table = Table(info_data, colWidths=[50*mm, 120*mm])
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BORDER', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
        ]))
        
        story.append(info_table)
        
        # إضافة رمز QR إذا كان متاحاً
        project_id = project_info.get('id', '')
        if project_id:
            story.append(Spacer(1, 10))
            
            # إنشاء رمز QR مؤقت
            qr_path = f"temp_qr_{project_id}.png"
            qr_data = f"PROJECT:{project_id}|NAME:{project_name}|DATE:{datetime.now().isoformat()}"
            
            if self.create_qr_code(qr_data, qr_path):
                # إضافة الرمز للمستند (يتطلب مكتبة إضافية)
                qr_note = Paragraph("رمز QR متاح في ملف منفصل", styles['Normal'])
                story.append(qr_note)
                
                # حذف الملف المؤقت
                try:
                    os.remove(qr_path)
                except:
                    pass
        
        # بناء المستند
        doc.build(story)
        return output_path
    
    def create_barcode_labels(self, items: List[Dict[str, Any]],
                            output_path: str = "barcode_labels.pdf") -> str:
        """إنشاء ملصقات باركود"""
        
        doc = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            rightMargin=10*mm,
            leftMargin=10*mm,
            topMargin=10*mm,
            bottomMargin=10*mm
        )
        
        story = []
        
        # عنوان
        styles = getSampleStyleSheet()
        title = Paragraph("ملصقات الباركود", styles['Heading1'])
        story.append(title)
        story.append(Spacer(1, 20))
        
        # إنشاء جدول الملصقات
        labels_data = []
        
        for item in items:
            item_id = item.get('id', '')
            item_name = item.get('name', 'عنصر')
            
            # إنشاء رمز QR للعنصر
            qr_data = f"ITEM:{item_id}|NAME:{item_name}|TYPE:{item.get('type', 'component')}"
            qr_path = f"temp_qr_{item_id}.png"
            
            label_content = f"""
<b>{item_name}</b><br/>
المعرف: {item_id}<br/>
النوع: {item.get('type', 'مكون')}<br/>
QR Code: متاح
            """
            
            labels_data.append([label_content.strip()])
            
            # إنشاء رمز QR
            self.create_qr_code(qr_data, qr_path)
        
        # إنشاء الجدول
        if labels_data:
            table = Table(labels_data, colWidths=[180*mm])
            table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BORDER', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('ROWBACKGROUNDS', (0, 0), (-1, -1), [colors.white, colors.lightgrey])
            ]))
            
            story.append(table)
        
        # بناء المستند
        doc.build(story)
        
        # تنظيف ملفات QR المؤقتة
        for item in items:
            qr_path = f"temp_qr_{item.get('id', '')}.png"
            try:
                if os.path.exists(qr_path):
                    os.remove(qr_path)
            except:
                pass
        
        return output_path


def test_label_printer():
    """اختبار طابع الملصقات"""
    printer = LabelPrinter()
    
    # مكونات تجريبية
    components = [
        {
            'name': 'سطح المكتب',
            'dimensions': {'length': 1200, 'width': 600, 'thickness': 25},
            'material': 'خشب',
            'quantity': 1
        },
        {
            'name': 'ساق المكتب',
            'dimensions': {'length': 720, 'width': 500, 'thickness': 25},
            'material': 'خشب',
            'quantity': 2
        }
    ]
    
    # إنشاء ملصقات المكونات
    labels_path = printer.create_component_labels(components, "test_component_labels.pdf")
    print(f"تم إنشاء ملصقات المكونات: {labels_path}")
    
    # معلومات مشروع تجريبي
    project_info = {
        'id': 'PROJECT_001',
        'name': 'مكتب مكتبي',
        'client_name': 'أحمد محمد',
        'components_count': 2,
        'estimated_cost': 1500.0,
        'production_time': 8,
        'status': 'قيد التنفيذ'
    }
    
    # إنشاء ملصق المشروع
    project_label_path = printer.create_project_summary_label(project_info, "test_project_label.pdf")
    print(f"تم إنشاء ملصق المشروع: {project_label_path}")
    
    # إنشاء رمز QR
    qr_path = printer.create_qr_code("PROJECT:001|NAME:مكتب مكتبي", "test_qr.png")
    if qr_path:
        print(f"تم إنشاء رمز QR: {qr_path}")


if __name__ == "__main__":
    test_label_printer()
