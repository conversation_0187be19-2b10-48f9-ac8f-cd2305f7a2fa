#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Client Management System
نظام إدارة العملاء المتقدم
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *


@dataclass
class Client:
    """عميل"""
    id: str
    name: str
    company: str
    phone: str
    email: str
    address: str
    city: str
    country: str
    tax_number: str
    payment_terms: int  # أيام الدفع
    discount_rate: float  # نسبة الخصم
    credit_limit: float  # حد الائتمان
    notes: str
    created_date: str
    last_contact: str
    total_orders: int = 0
    total_revenue: float = 0.0
    status: str = "نشط"  # نشط، معلق، محظور

    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class ClientContact:
    """تواصل مع العميل"""
    id: str
    client_id: str
    contact_type: str  # مكالمة، إيميل، اجتماع، زيارة
    subject: str
    description: str
    contact_date: str
    follow_up_date: str
    status: str  # مكتمل، معلق، ملغي
    created_by: str


class ClientDatabaseManager:
    """مدير قاعدة بيانات العملاء"""

    def __init__(self, db_path: str = "clients.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """تهيئة قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                company TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                city TEXT,
                country TEXT,
                tax_number TEXT,
                payment_terms INTEGER DEFAULT 30,
                discount_rate REAL DEFAULT 0.0,
                credit_limit REAL DEFAULT 0.0,
                notes TEXT,
                created_date TEXT,
                last_contact TEXT,
                total_orders INTEGER DEFAULT 0,
                total_revenue REAL DEFAULT 0.0,
                status TEXT DEFAULT 'نشط'
            )
        ''')

        # جدول التواصل مع العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_contacts (
                id TEXT PRIMARY KEY,
                client_id TEXT,
                contact_type TEXT,
                subject TEXT,
                description TEXT,
                contact_date TEXT,
                follow_up_date TEXT,
                status TEXT,
                created_by TEXT,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        # جدول عروض الأسعار
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS quotations (
                id TEXT PRIMARY KEY,
                client_id TEXT,
                project_name TEXT,
                quotation_date TEXT,
                valid_until TEXT,
                total_amount REAL,
                discount_amount REAL,
                final_amount REAL,
                status TEXT,
                notes TEXT,
                created_date TEXT,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id TEXT PRIMARY KEY,
                client_id TEXT,
                project_id TEXT,
                invoice_date TEXT,
                due_date TEXT,
                total_amount REAL,
                paid_amount REAL DEFAULT 0.0,
                status TEXT DEFAULT 'معلق',
                payment_method TEXT,
                notes TEXT,
                created_date TEXT,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        conn.commit()
        conn.close()

    def add_client(self, client: Client) -> bool:
        """إضافة عميل جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO clients
                (id, name, company, phone, email, address, city, country,
                 tax_number, payment_terms, discount_rate, credit_limit,
                 notes, created_date, last_contact, total_orders, total_revenue, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                client.id, client.name, client.company, client.phone, client.email,
                client.address, client.city, client.country, client.tax_number,
                client.payment_terms, client.discount_rate, client.credit_limit,
                client.notes, client.created_date, client.last_contact,
                client.total_orders, client.total_revenue, client.status
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إضافة العميل: {e}")
            return False

    def get_all_clients(self) -> List[Dict[str, Any]]:
        """الحصول على جميع العملاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM clients ORDER BY name')
            clients_data = cursor.fetchall()

            clients = []
            columns = [desc[0] for desc in cursor.description]

            for client_data in clients_data:
                client_dict = dict(zip(columns, client_data))
                clients.append(client_dict)

            conn.close()
            return clients

        except Exception as e:
            print(f"خطأ في تحميل العملاء: {e}")
            return []

    def search_clients(self, search_term: str) -> List[Dict[str, Any]]:
        """البحث في العملاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            search_pattern = f"%{search_term}%"
            cursor.execute('''
                SELECT * FROM clients
                WHERE name LIKE ? OR company LIKE ? OR phone LIKE ? OR email LIKE ?
                ORDER BY name
            ''', (search_pattern, search_pattern, search_pattern, search_pattern))

            clients_data = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            clients = []
            for client_data in clients_data:
                client_dict = dict(zip(columns, client_data))
                clients.append(client_dict)

            conn.close()
            return clients

        except Exception as e:
            print(f"خطأ في البحث: {e}")
            return []

    def update_client_stats(self, client_id: str, order_amount: float):
        """تحديث إحصائيات العميل"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE clients
                SET total_orders = total_orders + 1,
                    total_revenue = total_revenue + ?,
                    last_contact = ?
                WHERE id = ?
            ''', (order_amount, datetime.now().isoformat(), client_id))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في تحديث إحصائيات العميل: {e}")
            return False


class ClientManagementDialog(QDialog):
    """حوار إدارة العملاء"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = ClientDatabaseManager()
        self.init_ui()
        self.load_clients()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة العملاء المتقدمة")
        self.setModal(True)
        self.resize(1200, 800)

        layout = QVBoxLayout(self)

        # شريط البحث والأدوات
        toolbar_layout = QHBoxLayout()

        # البحث
        search_label = QLabel("البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم، الشركة، الهاتف، أو الإيميل...")
        self.search_edit.textChanged.connect(self.search_clients)

        # أزرار الإدارة
        add_client_btn = QPushButton("➕ إضافة عميل")
        add_client_btn.clicked.connect(self.add_new_client)

        edit_client_btn = QPushButton("✏️ تعديل")
        edit_client_btn.clicked.connect(self.edit_selected_client)

        delete_client_btn = QPushButton("🗑️ حذف")
        delete_client_btn.clicked.connect(self.delete_selected_client)

        export_btn = QPushButton("📊 تصدير")
        export_btn.clicked.connect(self.export_clients)

        toolbar_layout.addWidget(search_label)
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(add_client_btn)
        toolbar_layout.addWidget(edit_client_btn)
        toolbar_layout.addWidget(delete_client_btn)
        toolbar_layout.addWidget(export_btn)

        layout.addLayout(toolbar_layout)

        # جدول العملاء
        self.clients_table = QTableWidget()
        self.clients_table.setColumnCount(12)
        self.clients_table.setHorizontalHeaderLabels([
            "الاسم", "الشركة", "الهاتف", "الإيميل", "المدينة",
            "شروط الدفع", "نسبة الخصم", "إجمالي الطلبات", "إجمالي الإيرادات",
            "آخر تواصل", "الحالة", "تاريخ الإنشاء"
        ])

        # تعديل عرض الأعمدة
        header = self.clients_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.Stretch)

        self.clients_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.clients_table.setAlternatingRowColors(True)
        self.clients_table.setSortingEnabled(True)

        layout.addWidget(self.clients_table)

        # إحصائيات سريعة
        stats_layout = QHBoxLayout()

        self.total_clients_label = QLabel("إجمالي العملاء: 0")
        self.active_clients_label = QLabel("العملاء النشطون: 0")
        self.total_revenue_label = QLabel("إجمالي الإيرادات: 0.00 ريال")

        stats_layout.addWidget(self.total_clients_label)
        stats_layout.addWidget(self.active_clients_label)
        stats_layout.addWidget(self.total_revenue_label)
        stats_layout.addStretch()

        layout.addLayout(stats_layout)

        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Close)
        buttons.rejected.connect(self.close)
        layout.addWidget(buttons)

    def load_clients(self):
        """تحميل العملاء"""
        clients = self.db_manager.get_all_clients()
        self.populate_table(clients)
        self.update_statistics(clients)

    def populate_table(self, clients: List[Dict[str, Any]]):
        """ملء الجدول بالعملاء"""
        self.clients_table.setRowCount(len(clients))

        for row, client in enumerate(clients):
            self.clients_table.setItem(row, 0, QTableWidgetItem(client.get('name', '')))
            self.clients_table.setItem(row, 1, QTableWidgetItem(client.get('company', '')))
            self.clients_table.setItem(row, 2, QTableWidgetItem(client.get('phone', '')))
            self.clients_table.setItem(row, 3, QTableWidgetItem(client.get('email', '')))
            self.clients_table.setItem(row, 4, QTableWidgetItem(client.get('city', '')))
            self.clients_table.setItem(row, 5, QTableWidgetItem(f"{client.get('payment_terms', 0)} يوم"))
            self.clients_table.setItem(row, 6, QTableWidgetItem(f"{client.get('discount_rate', 0):.1%}"))
            self.clients_table.setItem(row, 7, QTableWidgetItem(str(client.get('total_orders', 0))))
            self.clients_table.setItem(row, 8, QTableWidgetItem(f"{client.get('total_revenue', 0):.2f} ريال"))

            last_contact = client.get('last_contact', '')
            if last_contact:
                last_contact = last_contact[:10]  # تاريخ فقط
            self.clients_table.setItem(row, 9, QTableWidgetItem(last_contact))

            # تلوين الحالة
            status_item = QTableWidgetItem(client.get('status', 'نشط'))
            if client.get('status') == 'نشط':
                status_item.setBackground(QColor("#d4edda"))
            elif client.get('status') == 'معلق':
                status_item.setBackground(QColor("#fff3cd"))
            else:
                status_item.setBackground(QColor("#f8d7da"))

            self.clients_table.setItem(row, 10, status_item)

            created_date = client.get('created_date', '')
            if created_date:
                created_date = created_date[:10]
            self.clients_table.setItem(row, 11, QTableWidgetItem(created_date))

            # حفظ ID العميل في البيانات المخفية
            self.clients_table.item(row, 0).setData(Qt.UserRole, client.get('id'))

    def update_statistics(self, clients: List[Dict[str, Any]]):
        """تحديث الإحصائيات"""
        total_clients = len(clients)
        active_clients = len([c for c in clients if c.get('status') == 'نشط'])
        total_revenue = sum(c.get('total_revenue', 0) for c in clients)

        self.total_clients_label.setText(f"إجمالي العملاء: {total_clients}")
        self.active_clients_label.setText(f"العملاء النشطون: {active_clients}")
        self.total_revenue_label.setText(f"إجمالي الإيرادات: {total_revenue:.2f} ريال")

    def search_clients(self):
        """البحث في العملاء"""
        search_term = self.search_edit.text().strip()
        if search_term:
            clients = self.db_manager.search_clients(search_term)
        else:
            clients = self.db_manager.get_all_clients()

        self.populate_table(clients)
        self.update_statistics(clients)

    def add_new_client(self):
        """إضافة عميل جديد"""
        dialog = ClientEditDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_clients()

    def edit_selected_client(self):
        """تعديل العميل المختار"""
        current_row = self.clients_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
            return

        client_id = self.clients_table.item(current_row, 0).data(Qt.UserRole)
        dialog = ClientEditDialog(self, client_id)
        if dialog.exec_() == QDialog.Accepted:
            self.load_clients()

    def delete_selected_client(self):
        """حذف العميل المختار"""
        current_row = self.clients_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للحذف")
            return

        client_name = self.clients_table.item(current_row, 0).text()
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف العميل: {client_name}؟\n"
            "سيتم حذف جميع البيانات المرتبطة به.",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # TODO: تنفيذ حذف العميل
            QMessageBox.information(self, "تم", "تم حذف العميل بنجاح")
            self.load_clients()

    def export_clients(self):
        """تصدير قائمة العملاء"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "تصدير قائمة العملاء",
            f"clients_list_{datetime.now().strftime('%Y%m%d')}.xlsx",
            "Excel Files (*.xlsx)"
        )

        if filename:
            try:
                import openpyxl
                from openpyxl.styles import Font, Alignment, PatternFill

                wb = openpyxl.Workbook()
                ws = wb.active
                ws.title = "قائمة العملاء"

                # العناوين
                headers = [
                    "الاسم", "الشركة", "الهاتف", "الإيميل", "العنوان", "المدينة",
                    "الدولة", "الرقم الضريبي", "شروط الدفع", "نسبة الخصم",
                    "حد الائتمان", "إجمالي الطلبات", "إجمالي الإيرادات", "الحالة"
                ]

                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=1, col=col, value=header)
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")

                # البيانات
                clients = self.db_manager.get_all_clients()
                for row, client in enumerate(clients, 2):
                    ws.cell(row=row, col=1, value=client.get('name', ''))
                    ws.cell(row=row, col=2, value=client.get('company', ''))
                    ws.cell(row=row, col=3, value=client.get('phone', ''))
                    ws.cell(row=row, col=4, value=client.get('email', ''))
                    ws.cell(row=row, col=5, value=client.get('address', ''))
                    ws.cell(row=row, col=6, value=client.get('city', ''))
                    ws.cell(row=row, col=7, value=client.get('country', ''))
                    ws.cell(row=row, col=8, value=client.get('tax_number', ''))
                    ws.cell(row=row, col=9, value=f"{client.get('payment_terms', 0)} يوم")
                    ws.cell(row=row, col=10, value=f"{client.get('discount_rate', 0):.1%}")
                    ws.cell(row=row, col=11, value=f"{client.get('credit_limit', 0):.2f}")
                    ws.cell(row=row, col=12, value=client.get('total_orders', 0))
                    ws.cell(row=row, col=13, value=f"{client.get('total_revenue', 0):.2f}")
                    ws.cell(row=row, col=14, value=client.get('status', ''))

                # تعديل عرض الأعمدة
                for col in range(1, len(headers) + 1):
                    ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15

                wb.save(filename)
                QMessageBox.information(self, "نجح", f"تم تصدير قائمة العملاء:\n{filename}")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في التصدير:\n{str(e)}")


class ClientEditDialog(QDialog):
    """حوار إضافة/تعديل عميل"""

    def __init__(self, parent=None, client_id=None):
        super().__init__(parent)
        self.client_id = client_id
        self.db_manager = ClientDatabaseManager()
        self.init_ui()

        if client_id:
            self.load_client_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إضافة عميل جديد" if not self.client_id else "تعديل بيانات العميل")
        self.setModal(True)
        self.resize(600, 700)

        layout = QVBoxLayout(self)

        # تبويبات
        tabs = QTabWidget()

        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_info_tab()
        tabs.addTab(basic_tab, "المعلومات الأساسية")

        # تبويب المعلومات المالية
        financial_tab = self.create_financial_info_tab()
        tabs.addTab(financial_tab, "المعلومات المالية")

        # تبويب الملاحظات
        notes_tab = self.create_notes_tab()
        tabs.addTab(notes_tab, "الملاحظات والتفاصيل")

        layout.addWidget(tabs)

        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.save_client)
        buttons.rejected.connect(self.reject)

        buttons.button(QDialogButtonBox.Ok).setText("حفظ")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # المعلومات الشخصية
        personal_group = QGroupBox("المعلومات الشخصية")
        personal_layout = QFormLayout(personal_group)

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("اسم العميل الكامل")

        self.company_edit = QLineEdit()
        self.company_edit.setPlaceholderText("اسم الشركة (اختياري)")

        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("+966 50 123 4567")

        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("<EMAIL>")

        personal_layout.addRow("الاسم الكامل *:", self.name_edit)
        personal_layout.addRow("الشركة:", self.company_edit)
        personal_layout.addRow("رقم الهاتف *:", self.phone_edit)
        personal_layout.addRow("البريد الإلكتروني:", self.email_edit)

        layout.addRow(personal_group)

        # معلومات العنوان
        address_group = QGroupBox("معلومات العنوان")
        address_layout = QFormLayout(address_group)

        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        self.address_edit.setPlaceholderText("العنوان التفصيلي...")

        self.city_edit = QLineEdit()
        self.city_edit.setPlaceholderText("الرياض")

        self.country_combo = QComboBox()
        self.country_combo.addItems([
            "السعودية", "الإمارات", "الكويت", "قطر", "البحرين",
            "عمان", "الأردن", "لبنان", "مصر", "أخرى"
        ])

        self.tax_number_edit = QLineEdit()
        self.tax_number_edit.setPlaceholderText("الرقم الضريبي (اختياري)")

        address_layout.addRow("العنوان:", self.address_edit)
        address_layout.addRow("المدينة:", self.city_edit)
        address_layout.addRow("الدولة:", self.country_combo)
        address_layout.addRow("الرقم الضريبي:", self.tax_number_edit)

        layout.addRow(address_group)

        return widget

    def create_financial_info_tab(self):
        """إنشاء تبويب المعلومات المالية"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # شروط الدفع
        payment_group = QGroupBox("شروط الدفع والائتمان")
        payment_layout = QFormLayout(payment_group)

        self.payment_terms_spin = QSpinBox()
        self.payment_terms_spin.setRange(0, 365)
        self.payment_terms_spin.setValue(30)
        self.payment_terms_spin.setSuffix(" يوم")

        self.discount_rate_spin = QDoubleSpinBox()
        self.discount_rate_spin.setRange(0.0, 50.0)
        self.discount_rate_spin.setValue(0.0)
        self.discount_rate_spin.setSuffix("%")
        self.discount_rate_spin.setSingleStep(0.5)

        self.credit_limit_spin = QDoubleSpinBox()
        self.credit_limit_spin.setRange(0.0, 1000000.0)
        self.credit_limit_spin.setValue(0.0)
        self.credit_limit_spin.setSuffix(" ريال")
        self.credit_limit_spin.setSingleStep(1000.0)

        payment_layout.addRow("مدة الدفع:", self.payment_terms_spin)
        payment_layout.addRow("نسبة الخصم:", self.discount_rate_spin)
        payment_layout.addRow("حد الائتمان:", self.credit_limit_spin)

        layout.addRow(payment_group)

        # حالة العميل
        status_group = QGroupBox("حالة العميل")
        status_layout = QFormLayout(status_group)

        self.status_combo = QComboBox()
        self.status_combo.addItems(["نشط", "معلق", "محظور"])

        status_layout.addRow("الحالة:", self.status_combo)

        layout.addRow(status_group)

        layout.addStretch()

        return widget

    def create_notes_tab(self):
        """إنشاء تبويب الملاحظات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        notes_group = QGroupBox("ملاحظات إضافية")
        notes_layout = QVBoxLayout(notes_group)

        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText(
            "أضف أي ملاحظات مهمة عن العميل:\n"
            "- تفضيلات خاصة\n"
            "- تاريخ التعامل\n"
            "- متطلبات خاصة\n"
            "- معلومات إضافية"
        )

        notes_layout.addWidget(self.notes_edit)
        layout.addWidget(notes_group)

        return widget

    def load_client_data(self):
        """تحميل بيانات العميل للتعديل"""
        # TODO: تحميل بيانات العميل من قاعدة البيانات
        pass

    def save_client(self):
        """حفظ بيانات العميل"""
        # التحقق من صحة البيانات
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم العميل")
            self.name_edit.setFocus()
            return

        if not self.phone_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الهاتف")
            self.phone_edit.setFocus()
            return

        # إنشاء كائن العميل
        client_id = self.client_id or f"CLIENT_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        client = Client(
            id=client_id,
            name=self.name_edit.text().strip(),
            company=self.company_edit.text().strip(),
            phone=self.phone_edit.text().strip(),
            email=self.email_edit.text().strip(),
            address=self.address_edit.toPlainText().strip(),
            city=self.city_edit.text().strip(),
            country=self.country_combo.currentText(),
            tax_number=self.tax_number_edit.text().strip(),
            payment_terms=self.payment_terms_spin.value(),
            discount_rate=self.discount_rate_spin.value() / 100.0,
            credit_limit=self.credit_limit_spin.value(),
            notes=self.notes_edit.toPlainText().strip(),
            created_date=datetime.now().isoformat(),
            last_contact="",
            status=self.status_combo.currentText()
        )

        # حفظ في قاعدة البيانات
        if self.db_manager.add_client(client):
            QMessageBox.information(self, "نجح", "تم حفظ بيانات العميل بنجاح")
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", "فشل في حفظ بيانات العميل")
