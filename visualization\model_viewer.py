#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Model Viewer 3D
عارض النماذج ثلاثية الأبعاد
"""

from typing import List, Dict, Any, Optional
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor


class ModelViewer3D(QWidget):
    """عارض النماذج ثلاثية الأبعاد"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.components = []
        self.rotation_x = 0
        self.rotation_y = 0
        self.zoom = 1.0
        self.offset_x = 0
        self.offset_y = 0
        
        self.setMinimumSize(400, 300)
        self.setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc;")
        
        # إعداد التخطيط
        layout = QVBoxLayout(self)
        self.info_label = QLabel("معاينة ثلاثية الأبعاد")
        self.info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.info_label)
    
    def add_component(self, component: Dict[str, Any]):
        """إضافة مكون للعرض"""
        self.components.append(component)
        self.update()
    
    def clear_components(self):
        """مسح جميع المكونات"""
        self.components.clear()
        self.update()
    
    def set_rotation(self, x: float, y: float):
        """تعيين الدوران"""
        self.rotation_x = x
        self.rotation_y = y
        self.update()
    
    def set_zoom(self, zoom: float):
        """تعيين التكبير"""
        self.zoom = max(0.1, min(5.0, zoom))
        self.update()
    
    def paintEvent(self, event):
        """رسم المعاينة"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # خلفية
        painter.fillRect(self.rect(), QColor("#f8f8f8"))
        
        if not self.components:
            # رسم رسالة فارغة
            painter.setPen(QPen(QColor("#999"), 1))
            painter.drawText(self.rect(), Qt.AlignCenter, "لا توجد مكونات للعرض")
            return
        
        # رسم المكونات (عرض مبسط ثنائي الأبعاد)
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        for i, component in enumerate(self.components):
            self._draw_component(painter, component, center_x, center_y, i)
        
        # رسم معلومات
        self._draw_info(painter)
    
    def _draw_component(self, painter: QPainter, component: Dict[str, Any], 
                       center_x: int, center_y: int, index: int):
        """رسم مكون واحد"""
        # الحصول على الأبعاد
        dimensions = component.get('dimensions', {})
        length = dimensions.get('length', 100) * self.zoom * 0.1
        width = dimensions.get('width', 100) * self.zoom * 0.1
        
        # الموضع
        x = center_x - length // 2 + (index * 20)
        y = center_y - width // 2 + (index * 20)
        
        # اللون حسب المادة
        material = component.get('material', 'خشب')
        color = self._get_material_color(material)
        
        # رسم المستطيل
        painter.setPen(QPen(QColor("#333"), 2))
        painter.setBrush(QBrush(color))
        painter.drawRect(int(x), int(y), int(length), int(width))
        
        # رسم النص
        painter.setPen(QPen(QColor("#000"), 1))
        name = component.get('name', f'مكون {index + 1}')
        painter.drawText(int(x + 5), int(y + 15), name)
    
    def _get_material_color(self, material: str) -> QColor:
        """الحصول على لون المادة"""
        colors = {
            'خشب': QColor("#8B4513"),
            'MDF': QColor("#DEB887"),
            'خشب رقائقي': QColor("#CD853F"),
            'ألمنيوم': QColor("#C0C0C0"),
            'بلاستيك': QColor("#87CEEB")
        }
        return colors.get(material, QColor("#8B4513"))
    
    def _draw_info(self, painter: QPainter):
        """رسم معلومات العرض"""
        info_text = f"المكونات: {len(self.components)} | التكبير: {self.zoom:.1f}x"
        painter.setPen(QPen(QColor("#666"), 1))
        painter.drawText(10, self.height() - 10, info_text)
    
    def mousePressEvent(self, event):
        """بداية السحب"""
        self.last_pos = event.pos()
    
    def mouseMoveEvent(self, event):
        """السحب للدوران"""
        if hasattr(self, 'last_pos'):
            dx = event.x() - self.last_pos.x()
            dy = event.y() - self.last_pos.y()
            
            self.rotation_y += dx * 0.5
            self.rotation_x += dy * 0.5
            
            self.last_pos = event.pos()
            self.update()
    
    def wheelEvent(self, event):
        """التكبير بالعجلة"""
        delta = event.angleDelta().y()
        zoom_factor = 1.1 if delta > 0 else 0.9
        self.set_zoom(self.zoom * zoom_factor)


def test_model_viewer():
    """اختبار عارض النماذج"""
    from PyQt5.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    
    viewer = ModelViewer3D()
    viewer.setWindowTitle("اختبار عارض النماذج ثلاثية الأبعاد")
    viewer.resize(600, 400)
    
    # إضافة مكونات تجريبية
    components = [
        {
            'name': 'سطح المكتب',
            'dimensions': {'length': 1200, 'width': 600, 'thickness': 25},
            'material': 'خشب'
        },
        {
            'name': 'ساق المكتب',
            'dimensions': {'length': 720, 'width': 50, 'thickness': 50},
            'material': 'خشب'
        }
    ]
    
    for component in components:
        viewer.add_component(component)
    
    viewer.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    test_model_viewer()
