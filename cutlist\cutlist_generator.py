#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cut List Generator Module
وحدة توليد جدول القطع
"""

import pandas as pd
from typing import List, Dict, Any
from collections import defaultdict


class CutListGenerator:
    """فئة لتوليد وإدارة جدول القطع"""
    
    def __init__(self):
        self.materials_db = {
            'خشب': {'density': 0.6, 'cost_per_m3': 500, 'color': '#8B4513'},
            'MDF': {'density': 0.75, 'cost_per_m3': 300, 'color': '#DEB887'},
            'خشب رقائقي': {'density': 0.65, 'cost_per_m3': 400, 'color': '#D2691E'},
            'ألمنيوم': {'density': 2.7, 'cost_per_m3': 2000, 'color': '#C0C0C0'},
            'بلاستيك': {'density': 0.9, 'cost_per_m3': 800, 'color': '#FFFFFF'}
        }
    
    def generate_cutlist(self, components: List[Dict[str, Any]], 
                        group_similar: bool = True) -> pd.DataFrame:
        """
        توليد جدول القطع من قائمة المكونات
        
        Args:
            components: قائمة المكونات
            group_similar: تجميع المكونات المتشابهة
            
        Returns:
            DataFrame يحتوي على جدول القطع
        """
        if not components:
            return pd.DataFrame()
        
        # تحضير البيانات
        cutlist_data = []
        
        if group_similar:
            grouped_components = self._group_similar_components(components)
            
            for group_key, group_components in grouped_components.items():
                dimensions = group_components[0]['dimensions']
                cutlist_data.append({
                    'اسم المكون': self._generate_component_name(dimensions, group_components[0].get('type', '')),
                    'الطول (مم)': round(dimensions['length'], 2),
                    'العرض (مم)': round(dimensions['width'], 2),
                    'السماكة (مم)': round(dimensions['thickness'], 2),
                    'نوع المادة': self._suggest_material(dimensions),
                    'الكمية': len(group_components),
                    'الحجم الإجمالي (سم³)': round(self._calculate_total_volume(group_components), 2),
                    'الوزن المقدر (كغ)': round(self._calculate_weight(group_components), 2),
                    'التكلفة المقدرة': round(self._calculate_cost(group_components), 2)
                })
        else:
            for i, component in enumerate(components):
                dimensions = component['dimensions']
                cutlist_data.append({
                    'اسم المكون': component.get('name', f'مكون {i+1}'),
                    'الطول (مم)': round(dimensions['length'], 2),
                    'العرض (مم)': round(dimensions['width'], 2),
                    'السماكة (مم)': round(dimensions['thickness'], 2),
                    'نوع المادة': self._suggest_material(dimensions),
                    'الكمية': 1,
                    'الحجم (سم³)': round(component.get('volume', 0) / 1000, 2),  # تحويل من مم³ إلى سم³
                    'الوزن المقدر (كغ)': round(self._calculate_single_weight(component), 2),
                    'التكلفة المقدرة': round(self._calculate_single_cost(component), 2)
                })
        
        return pd.DataFrame(cutlist_data)
    
    def _group_similar_components(self, components: List[Dict[str, Any]], 
                                tolerance: float = 1.0) -> Dict[str, List[Dict[str, Any]]]:
        """تجميع المكونات المتشابهة في الأبعاد"""
        groups = defaultdict(list)
        
        for component in components:
            dims = component['dimensions']
            # إنشاء مفتاح للتجميع بناءً على الأبعاد (مع تسامح)
            key = (
                round(dims['length'] / tolerance) * tolerance,
                round(dims['width'] / tolerance) * tolerance,
                round(dims['thickness'] / tolerance) * tolerance
            )
            groups[key].append(component)
        
        return dict(groups)
    
    def _generate_component_name(self, dimensions: Dict[str, float], component_type: str) -> str:
        """توليد اسم للمكون بناءً على أبعاده ونوعه"""
        length = dimensions['length']
        width = dimensions['width']
        thickness = dimensions['thickness']
        
        if component_type:
            base_name = component_type
        else:
            # تحديد النوع بناءً على الأبعاد
            if thickness < 10:
                base_name = "لوح رقيق"
            elif thickness < 50:
                base_name = "لوح"
            else:
                base_name = "قطعة"
        
        return f"{base_name} {int(length)}×{int(width)}×{int(thickness)}"
    
    def _suggest_material(self, dimensions: Dict[str, float]) -> str:
        """اقتراح نوع المادة بناءً على الأبعاد"""
        thickness = dimensions['thickness']
        length = dimensions['length']
        width = dimensions['width']
        
        # قواعد بسيطة لاقتراح المادة
        if thickness < 5:
            return "خشب رقائقي"
        elif thickness < 20:
            if max(length, width) > 500:
                return "MDF"
            else:
                return "خشب"
        else:
            return "خشب"
    
    def _calculate_total_volume(self, components: List[Dict[str, Any]]) -> float:
        """حساب الحجم الإجمالي لمجموعة من المكونات"""
        total_volume = 0
        for component in components:
            dims = component['dimensions']
            volume = dims['length'] * dims['width'] * dims['thickness'] / 1000  # تحويل إلى سم³
            total_volume += volume
        return total_volume
    
    def _calculate_weight(self, components: List[Dict[str, Any]]) -> float:
        """حساب الوزن المقدر لمجموعة من المكونات"""
        total_weight = 0
        for component in components:
            total_weight += self._calculate_single_weight(component)
        return total_weight
    
    def _calculate_single_weight(self, component: Dict[str, Any]) -> float:
        """حساب وزن مكون واحد"""
        dims = component['dimensions']
        volume_m3 = (dims['length'] * dims['width'] * dims['thickness']) / (1000**3)  # تحويل إلى م³
        
        # استخدام كثافة افتراضية للخشب
        material = self._suggest_material(dims)
        density = self.materials_db.get(material, {}).get('density', 0.6)
        
        return volume_m3 * density * 1000  # تحويل إلى كيلوغرام
    
    def _calculate_cost(self, components: List[Dict[str, Any]]) -> float:
        """حساب التكلفة المقدرة لمجموعة من المكونات"""
        total_cost = 0
        for component in components:
            total_cost += self._calculate_single_cost(component)
        return total_cost
    
    def _calculate_single_cost(self, component: Dict[str, Any]) -> float:
        """حساب تكلفة مكون واحد"""
        dims = component['dimensions']
        volume_m3 = (dims['length'] * dims['width'] * dims['thickness']) / (1000**3)
        
        material = self._suggest_material(dims)
        cost_per_m3 = self.materials_db.get(material, {}).get('cost_per_m3', 500)
        
        return volume_m3 * cost_per_m3
    
    def get_summary_statistics(self, cutlist_df: pd.DataFrame) -> Dict[str, Any]:
        """حساب إحصائيات ملخصة لجدول القطع"""
        if cutlist_df.empty:
            return {}
        
        summary = {
            'إجمالي المكونات': cutlist_df['الكمية'].sum(),
            'أنواع المكونات المختلفة': len(cutlist_df),
            'إجمالي الحجم (سم³)': cutlist_df.get('الحجم الإجمالي (سم³)', cutlist_df.get('الحجم (سم³)', pd.Series([0]))).sum(),
            'إجمالي الوزن المقدر (كغ)': cutlist_df['الوزن المقدر (كغ)'].sum(),
            'إجمالي التكلفة المقدرة': cutlist_df['التكلفة المقدرة'].sum(),
            'المواد المستخدمة': cutlist_df['نوع المادة'].unique().tolist(),
            'أكبر مكون (الطول)': cutlist_df['الطول (مم)'].max(),
            'أكبر مكون (العرض)': cutlist_df['العرض (مم)'].max(),
            'أكبر سماكة': cutlist_df['السماكة (مم)'].max()
        }
        
        return summary
    
    def optimize_cutting_plan(self, cutlist_df: pd.DataFrame, 
                            sheet_sizes: List[Dict[str, float]] = None) -> Dict[str, Any]:
        """تحسين خطة القطع لتقليل الهدر"""
        if sheet_sizes is None:
            # أحجام الألواح الشائعة (بالمم)
            sheet_sizes = [
                {'length': 2440, 'width': 1220, 'thickness': 18, 'name': 'لوح خشب قياسي'},
                {'length': 2500, 'width': 1250, 'thickness': 15, 'name': 'لوح MDF'},
                {'length': 3000, 'width': 1500, 'thickness': 20, 'name': 'لوح كبير'}
            ]
        
        optimization_plan = {
            'الألواح المطلوبة': [],
            'نسبة الاستغلال': 0,
            'الهدر المتوقع': 0
        }
        
        # خوارزمية بسيطة لتحسين القطع
        for _, row in cutlist_df.iterrows():
            length = row['الطول (مم)']
            width = row['العرض (مم)']
            thickness = row['السماكة (مم)']
            quantity = row['الكمية']
            
            # البحث عن أفضل لوح مناسب
            best_sheet = None
            best_efficiency = 0
            
            for sheet in sheet_sizes:
                if (length <= sheet['length'] and width <= sheet['width'] and 
                    abs(thickness - sheet['thickness']) <= 2):  # تسامح في السماكة
                    
                    # حساب كفاءة الاستغلال
                    efficiency = (length * width) / (sheet['length'] * sheet['width'])
                    if efficiency > best_efficiency:
                        best_efficiency = efficiency
                        best_sheet = sheet
            
            if best_sheet:
                optimization_plan['الألواح المطلوبة'].append({
                    'مكون': row['اسم المكون'],
                    'لوح': best_sheet['name'],
                    'كفاءة الاستغلال': f"{best_efficiency:.2%}",
                    'الكمية المطلوبة': quantity
                })
        
        return optimization_plan


def test_cutlist_generator():
    """اختبار مولد جدول القطع"""
    # بيانات اختبار
    test_components = [
        {
            'name': 'لوح علوي',
            'dimensions': {'length': 1200, 'width': 600, 'thickness': 18},
            'volume': 12960000,
            'type': 'لوح'
        },
        {
            'name': 'لوح جانبي 1',
            'dimensions': {'length': 800, 'width': 400, 'thickness': 18},
            'volume': 5760000,
            'type': 'لوح'
        },
        {
            'name': 'لوح جانبي 2',
            'dimensions': {'length': 800, 'width': 400, 'thickness': 18},
            'volume': 5760000,
            'type': 'لوح'
        }
    ]
    
    generator = CutListGenerator()
    
    # توليد جدول القطع
    cutlist = generator.generate_cutlist(test_components, group_similar=True)
    print("جدول القطع:")
    print(cutlist.to_string(index=False))
    
    # الإحصائيات
    stats = generator.get_summary_statistics(cutlist)
    print(f"\nالإحصائيات: {stats}")
    
    # خطة التحسين
    optimization = generator.optimize_cutting_plan(cutlist)
    print(f"\nخطة التحسين: {optimization}")


if __name__ == "__main__":
    test_cutlist_generator()
