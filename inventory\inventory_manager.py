#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Inventory Manager
مدير المخزون والمواد الخام
"""

import json
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum


class StockStatus(Enum):
    """حالة المخزون"""
    IN_STOCK = "متوفر"
    LOW_STOCK = "مخزون منخفض"
    OUT_OF_STOCK = "نفد المخزون"
    ON_ORDER = "مطلوب"


@dataclass
class InventoryItem:
    """عنصر في المخزون"""
    id: str
    name: str
    material_type: str
    dimensions: Dict[str, float]  # length, width, thickness
    quantity: int
    unit: str  # قطعة، م²، م³
    cost_per_unit: float
    supplier: str = ""
    location: str = ""  # موقع التخزين
    min_stock_level: int = 5
    max_stock_level: int = 100
    last_updated: str = ""
    expiry_date: str = ""  # للمواد القابلة للتلف
    notes: str = ""
    
    def __post_init__(self):
        if not self.last_updated:
            self.last_updated = datetime.now().isoformat()
    
    @property
    def status(self) -> StockStatus:
        """حالة المخزون"""
        if self.quantity <= 0:
            return StockStatus.OUT_OF_STOCK
        elif self.quantity <= self.min_stock_level:
            return StockStatus.LOW_STOCK
        else:
            return StockStatus.IN_STOCK
    
    @property
    def total_value(self) -> float:
        """القيمة الإجمالية"""
        return self.quantity * self.cost_per_unit


@dataclass
class StockMovement:
    """حركة المخزون"""
    id: str
    item_id: str
    movement_type: str  # in, out, adjustment
    quantity: int
    reason: str
    reference: str = ""  # رقم المرجع (فاتورة، طلب، إلخ)
    date: str = ""
    user: str = ""
    
    def __post_init__(self):
        if not self.date:
            self.date = datetime.now().isoformat()


class InventoryManager:
    """مدير المخزون"""
    
    def __init__(self, data_dir: str = "inventory_data"):
        self.data_dir = data_dir
        self.inventory: Dict[str, InventoryItem] = {}
        self.movements: List[StockMovement] = []
        
        self.ensure_data_directory()
        self.load_data()
    
    def ensure_data_directory(self):
        """التأكد من وجود مجلد البيانات"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def load_data(self):
        """تحميل بيانات المخزون"""
        # تحميل المخزون
        inventory_file = os.path.join(self.data_dir, "inventory.json")
        if os.path.exists(inventory_file):
            try:
                with open(inventory_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for item_data in data:
                    item = InventoryItem(**item_data)
                    self.inventory[item.id] = item
                    
            except Exception as e:
                print(f"خطأ في تحميل المخزون: {e}")
        
        # تحميل حركات المخزون
        movements_file = os.path.join(self.data_dir, "movements.json")
        if os.path.exists(movements_file):
            try:
                with open(movements_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for movement_data in data:
                    movement = StockMovement(**movement_data)
                    self.movements.append(movement)
                    
            except Exception as e:
                print(f"خطأ في تحميل حركات المخزون: {e}")
    
    def save_data(self):
        """حفظ بيانات المخزون"""
        try:
            # حفظ المخزون
            inventory_file = os.path.join(self.data_dir, "inventory.json")
            inventory_data = [asdict(item) for item in self.inventory.values()]
            
            with open(inventory_file, 'w', encoding='utf-8') as f:
                json.dump(inventory_data, f, ensure_ascii=False, indent=2)
            
            # حفظ الحركات
            movements_file = os.path.join(self.data_dir, "movements.json")
            movements_data = [asdict(movement) for movement in self.movements]
            
            with open(movements_file, 'w', encoding='utf-8') as f:
                json.dump(movements_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ بيانات المخزون: {e}")
    
    def add_item(self, item: InventoryItem) -> bool:
        """إضافة عنصر جديد للمخزون"""
        try:
            self.inventory[item.id] = item
            
            # تسجيل حركة إدخال
            movement = StockMovement(
                id=self.generate_movement_id(),
                item_id=item.id,
                movement_type="in",
                quantity=item.quantity,
                reason="إضافة عنصر جديد"
            )
            self.movements.append(movement)
            
            self.save_data()
            return True
        except Exception as e:
            print(f"خطأ في إضافة العنصر: {e}")
            return False
    
    def update_stock(self, item_id: str, quantity_change: int, reason: str, reference: str = "") -> bool:
        """تحديث كمية المخزون"""
        try:
            item = self.inventory.get(item_id)
            if not item:
                return False
            
            # تحديث الكمية
            new_quantity = item.quantity + quantity_change
            if new_quantity < 0:
                return False  # لا يمكن أن تكون الكمية سالبة
            
            item.quantity = new_quantity
            item.last_updated = datetime.now().isoformat()
            
            # تسجيل الحركة
            movement_type = "in" if quantity_change > 0 else "out"
            movement = StockMovement(
                id=self.generate_movement_id(),
                item_id=item_id,
                movement_type=movement_type,
                quantity=abs(quantity_change),
                reason=reason,
                reference=reference
            )
            self.movements.append(movement)
            
            self.save_data()
            return True
        except Exception as e:
            print(f"خطأ في تحديث المخزون: {e}")
            return False
    
    def check_availability(self, requirements: List[Dict[str, Any]]) -> Dict[str, Any]:
        """فحص توفر المواد المطلوبة"""
        availability_report = {
            'available': [],
            'insufficient': [],
            'missing': [],
            'total_cost': 0.0
        }
        
        for req in requirements:
            material = req.get('material', '')
            length = req.get('length', 0)
            width = req.get('width', 0)
            thickness = req.get('thickness', 0)
            quantity = req.get('quantity', 1)
            
            # البحث عن المواد المناسبة
            suitable_items = self.find_suitable_materials(material, length, width, thickness)
            
            total_needed = quantity
            cost = 0.0
            
            for item in suitable_items:
                if total_needed <= 0:
                    break
                
                available_qty = min(item.quantity, total_needed)
                total_needed -= available_qty
                cost += available_qty * item.cost_per_unit
                
                if available_qty > 0:
                    availability_report['available'].append({
                        'item': item,
                        'quantity_available': available_qty,
                        'cost': available_qty * item.cost_per_unit
                    })
            
            if total_needed > 0:
                availability_report['insufficient'].append({
                    'requirement': req,
                    'shortage': total_needed
                })
            
            availability_report['total_cost'] += cost
        
        return availability_report
    
    def find_suitable_materials(self, material: str, length: float, width: float, thickness: float) -> List[InventoryItem]:
        """البحث عن المواد المناسبة"""
        suitable = []
        
        for item in self.inventory.values():
            if (item.material_type == material and
                item.quantity > 0 and
                item.dimensions.get('length', 0) >= length and
                item.dimensions.get('width', 0) >= width and
                abs(item.dimensions.get('thickness', 0) - thickness) <= 1):  # تسامح 1 مم في السماكة
                suitable.append(item)
        
        # ترتيب حسب الحجم (الأصغر أولاً لتقليل الهدر)
        suitable.sort(key=lambda x: x.dimensions.get('length', 0) * x.dimensions.get('width', 0))
        
        return suitable
    
    def get_low_stock_items(self) -> List[InventoryItem]:
        """الحصول على العناصر ذات المخزون المنخفض"""
        return [item for item in self.inventory.values() 
                if item.status in [StockStatus.LOW_STOCK, StockStatus.OUT_OF_STOCK]]
    
    def get_expiring_items(self, days: int = 30) -> List[InventoryItem]:
        """الحصول على العناصر التي ستنتهي صلاحيتها قريباً"""
        expiring = []
        cutoff_date = datetime.now() + timedelta(days=days)
        
        for item in self.inventory.values():
            if item.expiry_date:
                try:
                    expiry = datetime.fromisoformat(item.expiry_date)
                    if expiry <= cutoff_date:
                        expiring.append(item)
                except:
                    pass
        
        return expiring
    
    def generate_reorder_report(self) -> Dict[str, Any]:
        """توليد تقرير إعادة الطلب"""
        low_stock = self.get_low_stock_items()
        expiring = self.get_expiring_items()
        
        # تجميع حسب المورد
        suppliers = {}
        for item in low_stock:
            supplier = item.supplier or "غير محدد"
            if supplier not in suppliers:
                suppliers[supplier] = []
            suppliers[supplier].append(item)
        
        return {
            'low_stock_items': low_stock,
            'expiring_items': expiring,
            'suppliers': suppliers,
            'total_reorder_cost': sum(item.cost_per_unit * (item.max_stock_level - item.quantity) 
                                    for item in low_stock)
        }
    
    def get_inventory_value(self) -> Dict[str, float]:
        """حساب قيمة المخزون"""
        total_value = 0.0
        by_material = {}
        
        for item in self.inventory.values():
            value = item.total_value
            total_value += value
            
            material = item.material_type
            if material not in by_material:
                by_material[material] = 0.0
            by_material[material] += value
        
        return {
            'total_value': total_value,
            'by_material': by_material
        }
    
    def generate_item_id(self) -> str:
        """توليد معرف عنصر جديد"""
        return f"INV_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    def generate_movement_id(self) -> str:
        """توليد معرف حركة جديد"""
        return f"MOV_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.movements)}"


def test_inventory_manager():
    """اختبار مدير المخزون"""
    manager = InventoryManager("test_inventory")
    
    # إضافة عناصر تجريبية
    item1 = InventoryItem(
        id=manager.generate_item_id(),
        name="لوح خشب 18مم",
        material_type="خشب",
        dimensions={"length": 2440, "width": 1220, "thickness": 18},
        quantity=10,
        unit="قطعة",
        cost_per_unit=150.0,
        supplier="مورد الخشب المحلي",
        location="مخزن A",
        min_stock_level=3
    )
    
    success = manager.add_item(item1)
    print(f"إضافة العنصر: {'نجح' if success else 'فشل'}")
    
    # فحص التوفر
    requirements = [
        {
            'material': 'خشب',
            'length': 1200,
            'width': 600,
            'thickness': 18,
            'quantity': 2
        }
    ]
    
    availability = manager.check_availability(requirements)
    print(f"تقرير التوفر: {len(availability['available'])} متوفر")
    
    # تحديث المخزون
    success = manager.update_stock(item1.id, -2, "استخدام في مشروع", "PROJECT_001")
    print(f"تحديث المخزون: {'نجح' if success else 'فشل'}")
    
    # قيمة المخزون
    value = manager.get_inventory_value()
    print(f"قيمة المخزون الإجمالية: {value['total_value']:.2f} ريال")
    
    # تنظيف
    import shutil
    if os.path.exists("test_inventory"):
        shutil.rmtree("test_inventory")


if __name__ == "__main__":
    test_inventory_manager()
