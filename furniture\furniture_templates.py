#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Furniture Templates Manager
مدير قوالب الأثاث الجاهزة
"""

import json
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class FurnitureComponent:
    """مكون أثاث واحد"""
    name: str
    length: float
    width: float
    thickness: float
    material: str = "خشب"
    quantity: int = 1
    edge_banding: List[str] = None  # الحواف المطلوب تغليفها
    drilling: List[Dict[str, Any]] = None  # نقاط الحفر
    notes: str = ""
    
    def __post_init__(self):
        if self.edge_banding is None:
            self.edge_banding = []
        if self.drilling is None:
            self.drilling = []


@dataclass
class FurnitureTemplate:
    """قالب أثاث كامل"""
    name: str
    category: str
    description: str
    components: List[FurnitureComponent]
    parameters: Dict[str, Any] = None  # المعاملات القابلة للتخصيص
    assembly_instructions: List[str] = None
    tools_required: List[str] = None
    estimated_time: int = 0  # بالدقائق
    difficulty_level: str = "متوسط"  # سهل، متوسط، صعب
    image_path: str = ""
    created_date: str = ""
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.assembly_instructions is None:
            self.assembly_instructions = []
        if self.tools_required is None:
            self.tools_required = []
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


class FurnitureTemplateManager:
    """مدير قوالب الأثاث"""
    
    def __init__(self, templates_dir: str = "furniture_templates"):
        self.templates_dir = templates_dir
        self.templates: Dict[str, FurnitureTemplate] = {}
        self.categories = set()
        
        self.ensure_templates_directory()
        self.load_templates()
        
        # إذا لم توجد قوالب، أضف القوالب الافتراضية
        if not self.templates:
            self._create_default_templates()
    
    def ensure_templates_directory(self):
        """التأكد من وجود مجلد القوالب"""
        if not os.path.exists(self.templates_dir):
            os.makedirs(self.templates_dir)
    
    def load_templates(self):
        """تحميل جميع القوالب"""
        templates_file = os.path.join(self.templates_dir, "templates.json")
        if os.path.exists(templates_file):
            try:
                with open(templates_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for template_data in data:
                    # تحويل المكونات
                    components = []
                    for comp_data in template_data.get('components', []):
                        component = FurnitureComponent(**comp_data)
                        components.append(component)
                    
                    # إنشاء القالب
                    template_data['components'] = components
                    template = FurnitureTemplate(**template_data)
                    
                    self.templates[template.name] = template
                    self.categories.add(template.category)
                    
            except Exception as e:
                print(f"خطأ في تحميل القوالب: {e}")
    
    def save_templates(self):
        """حفظ جميع القوالب"""
        templates_file = os.path.join(self.templates_dir, "templates.json")
        try:
            # تحويل القوالب إلى قوائم
            templates_data = []
            for template in self.templates.values():
                template_dict = asdict(template)
                templates_data.append(template_dict)
            
            with open(templates_file, 'w', encoding='utf-8') as f:
                json.dump(templates_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ القوالب: {e}")
    
    def get_template(self, name: str) -> Optional[FurnitureTemplate]:
        """الحصول على قالب بالاسم"""
        return self.templates.get(name)
    
    def get_templates_by_category(self, category: str) -> List[FurnitureTemplate]:
        """الحصول على القوالب حسب الفئة"""
        return [template for template in self.templates.values() 
                if template.category == category]
    
    def get_all_categories(self) -> List[str]:
        """الحصول على جميع الفئات"""
        return sorted(list(self.categories))
    
    def add_template(self, template: FurnitureTemplate) -> bool:
        """إضافة قالب جديد"""
        try:
            self.templates[template.name] = template
            self.categories.add(template.category)
            self.save_templates()
            return True
        except Exception as e:
            print(f"خطأ في إضافة القالب: {e}")
            return False
    
    def customize_template(self, template_name: str, parameters: Dict[str, Any]) -> Optional[FurnitureTemplate]:
        """تخصيص قالب بمعاملات جديدة"""
        template = self.get_template(template_name)
        if not template:
            return None
        
        # إنشاء نسخة مخصصة
        customized_components = []
        
        for component in template.components:
            # تطبيق المعاملات على المكون
            new_component = FurnitureComponent(
                name=component.name,
                length=component.length * parameters.get('length_scale', 1.0),
                width=component.width * parameters.get('width_scale', 1.0),
                thickness=component.thickness * parameters.get('thickness_scale', 1.0),
                material=parameters.get('material', component.material),
                quantity=component.quantity,
                edge_banding=component.edge_banding.copy(),
                drilling=component.drilling.copy(),
                notes=component.notes
            )
            customized_components.append(new_component)
        
        # إنشاء قالب مخصص
        customized_template = FurnitureTemplate(
            name=f"{template.name} (مخصص)",
            category=template.category,
            description=f"{template.description} - مخصص",
            components=customized_components,
            parameters=parameters,
            assembly_instructions=template.assembly_instructions.copy(),
            tools_required=template.tools_required.copy(),
            estimated_time=template.estimated_time,
            difficulty_level=template.difficulty_level,
            image_path=template.image_path
        )
        
        return customized_template
    
    def _create_default_templates(self):
        """إنشاء القوالب الافتراضية"""
        
        # قالب طاولة مكتب بسيطة
        desk_components = [
            FurnitureComponent("سطح المكتب", 1200, 600, 25, "خشب", 1, ["الأمام", "الخلف", "اليمين", "اليسار"]),
            FurnitureComponent("الساق الأيسر", 720, 500, 25, "خشب", 1, ["الأمام", "الخلف"]),
            FurnitureComponent("الساق الأيمن", 720, 500, 25, "خشب", 1, ["الأمام", "الخلف"]),
            FurnitureComponent("الرف السفلي", 1150, 450, 18, "خشب", 1, ["الأمام"]),
            FurnitureComponent("الظهر", 1150, 200, 6, "خشب رقائقي", 1),
        ]
        
        desk_template = FurnitureTemplate(
            name="مكتب مكتبي بسيط",
            category="مكاتب",
            description="مكتب مكتبي بسيط بساقين ورف سفلي",
            components=desk_components,
            parameters={
                "length": 1200,
                "width": 600,
                "height": 720,
                "shelf_height": 200
            },
            assembly_instructions=[
                "تجميع الساقين أولاً",
                "تركيب الرف السفلي",
                "تركيب سطح المكتب",
                "تركيب الظهر"
            ],
            tools_required=["مفك براغي", "مثقاب", "مسطرة", "قلم رصاص"],
            estimated_time=120,
            difficulty_level="سهل"
        )
        
        # قالب خزانة ملابس
        wardrobe_components = [
            FurnitureComponent("الجانب الأيسر", 2000, 600, 18, "خشب", 1, ["الأمام"]),
            FurnitureComponent("الجانب الأيمن", 2000, 600, 18, "خشب", 1, ["الأمام"]),
            FurnitureComponent("السقف", 1200, 600, 18, "خشب", 1, ["الأمام"]),
            FurnitureComponent("القاعدة", 1200, 600, 18, "خشب", 1),
            FurnitureComponent("الرف الأوسط", 1164, 580, 18, "خشب", 2, ["الأمام"]),
            FurnitureComponent("الظهر", 1200, 2000, 6, "خشب رقائقي", 1),
            FurnitureComponent("الباب الأيسر", 600, 1982, 18, "خشب", 1, ["جميع الجهات"]),
            FurnitureComponent("الباب الأيمن", 600, 1982, 18, "خشب", 1, ["جميع الجهات"]),
        ]
        
        wardrobe_template = FurnitureTemplate(
            name="خزانة ملابس بابين",
            category="خزائن",
            description="خزانة ملابس بابين مع رفوف داخلية",
            components=wardrobe_components,
            parameters={
                "width": 1200,
                "height": 2000,
                "depth": 600,
                "shelves_count": 2
            },
            assembly_instructions=[
                "تجميع الهيكل الأساسي",
                "تركيب الرفوف الداخلية",
                "تركيب الظهر",
                "تركيب الأبواب والمفصلات"
            ],
            tools_required=["مفك براغي", "مثقاب", "منشار", "مسطرة", "ميزان مائي"],
            estimated_time=240,
            difficulty_level="متوسط"
        )
        
        # قالب كرسي بسيط
        chair_components = [
            FurnitureComponent("المقعد", 450, 450, 25, "خشب", 1, ["جميع الجهات"]),
            FurnitureComponent("الظهر", 450, 400, 25, "خشب", 1, ["جميع الجهات"]),
            FurnitureComponent("الساق الأمامية اليسرى", 450, 50, 50, "خشب", 1),
            FurnitureComponent("الساق الأمامية اليمنى", 450, 50, 50, "خشب", 1),
            FurnitureComponent("الساق الخلفية اليسرى", 850, 50, 50, "خشب", 1),
            FurnitureComponent("الساق الخلفية اليمنى", 850, 50, 50, "خشب", 1),
            FurnitureComponent("الدعامة الأمامية", 350, 50, 25, "خشب", 1),
            FurnitureComponent("الدعامة الخلفية", 350, 50, 25, "خشب", 1),
            FurnitureComponent("الدعامة اليسرى", 350, 50, 25, "خشب", 1),
            FurnitureComponent("الدعامة اليمنى", 350, 50, 25, "خشب", 1),
        ]
        
        chair_template = FurnitureTemplate(
            name="كرسي خشبي بسيط",
            category="كراسي",
            description="كرسي خشبي بسيط مع ظهر ودعامات",
            components=chair_components,
            parameters={
                "seat_width": 450,
                "seat_depth": 450,
                "seat_height": 450,
                "back_height": 400
            },
            assembly_instructions=[
                "تجميع الأرجل والدعامات",
                "تركيب المقعد",
                "تركيب الظهر"
            ],
            tools_required=["مفك براغي", "مثقاب", "منشار", "ورق صنفرة"],
            estimated_time=180,
            difficulty_level="متوسط"
        )
        
        # إضافة القوالب
        self.add_template(desk_template)
        self.add_template(wardrobe_template)
        self.add_template(chair_template)


def test_furniture_templates():
    """اختبار مدير قوالب الأثاث"""
    manager = FurnitureTemplateManager("test_templates")
    
    print("الفئات المتاحة:")
    for category in manager.get_all_categories():
        print(f"- {category}")
    
    print("\nقوالب المكاتب:")
    desk_templates = manager.get_templates_by_category("مكاتب")
    for template in desk_templates:
        print(f"- {template.name}: {len(template.components)} مكون")
    
    # اختبار التخصيص
    if desk_templates:
        template = desk_templates[0]
        custom_params = {
            "length_scale": 1.5,
            "width_scale": 1.2,
            "material": "MDF"
        }
        
        customized = manager.customize_template(template.name, custom_params)
        if customized:
            print(f"\nقالب مخصص: {customized.name}")
            print(f"المكونات: {len(customized.components)}")
    
    # تنظيف
    import shutil
    if os.path.exists("test_templates"):
        shutil.rmtree("test_templates")


if __name__ == "__main__":
    test_furniture_templates()
