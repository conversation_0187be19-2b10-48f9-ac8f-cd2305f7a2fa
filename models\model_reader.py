#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Model Reader Module
وحدة قراءة وتحليل النماذج ثلاثية الأبعاد
"""

import trimesh
import numpy as np
from typing import List, Dict, Any
import os


class ModelReader:
    """فئة لقراءة وتحليل النماذج ثلاثية الأبعاد"""
    
    def __init__(self):
        self.supported_formats = ['.obj', '.stl', '.dae', '.ply', '.off']
    
    def analyze_model(self, file_path: str) -> List[Dict[str, Any]]:
        """
        تحليل النموذج ثلاثي الأبعاد واستخراج المكونات
        
        Args:
            file_path: مسار الملف
            
        Returns:
            قائمة بالمكونات وأبعادها
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"الملف غير موجود: {file_path}")
        
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in self.supported_formats:
            raise ValueError(f"صيغة الملف غير مدعومة: {file_ext}")
        
        try:
            # تحميل النموذج
            mesh = trimesh.load(file_path)
            
            # التحقق من نوع النموذج
            if isinstance(mesh, trimesh.Scene):
                # إذا كان النموذج يحتوي على عدة مكونات
                components = self._analyze_scene(mesh)
            else:
                # إذا كان النموذج مكون واحد
                components = [self._analyze_single_mesh(mesh, "مكون رئيسي")]
            
            return components
            
        except Exception as e:
            raise Exception(f"خطأ في تحليل النموذج: {str(e)}")
    
    def _analyze_scene(self, scene: trimesh.Scene) -> List[Dict[str, Any]]:
        """تحليل مشهد يحتوي على عدة مكونات"""
        components = []
        
        for name, geometry in scene.geometry.items():
            if isinstance(geometry, trimesh.Trimesh):
                component = self._analyze_single_mesh(geometry, name)
                components.append(component)
        
        # إذا لم نجد مكونات منفصلة، نحلل المشهد كمكون واحد
        if not components:
            # دمج جميع المكونات
            combined_mesh = scene.dump(concatenate=True)
            if isinstance(combined_mesh, trimesh.Trimesh):
                components = [self._analyze_single_mesh(combined_mesh, "مكون مدمج")]
        
        return components
    
    def _analyze_single_mesh(self, mesh: trimesh.Trimesh, name: str) -> Dict[str, Any]:
        """تحليل مكون واحد"""
        # حساب الصندوق المحيط
        bounds = mesh.bounds
        dimensions = bounds[1] - bounds[0]  # [max] - [min]
        
        # ترتيب الأبعاد (الطول، العرض، السماكة)
        sorted_dims = np.sort(dimensions)[::-1]  # ترتيب تنازلي
        
        # حساب الحجم والمساحة
        volume = mesh.volume if hasattr(mesh, 'volume') else 0
        area = mesh.area if hasattr(mesh, 'area') else 0
        
        # تحديد نوع المكون بناءً على الأبعاد
        component_type = self._determine_component_type(sorted_dims)
        
        return {
            'name': name,
            'dimensions': {
                'length': float(sorted_dims[0]),      # الطول (أكبر بُعد)
                'width': float(sorted_dims[1]),       # العرض (البُعد المتوسط)
                'thickness': float(sorted_dims[2])    # السماكة (أصغر بُعد)
            },
            'volume': float(volume),
            'area': float(area),
            'type': component_type,
            'bounds': {
                'min': bounds[0].tolist(),
                'max': bounds[1].tolist()
            }
        }
    
    def _determine_component_type(self, dimensions: np.ndarray) -> str:
        """تحديد نوع المكون بناءً على أبعاده"""
        length, width, thickness = dimensions
        
        # نسب الأبعاد
        length_width_ratio = length / width if width > 0 else float('inf')
        width_thickness_ratio = width / thickness if thickness > 0 else float('inf')
        
        # تصنيف المكون
        if thickness < 10:  # أقل من 1 سم
            return "لوح رقيق"
        elif thickness < 50:  # أقل من 5 سم
            if length_width_ratio > 3:
                return "لوح طويل"
            else:
                return "لوح"
        elif width_thickness_ratio < 2:
            return "قطعة مربعة"
        else:
            return "قطعة مستطيلة"
    
    def get_model_info(self, file_path: str) -> Dict[str, Any]:
        """الحصول على معلومات عامة عن النموذج"""
        try:
            mesh = trimesh.load(file_path)
            
            info = {
                'file_name': os.path.basename(file_path),
                'file_size': os.path.getsize(file_path),
                'format': os.path.splitext(file_path)[1].lower()
            }
            
            if isinstance(mesh, trimesh.Scene):
                info.update({
                    'type': 'مشهد متعدد المكونات',
                    'components_count': len(mesh.geometry),
                    'total_volume': sum(geom.volume for geom in mesh.geometry.values() 
                                      if hasattr(geom, 'volume')),
                    'bounds': mesh.bounds.tolist() if hasattr(mesh, 'bounds') else None
                })
            else:
                info.update({
                    'type': 'مكون واحد',
                    'components_count': 1,
                    'vertices_count': len(mesh.vertices),
                    'faces_count': len(mesh.faces),
                    'volume': float(mesh.volume) if hasattr(mesh, 'volume') else 0,
                    'area': float(mesh.area) if hasattr(mesh, 'area') else 0,
                    'bounds': mesh.bounds.tolist()
                })
            
            return info
            
        except Exception as e:
            raise Exception(f"خطأ في قراءة معلومات النموذج: {str(e)}")


def test_model_reader():
    """اختبار وحدة قراءة النماذج"""
    reader = ModelReader()
    
    # إنشاء نموذج اختبار بسيط
    box = trimesh.creation.box(extents=[100, 50, 20])  # صندوق 100x50x20 مم
    
    # حفظ النموذج كملف OBJ مؤقت
    test_file = "test_model.obj"
    box.export(test_file)
    
    try:
        # تحليل النموذج
        components = reader.analyze_model(test_file)
        print("نتائج التحليل:")
        for i, comp in enumerate(components):
            print(f"المكون {i+1}: {comp}")
        
        # معلومات النموذج
        info = reader.get_model_info(test_file)
        print(f"\nمعلومات النموذج: {info}")
        
    finally:
        # حذف الملف المؤقت
        if os.path.exists(test_file):
            os.remove(test_file)


if __name__ == "__main__":
    test_model_reader()
