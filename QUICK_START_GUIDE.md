# 🚀 دليل البدء السريع - CutList Pro

## تطبيق مصمم الأثاث الاحترافي

---

## ⚡ التشغيل السريع (30 ثانية)

### Windows
```bash
# انقر نقراً مزدوجاً على الملف أو شغل من سطر الأوامر
run_quick_test.bat
```

### macOS/Linux
```bash
# اجعل الملف قابلاً للتنفيذ وشغله
chmod +x run_quick_test.sh
./run_quick_test.sh
```

### أو مباشرة
```bash
python quick_test.py
```

---

## 🎯 ما يفعله الاختبار السريع

الاختبار يفحص **7 ميزات رئيسية**:

1. **🪑 قوالب الأثاث** - 3 فئات مع قوالب جاهزة
2. **👥 إدارة العملاء** - إضافة وبحث العملاء
3. **⚡ تحسين القطع** - خوارزميات توفير المواد
4. **💰 محرك التسعير** - حساب التكلفة والعمالة
5. **📦 إدارة المخزون** - تتبع المواد والكميات
6. **🤖 تصدير CNC** - توليد ملفات G-code
7. **🏷️ طباعة الملصقات** - ملصقات وباركود

### النتيجة المتوقعة
```
📊 نتائج الاختبار: 6/7 نجح (85.7%)
🎉 التطبيق جاهز للاستخدام!
```

---

## 🏃‍♂️ التشغيل الكامل

بعد نجاح الاختبار السريع:

```bash
# المشغل التلقائي الذكي (موصى به)
python run_furniture_designer.py
```

المشغل سيقوم بـ:
- ✅ فحص المتطلبات
- ✅ تثبيت المكتبات المفقودة
- ✅ إنشاء البيانات التجريبية
- ✅ تشغيل التطبيق

---

## 🔧 إذا واجهت مشاكل

### مشكلة: Python غير موجود
```bash
# تأكد من تثبيت Python 3.8+
python --version
# أو
python3 --version
```

### مشكلة: مكتبات مفقودة
```bash
# تثبيت المتطلبات الأساسية
pip install PyQt5 trimesh pandas numpy openpyxl reportlab

# أو تثبيت جميع المتطلبات
pip install -r requirements.txt
```

### مشكلة: خطأ في الاستيراد
- تأكد من وجود جميع الملفات في نفس المجلد
- شغل من المجلد الصحيح

---

## 📚 المزيد من المعلومات

- **📖 الدليل الشامل**: `FURNITURE_DESIGNER_README.md`
- **📋 ملخص المشروع**: `PROJECT_COMPLETE_SUMMARY.md`
- **🔧 متطلبات النظام**: `requirements.txt`

---

## 🎉 مبروك!

إذا نجح الاختبار السريع، فأنت الآن تملك:

### ✨ تطبيق احترافي متكامل يتضمن:
- 🪑 **مكتبة قوالب أثاث** شاملة
- ⚡ **محسن قطع متطور** (كفاءة 67.9%)
- 💰 **محرك تسعير ذكي** 
- 👥 **إدارة عملاء ومشاريع**
- 📦 **نظام مخزون متقدم**
- 🤖 **تصدير CNC مباشر**
- 🏷️ **طباعة ملصقات وباركود**

### 🚀 جاهز للاستخدام الفوري!

---

**🪑 CutList Pro - حيث يلتقي التصميم بالتكنولوجيا**

*تطبيق مصمم خصيصاً لمصممي الأثاث الطموحين*
