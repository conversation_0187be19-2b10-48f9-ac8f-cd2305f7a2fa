#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CutList Pro Advanced - Launcher Script
سكريپت تشغيل تطبيق مصمم الأثاث الاحترافي المتقدم
"""

import sys
import os
import subprocess
from pathlib import Path

def check_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية للإصدار المتقدم...")
    
    # فحص Python
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # فحص المكتبات المطلوبة
    required_packages = [
        'PyQt5',
        'sqlite3',
        'openpyxl',
        'datetime',
        'dataclasses',
        'typing'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sqlite3':
                import sqlite3
            elif package == 'PyQt5':
                import PyQt5
                from PyQt5.QtWidgets import QApplication
                from PyQt5.QtCore import Qt
                from PyQt5.QtGui import QFont
            elif package == 'openpyxl':
                import openpyxl
            elif package == 'datetime':
                import datetime
            elif package == 'dataclasses':
                import dataclasses
            elif package == 'typing':
                import typing
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - غير مثبت")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  المكتبات المفقودة: {', '.join(missing_packages)}")
        print("هل تريد تثبيت المكتبات المفقودة؟ (y/n): ", end="")
        
        response = input().lower()
        if response in ['y', 'yes', 'نعم']:
            install_packages(missing_packages)
        else:
            return False
    
    return True

def install_packages(packages):
    """تثبيت المكتبات المفقودة"""
    print("\n📦 تثبيت المكتبات المفقودة...")
    
    for package in packages:
        if package in ['sqlite3', 'datetime', 'dataclasses', 'typing']:
            continue  # مدمجة مع Python
        
        print(f"تثبيت {package}...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"❌ فشل في تثبيت {package}")
            return False
    
    print("✅ تم تثبيت جميع المكتبات بنجاح!")
    return True

def check_advanced_modules():
    """فحص الوحدات المتقدمة"""
    print("\n🔧 فحص الوحدات المتقدمة...")
    
    required_modules = [
        'cutlist_pro_real.py',
        'advanced_client_manager.py',
        'advanced_inventory_manager.py',
        'advanced_quotation_system.py'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        if Path(module).exists():
            print(f"✅ {module}")
        else:
            print(f"❌ {module} - مفقود")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️  الوحدات المفقودة: {', '.join(missing_modules)}")
        print("تأكد من وجود جميع ملفات التطبيق في نفس المجلد")
        return False
    
    return True

def create_databases():
    """إنشاء قواعد البيانات"""
    print("\n💾 إنشاء قواعد البيانات...")
    
    try:
        # إنشاء قاعدة بيانات العملاء
        from advanced_client_manager import ClientDatabaseManager
        client_db = ClientDatabaseManager()
        print("✅ قاعدة بيانات العملاء")
        
        # إنشاء قاعدة بيانات المخزون
        from advanced_inventory_manager import InventoryDatabaseManager
        inventory_db = InventoryDatabaseManager()
        print("✅ قاعدة بيانات المخزون")
        
        # إنشاء قاعدة بيانات عروض الأسعار
        from advanced_quotation_system import QuotationDatabaseManager
        quotation_db = QuotationDatabaseManager()
        print("✅ قاعدة بيانات عروض الأسعار")
        
        print("✅ تم إنشاء جميع قواعد البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قواعد البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🪑 CutList Pro Advanced - مصمم الأثاث الاحترافي المتقدم")
    print("   الإصدار 2.0 - حلم كل مصمم أثاث ومصنع أثاث")
    print("=" * 70)
    
    print("\n🌟 الميزات الجديدة في الإصدار المتقدم:")
    print("   • إدارة العملاء المتقدمة مع قاعدة بيانات شاملة")
    print("   • نظام إدارة المخزون الذكي مع تتبع الحركات")
    print("   • عروض الأسعار الاحترافية مع قوالب جاهزة")
    print("   • تكامل كامل بين جميع الأنظمة")
    print("   • تقارير مالية وإحصائيات متقدمة")
    print("   • تصدير احترافي لجميع البيانات")
    
    # فحص المتطلبات
    if not check_requirements():
        print("\n❌ لا يمكن تشغيل التطبيق بسبب متطلبات مفقودة")
        input("اضغط Enter للخروج...")
        return
    
    # فحص الوحدات المتقدمة
    if not check_advanced_modules():
        print("\n❌ لا يمكن تشغيل التطبيق بسبب وحدات مفقودة")
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء قواعد البيانات
    if not create_databases():
        print("\n⚠️  تحذير: قد تواجه مشاكل في قواعد البيانات")
    
    print("\n🚀 تشغيل التطبيق المتقدم...")
    
    try:
        # تشغيل التطبيق المتقدم
        from cutlist_pro_real import main as run_advanced_app
        run_advanced_app()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print("\n🔧 محاولة تشغيل الإصدار الأساسي...")
        
        try:
            # محاولة تشغيل الإصدار الأساسي
            import cutlist_pro_real
            app = cutlist_pro_real.QApplication(sys.argv)
            window = cutlist_pro_real.CutListProMainWindow()
            window.show()
            sys.exit(app.exec_())
            
        except Exception as e2:
            print(f"❌ خطأ في تشغيل الإصدار الأساسي: {e2}")
            input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
