# CutList Desktop App
## تطبيق سطح مكتب لتوليد جدول القطع

تطبيق سطح مكتب مطور بـ Python و PyQt5 لتوليد جدول قطع (Cut List) من النماذج ثلاثية الأبعاد، مشابه لإضافة OpenCutList في SketchUp.

## الميزات الرئيسية

### 🔧 الوظائف الأساسية
- **استيراد النماذج**: دعم صيغ .obj, .stl, .dae
- **تحليل تلقائي**: استخراج المكونات وحساب الأبعاد
- **جدول القطع**: عرض الطول × العرض × السماكة لكل مكون
- **إدارة المواد**: ربط المكونات بأنواع المواد المختلفة
- **تجميع ذكي**: تجميع المكونات المتشابهة تلقائياً

### 📊 التحليل والإحصائيات
- حساب الحجم الإجمالي
- تقدير الوزن والتكلفة
- إحصائيات مفصلة للمشروع
- اقتراحات تحسين خطة القطع

### 🌐 دعم اللغات
- واجهة باللغة العربية
- دعم مستقبلي للإنجليزية
- معالجة صحيحة للنصوص العربية في التصدير

## التثبيت والتشغيل

### المتطلبات
- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, Linux
- PyQt5 (للواجهة الرسومية)

### خطوات التثبيت

1. **تحميل المشروع**
```bash
# إذا كان لديك git
git clone <repository-url>
cd cutlist-desktop-app

# أو تحميل الملفات مباشرة
```

2. **تثبيت PyQt5 (المطلوب)**
```bash
pip install PyQt5
```

3. **تشغيل الإصدار المبسط (موصى به للبداية)**
```bash
python main_simple.py
```

4. **تثبيت المكتبات الإضافية (اختياري)**
```bash
pip install trimesh pandas numpy openpyxl arabic-reshaper python-bidi
```

5. **تشغيل الإصدار الكامل (بعد تثبيت المكتبات)**
```bash
python main.py
```

## هيكل المشروع

```
cutlist_app/
├── main.py                     # ملف التشغيل الرئيسي
├── requirements.txt            # المكتبات المطلوبة
├── README.md                   # توثيق المشروع
├── models/                     # وحدة تحليل النماذج ثلاثية الأبعاد
│   ├── __init__.py
│   └── model_reader.py
├── cutlist/                    # وحدة توليد جدول القطع
│   ├── __init__.py
│   └── cutlist_generator.py
├── materials/                  # إدارة المواد (قادم)
├── export/                     # تصدير Excel و PDF (قادم)
├── ui/                         # ملفات الواجهة (قادم)
└── assets/                     # الأيقونات والملفات المساعدة
```

## كيفية الاستخدام

### الإصدار المبسط (main_simple.py)

#### 1. إضافة مكونات يدوياً
- انقر على زر "إضافة مكون يدوياً"
- أدخل اسم المكون والأبعاد (الطول × العرض × السماكة)
- اختر نوع المادة والكمية
- انقر "موافق" لإضافة المكون للجدول

#### 2. فتح ملف ثلاثي الأبعاد (تجريبي)
- انقر على زر "فتح ملف ثلاثي الأبعاد"
- اختر أي ملف بصيغة .obj, .stl, أو .dae
- سيتم إضافة مكونات تجريبية للجدول

#### 3. إدارة الجدول
- يمكن مراجعة جميع المكونات في الجدول
- استخدم زر "مسح الجدول" لحذف جميع البيانات
- البيانات تظهر بالتنسيق: الطول × العرض × السماكة بالمليمتر

### الإصدار الكامل (main.py) - يتطلب مكتبات إضافية

#### 1. فتح ملف ثلاثي الأبعاد
- انقر على زر "فتح ملف ثلاثي الأبعاد"
- اختر ملف بصيغة .obj, .stl, أو .dae
- انتظر حتى يكتمل التحليل التلقائي

#### 2. مراجعة النتائج
- ستظهر المكونات في الجدول مع أبعادها الحقيقية
- يتم تجميع المكونات المتشابهة تلقائياً
- يمكن تعديل نوع المادة لكل مكون

#### 3. التصدير (قادم)
- تصدير إلى Excel للتعديل المتقدم
- تصدير إلى PDF للطباعة
- حفظ المشروع للعمل عليه لاحقاً

## المكتبات المستخدمة

| المكتبة | الغرض |
|---------|--------|
| PyQt5 | الواجهة الرسومية |
| trimesh | تحليل النماذج ثلاثية الأبعاد |
| pandas | تنظيم وإدارة البيانات |
| numpy | العمليات الرياضية |
| openpyxl | تصدير Excel |
| reportlab | تصدير PDF |
| arabic-reshaper | دعم النصوص العربية |

## ✅ الميزات الجديدة في الإصدار 2.0

### تم إضافتها حديثاً
- ✅ **تصدير إلى Excel و PDF** - تصدير احترافي مع تنسيق متقدم
- ✅ **إدارة متقدمة للمواد** - قاعدة بيانات شاملة للمواد
- ✅ **حفظ وتحميل المشاريع** - نظام إدارة مشاريع كامل
- ✅ **واجهة متقدمة** - تبويبات وإحصائيات مفصلة
- ✅ **حساب التكلفة والوزن** - تقديرات دقيقة للمواد
- ✅ **تحرير مباشر** - تعديل البيانات في الجدول مباشرة
- ✅ **إحصائيات شاملة** - تحليل مفصل للمشروع

### الميزات القادمة

#### المرحلة التالية
- [ ] مدير المواد المرئي - واجهة لإضافة وتعديل المواد
- [ ] تحسين خطة القطع - خوارزميات تحسين الهدر
- [ ] رسوم بيانية - مخططات للإحصائيات
- [ ] طباعة ملصقات - ملصقات للمكونات مع باركود

#### المراحل المستقبلية
- [ ] دعم صيغ إضافية (.3ds, .fbx, .step)
- [ ] واجهة ثنائية اللغة (عربي/إنجليزي)
- [ ] تكامل مع قواعد بيانات المواد عبر الإنترنت
- [ ] تصدير إلى ماكينات CNC
- [ ] تطبيق ويب مصاحب
- [ ] API للتكامل مع برامج أخرى

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات مع اختبارات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- افتح Issue في GitHub
- راسلنا على البريد الإلكتروني
- راجع الوثائق في Wiki

---

**ملاحظة**: هذا المشروع في مرحلة التطوير النشط. بعض الميزات قد تكون غير مكتملة.
