#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Reports System
نظام التقارير المتقدم
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure


class ReportsManager:
    """مدير التقارير"""
    
    def __init__(self):
        self.projects_db = "cutlist_pro.db"
        self.clients_db = "clients.db"
        self.inventory_db = "inventory.db"
        self.quotations_db = "quotations.db"
    
    def get_financial_summary(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """الحصول على ملخص مالي"""
        try:
            # إيرادات من المشاريع
            conn = sqlite3.connect(self.projects_db)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT SUM(total_cost) FROM projects 
                WHERE created_date BETWEEN ? AND ? AND status = 'مكتمل'
            ''', (start_date, end_date))
            
            projects_revenue = cursor.fetchone()[0] or 0.0
            conn.close()
            
            # إيرادات من عروض الأسعار المقبولة
            conn = sqlite3.connect(self.quotations_db)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT SUM(total_amount) FROM quotations 
                WHERE quotation_date BETWEEN ? AND ? AND status = 'مقبول'
            ''', (start_date, end_date))
            
            quotations_revenue = cursor.fetchone()[0] or 0.0
            conn.close()
            
            # تكلفة المخزون المستخدم
            conn = sqlite3.connect(self.inventory_db)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT SUM(total_amount) FROM stock_movements 
                WHERE movement_date BETWEEN ? AND ? AND movement_type = 'بيع'
            ''', (start_date, end_date))
            
            inventory_cost = cursor.fetchone()[0] or 0.0
            conn.close()
            
            total_revenue = projects_revenue + quotations_revenue
            profit = total_revenue - inventory_cost
            profit_margin = (profit / total_revenue * 100) if total_revenue > 0 else 0
            
            return {
                'total_revenue': total_revenue,
                'projects_revenue': projects_revenue,
                'quotations_revenue': quotations_revenue,
                'inventory_cost': inventory_cost,
                'profit': profit,
                'profit_margin': profit_margin
            }
            
        except Exception as e:
            print(f"خطأ في الحصول على الملخص المالي: {e}")
            return {}
    
    def get_projects_statistics(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """إحصائيات المشاريع"""
        try:
            conn = sqlite3.connect(self.projects_db)
            cursor = conn.cursor()
            
            # إجمالي المشاريع
            cursor.execute('''
                SELECT COUNT(*) FROM projects 
                WHERE created_date BETWEEN ? AND ?
            ''', (start_date, end_date))
            total_projects = cursor.fetchone()[0]
            
            # المشاريع حسب الحالة
            cursor.execute('''
                SELECT status, COUNT(*) FROM projects 
                WHERE created_date BETWEEN ? AND ?
                GROUP BY status
            ''', (start_date, end_date))
            status_counts = dict(cursor.fetchall())
            
            # متوسط قيمة المشروع
            cursor.execute('''
                SELECT AVG(total_cost) FROM projects 
                WHERE created_date BETWEEN ? AND ? AND total_cost > 0
            ''', (start_date, end_date))
            avg_project_value = cursor.fetchone()[0] or 0.0
            
            conn.close()
            
            return {
                'total_projects': total_projects,
                'status_counts': status_counts,
                'avg_project_value': avg_project_value
            }
            
        except Exception as e:
            print(f"خطأ في إحصائيات المشاريع: {e}")
            return {}
    
    def get_clients_analysis(self) -> Dict[str, Any]:
        """تحليل العملاء"""
        try:
            conn = sqlite3.connect(self.clients_db)
            cursor = conn.cursor()
            
            # إجمالي العملاء
            cursor.execute('SELECT COUNT(*) FROM clients')
            total_clients = cursor.fetchone()[0]
            
            # العملاء النشطون
            cursor.execute('SELECT COUNT(*) FROM clients WHERE status = "نشط"')
            active_clients = cursor.fetchone()[0]
            
            # أفضل العملاء (حسب الإيرادات)
            cursor.execute('''
                SELECT name, total_revenue FROM clients 
                ORDER BY total_revenue DESC LIMIT 10
            ''')
            top_clients = cursor.fetchall()
            
            # العملاء حسب المدينة
            cursor.execute('''
                SELECT city, COUNT(*) FROM clients 
                WHERE city IS NOT NULL AND city != ""
                GROUP BY city ORDER BY COUNT(*) DESC
            ''')
            clients_by_city = cursor.fetchall()
            
            conn.close()
            
            return {
                'total_clients': total_clients,
                'active_clients': active_clients,
                'top_clients': top_clients,
                'clients_by_city': clients_by_city
            }
            
        except Exception as e:
            print(f"خطأ في تحليل العملاء: {e}")
            return {}
    
    def get_inventory_analysis(self) -> Dict[str, Any]:
        """تحليل المخزون"""
        try:
            conn = sqlite3.connect(self.inventory_db)
            cursor = conn.cursor()
            
            # إجمالي قيمة المخزون
            cursor.execute('''
                SELECT SUM(current_stock * cost_price) FROM inventory_items
            ''')
            total_inventory_value = cursor.fetchone()[0] or 0.0
            
            # العناصر منخفضة المخزون
            cursor.execute('''
                SELECT COUNT(*) FROM inventory_items 
                WHERE current_stock <= minimum_stock
            ''')
            low_stock_items = cursor.fetchone()[0]
            
            # العناصر نفد مخزونها
            cursor.execute('''
                SELECT COUNT(*) FROM inventory_items 
                WHERE current_stock = 0
            ''')
            out_of_stock_items = cursor.fetchone()[0]
            
            # أكثر المواد استخداماً
            cursor.execute('''
                SELECT i.name, SUM(sm.quantity) as total_used
                FROM inventory_items i
                JOIN stock_movements sm ON i.id = sm.item_id
                WHERE sm.movement_type = 'بيع'
                GROUP BY i.id, i.name
                ORDER BY total_used DESC
                LIMIT 10
            ''')
            most_used_materials = cursor.fetchall()
            
            conn.close()
            
            return {
                'total_inventory_value': total_inventory_value,
                'low_stock_items': low_stock_items,
                'out_of_stock_items': out_of_stock_items,
                'most_used_materials': most_used_materials
            }
            
        except Exception as e:
            print(f"خطأ في تحليل المخزون: {e}")
            return {}


class ReportsDialog(QDialog):
    """حوار التقارير المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.reports_manager = ReportsManager()
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("التقارير المتقدمة")
        self.setModal(True)
        self.resize(1200, 800)
        
        layout = QVBoxLayout(self)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        # فترة التقرير
        period_label = QLabel("فترة التقرير:")
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        
        to_label = QLabel("إلى:")
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        
        # أزرار التقارير
        financial_btn = QPushButton("📊 التقرير المالي")
        financial_btn.clicked.connect(self.generate_financial_report)
        
        projects_btn = QPushButton("📋 تقرير المشاريع")
        projects_btn.clicked.connect(self.generate_projects_report)
        
        clients_btn = QPushButton("👥 تحليل العملاء")
        clients_btn.clicked.connect(self.generate_clients_report)
        
        inventory_btn = QPushButton("📦 تحليل المخزون")
        inventory_btn.clicked.connect(self.generate_inventory_report)
        
        export_btn = QPushButton("📄 تصدير PDF")
        export_btn.clicked.connect(self.export_report)
        
        toolbar_layout.addWidget(period_label)
        toolbar_layout.addWidget(self.start_date)
        toolbar_layout.addWidget(to_label)
        toolbar_layout.addWidget(self.end_date)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(financial_btn)
        toolbar_layout.addWidget(projects_btn)
        toolbar_layout.addWidget(clients_btn)
        toolbar_layout.addWidget(inventory_btn)
        toolbar_layout.addWidget(export_btn)
        
        layout.addLayout(toolbar_layout)
        
        # منطقة عرض التقارير
        self.reports_tabs = QTabWidget()
        layout.addWidget(self.reports_tabs)
        
        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Close)
        buttons.rejected.connect(self.close)
        layout.addWidget(buttons)
        
        # تحميل التقرير المالي افتراضياً
        self.generate_financial_report()
    
    def generate_financial_report(self):
        """توليد التقرير المالي"""
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")
        
        financial_data = self.reports_manager.get_financial_summary(start_date, end_date)
        
        if not financial_data:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات مالية للفترة المحددة")
            return
        
        # إنشاء تبويب التقرير المالي
        financial_widget = self.create_financial_report_widget(financial_data)
        
        # إضافة أو تحديث التبويب
        for i in range(self.reports_tabs.count()):
            if self.reports_tabs.tabText(i) == "التقرير المالي":
                self.reports_tabs.removeTab(i)
                break
        
        self.reports_tabs.insertTab(0, financial_widget, "التقرير المالي")
        self.reports_tabs.setCurrentIndex(0)
    
    def create_financial_report_widget(self, data: Dict[str, Any]) -> QWidget:
        """إنشاء واجهة التقرير المالي"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # ملخص مالي
        summary_group = QGroupBox("الملخص المالي")
        summary_layout = QGridLayout(summary_group)
        
        # بطاقات الملخص
        cards_layout = QHBoxLayout()
        
        # إجمالي الإيرادات
        revenue_card = self.create_summary_card(
            "إجمالي الإيرادات", 
            f"{data.get('total_revenue', 0):.2f} ريال",
            "#28a745"
        )
        cards_layout.addWidget(revenue_card)
        
        # الربح
        profit_card = self.create_summary_card(
            "صافي الربح",
            f"{data.get('profit', 0):.2f} ريال",
            "#17a2b8"
        )
        cards_layout.addWidget(profit_card)
        
        # هامش الربح
        margin_card = self.create_summary_card(
            "هامش الربح",
            f"{data.get('profit_margin', 0):.1f}%",
            "#ffc107"
        )
        cards_layout.addWidget(margin_card)
        
        summary_layout.addLayout(cards_layout, 0, 0)
        layout.addWidget(summary_group)
        
        # تفاصيل الإيرادات
        details_group = QGroupBox("تفاصيل الإيرادات")
        details_layout = QFormLayout(details_group)
        
        details_layout.addRow("إيرادات المشاريع:", QLabel(f"{data.get('projects_revenue', 0):.2f} ريال"))
        details_layout.addRow("إيرادات عروض الأسعار:", QLabel(f"{data.get('quotations_revenue', 0):.2f} ريال"))
        details_layout.addRow("تكلفة المخزون:", QLabel(f"{data.get('inventory_cost', 0):.2f} ريال"))
        
        layout.addWidget(details_group)
        
        layout.addStretch()
        return widget
    
    def create_summary_card(self, title: str, value: str, color: str) -> QWidget:
        """إنشاء بطاقة ملخص"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setStyleSheet(f"""
            QFrame {{
                border: 2px solid {color};
                border-radius: 10px;
                background-color: white;
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-weight: bold; color: #666;")
        
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"font-size: 18px; font-weight: bold; color: {color};")
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        return card
    
    def generate_projects_report(self):
        """توليد تقرير المشاريع"""
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")
        
        projects_data = self.reports_manager.get_projects_statistics(start_date, end_date)
        
        if not projects_data:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات مشاريع للفترة المحددة")
            return
        
        # إنشاء تبويب تقرير المشاريع
        projects_widget = self.create_projects_report_widget(projects_data)
        
        # إضافة أو تحديث التبويب
        for i in range(self.reports_tabs.count()):
            if self.reports_tabs.tabText(i) == "تقرير المشاريع":
                self.reports_tabs.removeTab(i)
                break
        
        self.reports_tabs.addTab(projects_widget, "تقرير المشاريع")
        self.reports_tabs.setCurrentWidget(projects_widget)
    
    def create_projects_report_widget(self, data: Dict[str, Any]) -> QWidget:
        """إنشاء واجهة تقرير المشاريع"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # إحصائيات عامة
        stats_group = QGroupBox("إحصائيات المشاريع")
        stats_layout = QFormLayout(stats_group)
        
        stats_layout.addRow("إجمالي المشاريع:", QLabel(str(data.get('total_projects', 0))))
        stats_layout.addRow("متوسط قيمة المشروع:", QLabel(f"{data.get('avg_project_value', 0):.2f} ريال"))
        
        layout.addWidget(stats_group)
        
        # المشاريع حسب الحالة
        status_group = QGroupBox("المشاريع حسب الحالة")
        status_layout = QVBoxLayout(status_group)
        
        status_counts = data.get('status_counts', {})
        for status, count in status_counts.items():
            status_layout.addWidget(QLabel(f"{status}: {count} مشروع"))
        
        layout.addWidget(status_group)
        
        layout.addStretch()
        return widget
    
    def generate_clients_report(self):
        """توليد تحليل العملاء"""
        clients_data = self.reports_manager.get_clients_analysis()
        
        if not clients_data:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات عملاء")
            return
        
        # إنشاء تبويب تحليل العملاء
        clients_widget = self.create_clients_report_widget(clients_data)
        
        # إضافة أو تحديث التبويب
        for i in range(self.reports_tabs.count()):
            if self.reports_tabs.tabText(i) == "تحليل العملاء":
                self.reports_tabs.removeTab(i)
                break
        
        self.reports_tabs.addTab(clients_widget, "تحليل العملاء")
        self.reports_tabs.setCurrentWidget(clients_widget)
    
    def create_clients_report_widget(self, data: Dict[str, Any]) -> QWidget:
        """إنشاء واجهة تحليل العملاء"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # إحصائيات عامة
        stats_group = QGroupBox("إحصائيات العملاء")
        stats_layout = QFormLayout(stats_group)
        
        stats_layout.addRow("إجمالي العملاء:", QLabel(str(data.get('total_clients', 0))))
        stats_layout.addRow("العملاء النشطون:", QLabel(str(data.get('active_clients', 0))))
        
        layout.addWidget(stats_group)
        
        # أفضل العملاء
        top_clients_group = QGroupBox("أفضل العملاء (حسب الإيرادات)")
        top_clients_layout = QVBoxLayout(top_clients_group)
        
        top_clients = data.get('top_clients', [])
        for client_name, revenue in top_clients[:5]:
            top_clients_layout.addWidget(QLabel(f"{client_name}: {revenue:.2f} ريال"))
        
        layout.addWidget(top_clients_group)
        
        layout.addStretch()
        return widget
    
    def generate_inventory_report(self):
        """توليد تحليل المخزون"""
        inventory_data = self.reports_manager.get_inventory_analysis()
        
        if not inventory_data:
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات مخزون")
            return
        
        # إنشاء تبويب تحليل المخزون
        inventory_widget = self.create_inventory_report_widget(inventory_data)
        
        # إضافة أو تحديث التبويب
        for i in range(self.reports_tabs.count()):
            if self.reports_tabs.tabText(i) == "تحليل المخزون":
                self.reports_tabs.removeTab(i)
                break
        
        self.reports_tabs.addTab(inventory_widget, "تحليل المخزون")
        self.reports_tabs.setCurrentWidget(inventory_widget)
    
    def create_inventory_report_widget(self, data: Dict[str, Any]) -> QWidget:
        """إنشاء واجهة تحليل المخزون"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # إحصائيات المخزون
        stats_group = QGroupBox("إحصائيات المخزون")
        stats_layout = QFormLayout(stats_group)
        
        stats_layout.addRow("قيمة المخزون الإجمالية:", QLabel(f"{data.get('total_inventory_value', 0):.2f} ريال"))
        stats_layout.addRow("عناصر منخفضة المخزون:", QLabel(str(data.get('low_stock_items', 0))))
        stats_layout.addRow("عناصر نفد مخزونها:", QLabel(str(data.get('out_of_stock_items', 0))))
        
        layout.addWidget(stats_group)
        
        # أكثر المواد استخداماً
        most_used_group = QGroupBox("أكثر المواد استخداماً")
        most_used_layout = QVBoxLayout(most_used_group)
        
        most_used_materials = data.get('most_used_materials', [])
        for material_name, quantity in most_used_materials[:5]:
            most_used_layout.addWidget(QLabel(f"{material_name}: {quantity:.1f} وحدة"))
        
        layout.addWidget(most_used_group)
        
        layout.addStretch()
        return widget
    
    def export_report(self):
        """تصدير التقرير"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ تصدير التقارير قريباً")
