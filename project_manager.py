#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Project Manager Module
وحدة إدارة المشاريع - حفظ وتحميل
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class ProjectInfo:
    """معلومات المشروع"""
    name: str = ""
    description: str = ""
    author: str = ""
    created_date: str = ""
    modified_date: str = ""
    version: str = "1.0"
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if not self.created_date:
            self.created_date = datetime.now().isoformat()
        if not self.modified_date:
            self.modified_date = self.created_date


@dataclass
class CutListProject:
    """مشروع جدول القطع"""
    info: ProjectInfo
    components: List[Dict[str, Any]]
    materials_used: List[str] = None
    statistics: Dict[str, Any] = None
    settings: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.materials_used is None:
            self.materials_used = []
        if self.statistics is None:
            self.statistics = {}
        if self.settings is None:
            self.settings = {}


class ProjectManager:
    """مدير المشاريع"""
    
    def __init__(self, projects_dir: str = "projects"):
        self.projects_dir = projects_dir
        self.ensure_projects_directory()
    
    def ensure_projects_directory(self):
        """التأكد من وجود مجلد المشاريع"""
        if not os.path.exists(self.projects_dir):
            os.makedirs(self.projects_dir)
    
    def save_project(self, project: CutListProject, file_path: str = None) -> bool:
        """
        حفظ المشروع
        
        Args:
            project: المشروع المراد حفظه
            file_path: مسار الملف (اختياري)
            
        Returns:
            True إذا تم الحفظ بنجاح
        """
        try:
            # تحديث تاريخ التعديل
            project.info.modified_date = datetime.now().isoformat()
            
            # تحديد مسار الملف
            if not file_path:
                safe_name = self._make_safe_filename(project.info.name)
                file_path = os.path.join(self.projects_dir, f"{safe_name}.cutlist")
            
            # تحويل المشروع إلى قاموس
            project_data = asdict(project)
            
            # حفظ الملف
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ المشروع: {e}")
            return False
    
    def load_project(self, file_path: str) -> Optional[CutListProject]:
        """
        تحميل المشروع
        
        Args:
            file_path: مسار الملف
            
        Returns:
            المشروع المحمل أو None في حالة الخطأ
        """
        try:
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            
            # تحويل البيانات إلى كائنات
            info_data = project_data.get('info', {})
            project_info = ProjectInfo(**info_data)
            
            components = project_data.get('components', [])
            materials_used = project_data.get('materials_used', [])
            statistics = project_data.get('statistics', {})
            settings = project_data.get('settings', {})
            
            project = CutListProject(
                info=project_info,
                components=components,
                materials_used=materials_used,
                statistics=statistics,
                settings=settings
            )
            
            return project
            
        except Exception as e:
            print(f"خطأ في تحميل المشروع: {e}")
            return None
    
    def get_project_list(self) -> List[Dict[str, Any]]:
        """
        الحصول على قائمة المشاريع المحفوظة
        
        Returns:
            قائمة بمعلومات المشاريع
        """
        projects = []
        
        try:
            for filename in os.listdir(self.projects_dir):
                if filename.endswith('.cutlist'):
                    file_path = os.path.join(self.projects_dir, filename)
                    project = self.load_project(file_path)
                    
                    if project:
                        projects.append({
                            'filename': filename,
                            'file_path': file_path,
                            'name': project.info.name,
                            'description': project.info.description,
                            'created_date': project.info.created_date,
                            'modified_date': project.info.modified_date,
                            'components_count': len(project.components),
                            'file_size': os.path.getsize(file_path)
                        })
            
            # ترتيب حسب تاريخ التعديل (الأحدث أولاً)
            projects.sort(key=lambda x: x['modified_date'], reverse=True)
            
        except Exception as e:
            print(f"خطأ في قراءة قائمة المشاريع: {e}")
        
        return projects
    
    def delete_project(self, file_path: str) -> bool:
        """
        حذف مشروع
        
        Args:
            file_path: مسار الملف
            
        Returns:
            True إذا تم الحذف بنجاح
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
            
        except Exception as e:
            print(f"خطأ في حذف المشروع: {e}")
            return False
    
    def duplicate_project(self, source_path: str, new_name: str) -> bool:
        """
        نسخ مشروع
        
        Args:
            source_path: مسار المشروع الأصلي
            new_name: اسم المشروع الجديد
            
        Returns:
            True إذا تم النسخ بنجاح
        """
        try:
            project = self.load_project(source_path)
            if not project:
                return False
            
            # تحديث معلومات المشروع الجديد
            project.info.name = new_name
            project.info.created_date = datetime.now().isoformat()
            project.info.modified_date = project.info.created_date
            
            # حفظ المشروع الجديد
            return self.save_project(project)
            
        except Exception as e:
            print(f"خطأ في نسخ المشروع: {e}")
            return False
    
    def export_project_summary(self, project: CutListProject) -> Dict[str, Any]:
        """
        تصدير ملخص المشروع
        
        Args:
            project: المشروع
            
        Returns:
            ملخص المشروع
        """
        # حساب الإحصائيات
        total_components = len(project.components)
        total_quantity = sum(comp.get('quantity', 1) for comp in project.components)
        
        materials_count = {}
        total_volume = 0
        total_weight = 0
        total_cost = 0
        
        for comp in project.components:
            # المواد
            material = comp.get('material', 'غير محدد')
            materials_count[material] = materials_count.get(material, 0) + comp.get('quantity', 1)
            
            # الحجم والوزن والتكلفة
            total_volume += comp.get('volume', 0) * comp.get('quantity', 1)
            total_weight += comp.get('weight', 0) * comp.get('quantity', 1)
            total_cost += comp.get('cost', 0) * comp.get('quantity', 1)
        
        return {
            'project_info': asdict(project.info),
            'statistics': {
                'total_components': total_components,
                'total_quantity': total_quantity,
                'total_volume_cm3': total_volume / 1000,  # تحويل إلى سم³
                'total_weight_kg': total_weight,
                'total_cost': total_cost,
                'materials_breakdown': materials_count,
                'unique_materials': len(materials_count)
            },
            'components_summary': [
                {
                    'name': comp.get('name', ''),
                    'dimensions': comp.get('dimensions', {}),
                    'material': comp.get('material', ''),
                    'quantity': comp.get('quantity', 1)
                }
                for comp in project.components
            ]
        }
    
    def _make_safe_filename(self, name: str) -> str:
        """تحويل اسم المشروع إلى اسم ملف آمن"""
        # إزالة الأحرف غير المسموحة
        safe_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_"
        safe_name = ""
        
        for char in name:
            if char in safe_chars:
                safe_name += char
            elif char == ' ':
                safe_name += '_'
            else:
                safe_name += '_'
        
        # التأكد من أن الاسم ليس فارغاً
        if not safe_name:
            safe_name = f"project_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        return safe_name
    
    def create_project_from_components(self, components: List[Dict[str, Any]], 
                                     project_name: str = "", 
                                     description: str = "") -> CutListProject:
        """
        إنشاء مشروع جديد من قائمة المكونات
        
        Args:
            components: قائمة المكونات
            project_name: اسم المشروع
            description: وصف المشروع
            
        Returns:
            المشروع الجديد
        """
        # إنشاء معلومات المشروع
        if not project_name:
            project_name = f"مشروع جديد - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        project_info = ProjectInfo(
            name=project_name,
            description=description
        )
        
        # استخراج المواد المستخدمة
        materials_used = list(set(comp.get('material', 'خشب') for comp in components))
        
        # حساب الإحصائيات
        statistics = {
            'total_components': len(components),
            'total_quantity': sum(comp.get('quantity', 1) for comp in components),
            'materials_count': len(materials_used)
        }
        
        # إنشاء المشروع
        project = CutListProject(
            info=project_info,
            components=components,
            materials_used=materials_used,
            statistics=statistics
        )
        
        return project


def test_project_manager():
    """اختبار مدير المشاريع"""
    # إنشاء مدير المشاريع
    manager = ProjectManager("test_projects")
    
    # إنشاء مشروع تجريبي
    test_components = [
        {
            'name': 'لوح علوي',
            'dimensions': {'length': 1200, 'width': 600, 'thickness': 18},
            'material': 'خشب',
            'quantity': 1,
            'volume': 12960000,
            'weight': 7.78,
            'cost': 65.0
        },
        {
            'name': 'لوح جانبي',
            'dimensions': {'length': 800, 'width': 400, 'thickness': 18},
            'material': 'خشب',
            'quantity': 2,
            'volume': 5760000,
            'weight': 3.46,
            'cost': 28.8
        }
    ]
    
    project = manager.create_project_from_components(
        test_components,
        "طاولة مكتب خشبية",
        "طاولة مكتب بسيطة من الخشب الطبيعي"
    )
    
    # حفظ المشروع
    success = manager.save_project(project)
    print(f"حفظ المشروع: {'نجح' if success else 'فشل'}")
    
    # قائمة المشاريع
    projects = manager.get_project_list()
    print(f"عدد المشاريع المحفوظة: {len(projects)}")
    
    for proj in projects:
        print(f"- {proj['name']}: {proj['components_count']} مكون")
    
    # تحميل المشروع
    if projects:
        loaded_project = manager.load_project(projects[0]['file_path'])
        if loaded_project:
            print(f"تم تحميل المشروع: {loaded_project.info.name}")
            
            # ملخص المشروع
            summary = manager.export_project_summary(loaded_project)
            print(f"إحصائيات المشروع: {summary['statistics']}")
    
    # تنظيف ملفات الاختبار
    import shutil
    if os.path.exists("test_projects"):
        shutil.rmtree("test_projects")


if __name__ == "__main__":
    test_project_manager()
