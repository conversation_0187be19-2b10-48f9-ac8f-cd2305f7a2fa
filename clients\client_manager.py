#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Client Manager
مدير العملاء والمشاريع
"""

import json
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, date
from enum import Enum


class ProjectStatus(Enum):
    """حالة المشروع"""
    QUOTE = "عرض سعر"
    APPROVED = "معتمد"
    IN_PRODUCTION = "قيد الإنتاج"
    READY = "جاهز"
    DELIVERED = "مسلم"
    CANCELLED = "ملغي"


class ClientType(Enum):
    """نوع العميل"""
    INDIVIDUAL = "فرد"
    COMPANY = "شركة"
    CONTRACTOR = "مقاول"
    DESIGNER = "مصمم"


@dataclass
class ClientContact:
    """معلومات الاتصال"""
    phone: str = ""
    email: str = ""
    address: str = ""
    city: str = ""
    postal_code: str = ""
    country: str = "السعودية"


@dataclass
class Client:
    """بيانات العميل"""
    id: str
    name: str
    client_type: ClientType
    contact: ClientContact
    tax_number: str = ""
    commercial_register: str = ""
    notes: str = ""
    created_date: str = ""
    last_contact: str = ""
    total_projects: int = 0
    total_revenue: float = 0.0
    preferred_materials: List[str] = None
    discount_rate: float = 0.0
    payment_terms: str = "نقداً عند التسليم"
    
    def __post_init__(self):
        if self.preferred_materials is None:
            self.preferred_materials = []
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class ProjectOrder:
    """طلب مشروع"""
    id: str
    client_id: str
    name: str
    description: str
    status: ProjectStatus
    quote_amount: float = 0.0
    final_amount: float = 0.0
    created_date: str = ""
    quote_date: str = ""
    approval_date: str = ""
    delivery_date: str = ""
    actual_delivery_date: str = ""
    components: List[Dict[str, Any]] = None
    materials_cost: float = 0.0
    labor_cost: float = 0.0
    overhead_cost: float = 0.0
    profit_margin: float = 0.0
    notes: str = ""
    attachments: List[str] = None
    
    def __post_init__(self):
        if self.components is None:
            self.components = []
        if self.attachments is None:
            self.attachments = []
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


class ClientManager:
    """مدير العملاء"""
    
    def __init__(self, data_dir: str = "clients_data"):
        self.data_dir = data_dir
        self.clients: Dict[str, Client] = {}
        self.projects: Dict[str, ProjectOrder] = {}
        
        self.ensure_data_directory()
        self.load_data()
    
    def ensure_data_directory(self):
        """التأكد من وجود مجلد البيانات"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def load_data(self):
        """تحميل بيانات العملاء والمشاريع"""
        # تحميل العملاء
        clients_file = os.path.join(self.data_dir, "clients.json")
        if os.path.exists(clients_file):
            try:
                with open(clients_file, 'r', encoding='utf-8') as f:
                    clients_data = json.load(f)
                
                for client_data in clients_data:
                    # تحويل نوع العميل
                    client_data['client_type'] = ClientType(client_data['client_type'])
                    # تحويل معلومات الاتصال
                    client_data['contact'] = ClientContact(**client_data['contact'])
                    
                    client = Client(**client_data)
                    self.clients[client.id] = client
                    
            except Exception as e:
                print(f"خطأ في تحميل العملاء: {e}")
        
        # تحميل المشاريع
        projects_file = os.path.join(self.data_dir, "projects.json")
        if os.path.exists(projects_file):
            try:
                with open(projects_file, 'r', encoding='utf-8') as f:
                    projects_data = json.load(f)
                
                for project_data in projects_data:
                    # تحويل حالة المشروع
                    project_data['status'] = ProjectStatus(project_data['status'])
                    
                    project = ProjectOrder(**project_data)
                    self.projects[project.id] = project
                    
            except Exception as e:
                print(f"خطأ في تحميل المشاريع: {e}")
    
    def save_data(self):
        """حفظ بيانات العملاء والمشاريع"""
        try:
            # حفظ العملاء
            clients_file = os.path.join(self.data_dir, "clients.json")
            clients_data = []
            for client in self.clients.values():
                client_dict = asdict(client)
                client_dict['client_type'] = client.client_type.value
                clients_data.append(client_dict)
            
            with open(clients_file, 'w', encoding='utf-8') as f:
                json.dump(clients_data, f, ensure_ascii=False, indent=2)
            
            # حفظ المشاريع
            projects_file = os.path.join(self.data_dir, "projects.json")
            projects_data = []
            for project in self.projects.values():
                project_dict = asdict(project)
                project_dict['status'] = project.status.value
                projects_data.append(project_dict)
            
            with open(projects_file, 'w', encoding='utf-8') as f:
                json.dump(projects_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ البيانات: {e}")
    
    def add_client(self, client: Client) -> bool:
        """إضافة عميل جديد"""
        try:
            self.clients[client.id] = client
            self.save_data()
            return True
        except Exception as e:
            print(f"خطأ في إضافة العميل: {e}")
            return False
    
    def update_client(self, client: Client) -> bool:
        """تحديث بيانات عميل"""
        try:
            if client.id in self.clients:
                self.clients[client.id] = client
                self.save_data()
                return True
            return False
        except Exception as e:
            print(f"خطأ في تحديث العميل: {e}")
            return False
    
    def get_client(self, client_id: str) -> Optional[Client]:
        """الحصول على عميل بالمعرف"""
        return self.clients.get(client_id)
    
    def search_clients(self, query: str) -> List[Client]:
        """البحث في العملاء"""
        results = []
        query_lower = query.lower()
        
        for client in self.clients.values():
            if (query_lower in client.name.lower() or
                query_lower in client.contact.phone or
                query_lower in client.contact.email.lower()):
                results.append(client)
        
        return results
    
    def add_project(self, project: ProjectOrder) -> bool:
        """إضافة مشروع جديد"""
        try:
            self.projects[project.id] = project
            
            # تحديث إحصائيات العميل
            client = self.get_client(project.client_id)
            if client:
                client.total_projects += 1
                client.last_contact = datetime.now().isoformat()
                if project.status == ProjectStatus.DELIVERED:
                    client.total_revenue += project.final_amount
            
            self.save_data()
            return True
        except Exception as e:
            print(f"خطأ في إضافة المشروع: {e}")
            return False
    
    def update_project_status(self, project_id: str, new_status: ProjectStatus) -> bool:
        """تحديث حالة المشروع"""
        try:
            project = self.projects.get(project_id)
            if project:
                old_status = project.status
                project.status = new_status
                
                # تحديث التواريخ
                now = datetime.now().isoformat()
                if new_status == ProjectStatus.APPROVED and old_status != ProjectStatus.APPROVED:
                    project.approval_date = now
                elif new_status == ProjectStatus.DELIVERED and old_status != ProjectStatus.DELIVERED:
                    project.actual_delivery_date = now
                    
                    # تحديث إيرادات العميل
                    client = self.get_client(project.client_id)
                    if client and old_status != ProjectStatus.DELIVERED:
                        client.total_revenue += project.final_amount
                
                self.save_data()
                return True
            return False
        except Exception as e:
            print(f"خطأ في تحديث حالة المشروع: {e}")
            return False
    
    def get_client_projects(self, client_id: str) -> List[ProjectOrder]:
        """الحصول على مشاريع عميل معين"""
        return [project for project in self.projects.values() 
                if project.client_id == client_id]
    
    def get_projects_by_status(self, status: ProjectStatus) -> List[ProjectOrder]:
        """الحصول على المشاريع حسب الحالة"""
        return [project for project in self.projects.values() 
                if project.status == status]
    
    def get_revenue_statistics(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """حساب إحصائيات الإيرادات"""
        total_revenue = 0.0
        total_projects = 0
        delivered_projects = 0
        pending_quotes = 0
        
        for project in self.projects.values():
            if start_date and project.created_date < start_date:
                continue
            if end_date and project.created_date > end_date:
                continue
            
            total_projects += 1
            
            if project.status == ProjectStatus.DELIVERED:
                total_revenue += project.final_amount
                delivered_projects += 1
            elif project.status == ProjectStatus.QUOTE:
                pending_quotes += 1
        
        return {
            'total_revenue': total_revenue,
            'total_projects': total_projects,
            'delivered_projects': delivered_projects,
            'pending_quotes': pending_quotes,
            'average_project_value': total_revenue / delivered_projects if delivered_projects > 0 else 0,
            'conversion_rate': delivered_projects / total_projects if total_projects > 0 else 0
        }
    
    def generate_client_id(self) -> str:
        """توليد معرف عميل جديد"""
        return f"CLIENT_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    def generate_project_id(self) -> str:
        """توليد معرف مشروع جديد"""
        return f"PROJECT_{datetime.now().strftime('%Y%m%d_%H%M%S')}"


def test_client_manager():
    """اختبار مدير العملاء"""
    manager = ClientManager("test_clients")
    
    # إضافة عميل تجريبي
    contact = ClientContact(
        phone="0501234567",
        email="<EMAIL>",
        address="شارع الملك فهد",
        city="الرياض"
    )
    
    client = Client(
        id=manager.generate_client_id(),
        name="أحمد محمد",
        client_type=ClientType.INDIVIDUAL,
        contact=contact,
        notes="عميل مهم"
    )
    
    success = manager.add_client(client)
    print(f"إضافة العميل: {'نجح' if success else 'فشل'}")
    
    # إضافة مشروع
    project = ProjectOrder(
        id=manager.generate_project_id(),
        client_id=client.id,
        name="مكتب مكتبي",
        description="مكتب مكتبي خشبي بسيط",
        status=ProjectStatus.QUOTE,
        quote_amount=1500.0
    )
    
    success = manager.add_project(project)
    print(f"إضافة المشروع: {'نجح' if success else 'فشل'}")
    
    # إحصائيات
    stats = manager.get_revenue_statistics()
    print(f"الإحصائيات: {stats}")
    
    # تنظيف
    import shutil
    if os.path.exists("test_clients"):
        shutil.rmtree("test_clients")


if __name__ == "__main__":
    test_client_manager()
