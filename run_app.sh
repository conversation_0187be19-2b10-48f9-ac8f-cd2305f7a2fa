#!/bin/bash

echo "========================================"
echo "    CutList Desktop App"
echo "    تطبيق جدول القطع"
echo "========================================"
echo

echo "جاري التحقق من Python..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "خطأ: Python غير مثبت"
        echo "يرجى تثبيت Python 3.8 أو أحدث"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "جاري التحقق من PyQt5..."
$PYTHON_CMD -c "import PyQt5" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "PyQt5 غير مثبت. جاري التثبيت..."
    pip install PyQt5
    if [ $? -ne 0 ]; then
        echo "فشل في تثبيت PyQt5"
        exit 1
    fi
fi

echo "جاري تشغيل التطبيق..."
echo
$PYTHON_CMD main_simple.py

if [ $? -ne 0 ]; then
    echo
    echo "حدث خطأ أثناء تشغيل التطبيق"
fi
