#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Market Pricing Engine
محرك التسعير وأسعار السوق
"""

import json
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum


class PriceType(Enum):
    """نوع السعر"""
    MATERIAL = "مادة"
    LABOR = "عمالة"
    OVERHEAD = "مصاريف عامة"
    PROFIT = "ربح"


class ComplexityLevel(Enum):
    """مستوى التعقيد"""
    SIMPLE = "بسيط"
    MEDIUM = "متوسط"
    COMPLEX = "معقد"
    VERY_COMPLEX = "معقد جداً"


@dataclass
class PriceEntry:
    """إدخال سعر"""
    material: str
    price_per_unit: float
    unit: str  # م³، م²، قطعة، ساعة
    supplier: str = ""
    last_updated: str = ""
    notes: str = ""
    
    def __post_init__(self):
        if not self.last_updated:
            self.last_updated = datetime.now().isoformat()


@dataclass
class LaborRate:
    """معدل العمالة"""
    skill_level: str
    hourly_rate: float
    daily_rate: float
    description: str = ""
    
    
@dataclass
class ProjectPricing:
    """تسعير مشروع"""
    project_id: str
    materials_cost: float
    labor_cost: float
    overhead_cost: float
    profit_margin: float
    total_cost: float
    complexity_multiplier: float = 1.0
    rush_order_multiplier: float = 1.0
    client_discount: float = 0.0
    final_price: float = 0.0
    created_date: str = ""
    
    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()
        
        # حساب السعر النهائي
        base_price = (self.materials_cost + self.labor_cost + self.overhead_cost) * (1 + self.profit_margin)
        adjusted_price = base_price * self.complexity_multiplier * self.rush_order_multiplier
        self.final_price = adjusted_price * (1 - self.client_discount)
        self.total_cost = self.materials_cost + self.labor_cost + self.overhead_cost


class MarketPricingEngine:
    """محرك التسعير وأسعار السوق"""
    
    def __init__(self, data_dir: str = "pricing_data"):
        self.data_dir = data_dir
        self.material_prices: Dict[str, PriceEntry] = {}
        self.labor_rates: Dict[str, LaborRate] = {}
        self.overhead_rates: Dict[str, float] = {}
        self.complexity_multipliers: Dict[ComplexityLevel, float] = {}
        
        self.ensure_data_directory()
        self.load_pricing_data()
        
        # إذا لم توجد بيانات، أضف البيانات الافتراضية
        if not self.material_prices:
            self._create_default_pricing()
    
    def ensure_data_directory(self):
        """التأكد من وجود مجلد البيانات"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def load_pricing_data(self):
        """تحميل بيانات التسعير"""
        # تحميل أسعار المواد
        materials_file = os.path.join(self.data_dir, "material_prices.json")
        if os.path.exists(materials_file):
            try:
                with open(materials_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for material, price_data in data.items():
                    self.material_prices[material] = PriceEntry(**price_data)
                    
            except Exception as e:
                print(f"خطأ في تحميل أسعار المواد: {e}")
        
        # تحميل معدلات العمالة
        labor_file = os.path.join(self.data_dir, "labor_rates.json")
        if os.path.exists(labor_file):
            try:
                with open(labor_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for skill, rate_data in data.items():
                    self.labor_rates[skill] = LaborRate(**rate_data)
                    
            except Exception as e:
                print(f"خطأ في تحميل معدلات العمالة: {e}")
        
        # تحميل معدلات المصاريف العامة
        overhead_file = os.path.join(self.data_dir, "overhead_rates.json")
        if os.path.exists(overhead_file):
            try:
                with open(overhead_file, 'r', encoding='utf-8') as f:
                    self.overhead_rates = json.load(f)
                    
            except Exception as e:
                print(f"خطأ في تحميل المصاريف العامة: {e}")
        
        # تحميل مضاعفات التعقيد
        complexity_file = os.path.join(self.data_dir, "complexity_multipliers.json")
        if os.path.exists(complexity_file):
            try:
                with open(complexity_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for level_str, multiplier in data.items():
                    level = ComplexityLevel(level_str)
                    self.complexity_multipliers[level] = multiplier
                    
            except Exception as e:
                print(f"خطأ في تحميل مضاعفات التعقيد: {e}")
    
    def save_pricing_data(self):
        """حفظ بيانات التسعير"""
        try:
            # حفظ أسعار المواد
            materials_file = os.path.join(self.data_dir, "material_prices.json")
            materials_data = {}
            for material, price_entry in self.material_prices.items():
                materials_data[material] = asdict(price_entry)
            
            with open(materials_file, 'w', encoding='utf-8') as f:
                json.dump(materials_data, f, ensure_ascii=False, indent=2)
            
            # حفظ معدلات العمالة
            labor_file = os.path.join(self.data_dir, "labor_rates.json")
            labor_data = {}
            for skill, rate in self.labor_rates.items():
                labor_data[skill] = asdict(rate)
            
            with open(labor_file, 'w', encoding='utf-8') as f:
                json.dump(labor_data, f, ensure_ascii=False, indent=2)
            
            # حفظ المصاريف العامة
            overhead_file = os.path.join(self.data_dir, "overhead_rates.json")
            with open(overhead_file, 'w', encoding='utf-8') as f:
                json.dump(self.overhead_rates, f, ensure_ascii=False, indent=2)
            
            # حفظ مضاعفات التعقيد
            complexity_file = os.path.join(self.data_dir, "complexity_multipliers.json")
            complexity_data = {}
            for level, multiplier in self.complexity_multipliers.items():
                complexity_data[level.value] = multiplier
            
            with open(complexity_file, 'w', encoding='utf-8') as f:
                json.dump(complexity_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ بيانات التسعير: {e}")
    
    def calculate_material_cost(self, components: List[Dict[str, Any]]) -> float:
        """حساب تكلفة المواد"""
        total_cost = 0.0
        
        for component in components:
            material = component.get('material', 'خشب')
            dimensions = component.get('dimensions', {})
            quantity = component.get('quantity', 1)
            
            # حساب الحجم أو المساحة
            length = dimensions.get('length', 0) / 1000  # تحويل إلى متر
            width = dimensions.get('width', 0) / 1000
            thickness = dimensions.get('thickness', 0) / 1000
            
            volume_m3 = length * width * thickness * quantity
            area_m2 = length * width * quantity
            
            # الحصول على سعر المادة
            price_entry = self.material_prices.get(material)
            if price_entry:
                if price_entry.unit == "م³":
                    cost = volume_m3 * price_entry.price_per_unit
                elif price_entry.unit == "م²":
                    cost = area_m2 * price_entry.price_per_unit
                else:  # قطعة
                    cost = quantity * price_entry.price_per_unit
                
                total_cost += cost
        
        return total_cost
    
    def calculate_labor_cost(self, components: List[Dict[str, Any]], 
                           complexity: ComplexityLevel = ComplexityLevel.MEDIUM) -> float:
        """حساب تكلفة العمالة"""
        # تقدير ساعات العمل بناءً على التعقيد والحجم
        total_hours = 0.0
        
        for component in components:
            dimensions = component.get('dimensions', {})
            quantity = component.get('quantity', 1)
            
            # حساب مؤشر التعقيد
            area = (dimensions.get('length', 0) * dimensions.get('width', 0)) / 1000000  # م²
            
            # ساعات العمل الأساسية (تقدير)
            base_hours = area * 0.5 * quantity  # 30 دقيقة لكل متر مربع
            
            # تطبيق مضاعف التعقيد
            complexity_multiplier = self.complexity_multipliers.get(complexity, 1.5)
            hours = base_hours * complexity_multiplier
            
            total_hours += hours
        
        # حساب التكلفة بناءً على مستوى المهارة
        carpenter_rate = self.labor_rates.get("نجار", LaborRate("نجار", 50, 400))
        return total_hours * carpenter_rate.hourly_rate
    
    def calculate_overhead_cost(self, materials_cost: float, labor_cost: float) -> float:
        """حساب المصاريف العامة"""
        base_cost = materials_cost + labor_cost
        overhead_rate = self.overhead_rates.get("general", 0.15)  # 15% افتراضي
        return base_cost * overhead_rate
    
    def generate_project_pricing(self, components: List[Dict[str, Any]], 
                                project_id: str,
                                complexity: ComplexityLevel = ComplexityLevel.MEDIUM,
                                profit_margin: float = 0.25,
                                rush_order: bool = False,
                                client_discount: float = 0.0) -> ProjectPricing:
        """توليد تسعير شامل للمشروع"""
        
        # حساب التكاليف الأساسية
        materials_cost = self.calculate_material_cost(components)
        labor_cost = self.calculate_labor_cost(components, complexity)
        overhead_cost = self.calculate_overhead_cost(materials_cost, labor_cost)
        
        # مضاعفات إضافية
        complexity_multiplier = self.complexity_multipliers.get(complexity, 1.5)
        rush_order_multiplier = 1.3 if rush_order else 1.0
        
        pricing = ProjectPricing(
            project_id=project_id,
            materials_cost=materials_cost,
            labor_cost=labor_cost,
            overhead_cost=overhead_cost,
            profit_margin=profit_margin,
            total_cost=0,  # سيتم حسابه في __post_init__
            complexity_multiplier=complexity_multiplier,
            rush_order_multiplier=rush_order_multiplier,
            client_discount=client_discount,
            final_price=0  # سيتم حسابه في __post_init__
        )
        
        return pricing
    
    def update_material_price(self, material: str, price_entry: PriceEntry):
        """تحديث سعر مادة"""
        self.material_prices[material] = price_entry
        self.save_pricing_data()
    
    def get_price_history(self, material: str, days: int = 30) -> List[Dict[str, Any]]:
        """الحصول على تاريخ الأسعار (محاكاة)"""
        # في التطبيق الحقيقي، سيتم تخزين تاريخ الأسعار
        current_price = self.material_prices.get(material)
        if not current_price:
            return []
        
        # محاكاة تغيرات الأسعار
        history = []
        base_price = current_price.price_per_unit
        
        for i in range(days):
            date = datetime.now() - timedelta(days=i)
            # تغيير عشوائي بسيط
            variation = 1 + (i % 7 - 3) * 0.02  # تغيير ±6%
            price = base_price * variation
            
            history.append({
                'date': date.strftime('%Y-%m-%d'),
                'price': round(price, 2),
                'material': material
            })
        
        return sorted(history, key=lambda x: x['date'])
    
    def _create_default_pricing(self):
        """إنشاء بيانات التسعير الافتراضية"""
        
        # أسعار المواد الافتراضية
        default_materials = {
            "خشب": PriceEntry("خشب", 500, "م³", "مورد محلي", notes="خشب طبيعي عالي الجودة"),
            "MDF": PriceEntry("MDF", 300, "م³", "مورد محلي", notes="ألواح ليفية متوسطة الكثافة"),
            "خشب رقائقي": PriceEntry("خشب رقائقي", 400, "م³", "مورد محلي", notes="خشب رقائقي متعدد الطبقات"),
            "ألمنيوم": PriceEntry("ألمنيوم", 2000, "م³", "مورد معادن", notes="ألمنيوم خفيف الوزن"),
            "بلاستيك": PriceEntry("بلاستيك", 800, "م³", "مورد بلاستيك", notes="بلاستيك عالي الجودة"),
        }
        
        for material, price_entry in default_materials.items():
            self.material_prices[material] = price_entry
        
        # معدلات العمالة الافتراضية
        default_labor = {
            "نجار مبتدئ": LaborRate("نجار مبتدئ", 30, 240, "نجار بخبرة أقل من سنتين"),
            "نجار": LaborRate("نجار", 50, 400, "نجار بخبرة متوسطة"),
            "نجار خبير": LaborRate("نجار خبير", 80, 640, "نجار بخبرة أكثر من 5 سنوات"),
            "مصمم": LaborRate("مصمم", 100, 800, "مصمم أثاث محترف"),
        }
        
        for skill, rate in default_labor.items():
            self.labor_rates[skill] = rate
        
        # المصاريف العامة الافتراضية
        self.overhead_rates = {
            "general": 0.15,  # 15% مصاريف عامة
            "rent": 0.05,     # 5% إيجار
            "utilities": 0.03, # 3% مرافق
            "insurance": 0.02, # 2% تأمين
            "tools": 0.05     # 5% أدوات ومعدات
        }
        
        # مضاعفات التعقيد
        self.complexity_multipliers = {
            ComplexityLevel.SIMPLE: 1.0,
            ComplexityLevel.MEDIUM: 1.5,
            ComplexityLevel.COMPLEX: 2.0,
            ComplexityLevel.VERY_COMPLEX: 3.0
        }
        
        # حفظ البيانات
        self.save_pricing_data()


def test_market_pricing():
    """اختبار محرك التسعير"""
    engine = MarketPricingEngine("test_pricing")
    
    # مكونات تجريبية
    components = [
        {
            'name': 'لوح علوي',
            'dimensions': {'length': 1200, 'width': 600, 'thickness': 18},
            'material': 'خشب',
            'quantity': 1
        },
        {
            'name': 'لوح جانبي',
            'dimensions': {'length': 800, 'width': 400, 'thickness': 18},
            'material': 'خشب',
            'quantity': 2
        }
    ]
    
    # توليد تسعير
    pricing = engine.generate_project_pricing(
        components, 
        "TEST_PROJECT",
        ComplexityLevel.MEDIUM,
        profit_margin=0.25,
        rush_order=False,
        client_discount=0.1
    )
    
    print("تسعير المشروع:")
    print(f"تكلفة المواد: {pricing.materials_cost:.2f} ريال")
    print(f"تكلفة العمالة: {pricing.labor_cost:.2f} ريال")
    print(f"المصاريف العامة: {pricing.overhead_cost:.2f} ريال")
    print(f"إجمالي التكلفة: {pricing.total_cost:.2f} ريال")
    print(f"السعر النهائي: {pricing.final_price:.2f} ريال")
    
    # تنظيف
    import shutil
    if os.path.exists("test_pricing"):
        shutil.rmtree("test_pricing")


if __name__ == "__main__":
    test_market_pricing()
