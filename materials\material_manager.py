#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Material Manager Module
وحدة إدارة المواد والخصائص
"""

import json
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class Material:
    """فئة تمثل مادة واحدة"""
    name: str                    # اسم المادة
    density: float              # الكثافة (كغ/م³)
    cost_per_m3: float         # التكلفة لكل متر مكعب
    cost_per_m2: float = 0.0   # التكلفة لكل متر مربع (للألواح)
    color: str = "#8B4513"     # لون المادة (للعرض)
    category: str = "خشب"      # فئة المادة
    description: str = ""       # وصف المادة
    supplier: str = ""          # المورد
    thickness_options: List[float] = None  # السماكات المتاحة
    sheet_sizes: List[Dict[str, float]] = None  # أحجام الألواح المتاحة
    created_date: str = ""      # تاريخ الإضافة
    
    def __post_init__(self):
        if self.thickness_options is None:
            self.thickness_options = []
        if self.sheet_sizes is None:
            self.sheet_sizes = []
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


class MaterialManager:
    """مدير المواد والخصائص"""
    
    def __init__(self, data_file: str = "materials_database.json"):
        self.data_file = data_file
        self.materials: Dict[str, Material] = {}
        self.load_materials()
        
        # إذا لم توجد مواد، أضف المواد الافتراضية
        if not self.materials:
            self._add_default_materials()
    
    def load_materials(self):
        """تحميل المواد من الملف"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                for name, material_data in data.items():
                    # تحويل البيانات إلى كائن Material
                    material = Material(**material_data)
                    self.materials[name] = material
                    
            except Exception as e:
                print(f"خطأ في تحميل المواد: {e}")
                self.materials = {}
    
    def save_materials(self):
        """حفظ المواد في الملف"""
        try:
            # تحويل المواد إلى قاموس
            data = {}
            for name, material in self.materials.items():
                data[name] = asdict(material)
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ المواد: {e}")
    
    def add_material(self, material: Material) -> bool:
        """إضافة مادة جديدة"""
        try:
            self.materials[material.name] = material
            self.save_materials()
            return True
        except Exception as e:
            print(f"خطأ في إضافة المادة: {e}")
            return False
    
    def update_material(self, name: str, updated_material: Material) -> bool:
        """تحديث مادة موجودة"""
        try:
            if name in self.materials:
                # الاحتفاظ بتاريخ الإنشاء الأصلي
                updated_material.created_date = self.materials[name].created_date
                self.materials[name] = updated_material
                self.save_materials()
                return True
            return False
        except Exception as e:
            print(f"خطأ في تحديث المادة: {e}")
            return False
    
    def delete_material(self, name: str) -> bool:
        """حذف مادة"""
        try:
            if name in self.materials:
                del self.materials[name]
                self.save_materials()
                return True
            return False
        except Exception as e:
            print(f"خطأ في حذف المادة: {e}")
            return False
    
    def get_material(self, name: str) -> Optional[Material]:
        """الحصول على مادة بالاسم"""
        return self.materials.get(name)
    
    def get_all_materials(self) -> Dict[str, Material]:
        """الحصول على جميع المواد"""
        return self.materials.copy()
    
    def get_materials_by_category(self, category: str) -> Dict[str, Material]:
        """الحصول على المواد حسب الفئة"""
        return {name: material for name, material in self.materials.items() 
                if material.category == category}
    
    def get_material_names(self) -> List[str]:
        """الحصول على أسماء جميع المواد"""
        return list(self.materials.keys())
    
    def get_categories(self) -> List[str]:
        """الحصول على جميع فئات المواد"""
        categories = set(material.category for material in self.materials.values())
        return sorted(list(categories))
    
    def calculate_weight(self, material_name: str, volume_m3: float) -> float:
        """حساب الوزن بناءً على المادة والحجم"""
        material = self.get_material(material_name)
        if material:
            return volume_m3 * material.density
        return 0.0
    
    def calculate_cost(self, material_name: str, volume_m3: float = 0, area_m2: float = 0) -> float:
        """حساب التكلفة بناءً على المادة والحجم أو المساحة"""
        material = self.get_material(material_name)
        if material:
            cost = 0.0
            if volume_m3 > 0:
                cost += volume_m3 * material.cost_per_m3
            if area_m2 > 0:
                cost += area_m2 * material.cost_per_m2
            return cost
        return 0.0
    
    def suggest_material(self, dimensions: Dict[str, float]) -> str:
        """اقتراح مادة مناسبة بناءً على الأبعاد"""
        thickness = dimensions.get('thickness', 0)
        length = dimensions.get('length', 0)
        width = dimensions.get('width', 0)
        
        # قواعد بسيطة لاقتراح المادة
        if thickness < 5:
            return "خشب رقائقي"
        elif thickness < 20:
            if max(length, width) > 500:
                return "MDF"
            else:
                return "خشب"
        else:
            return "خشب"
    
    def get_available_thicknesses(self, material_name: str) -> List[float]:
        """الحصول على السماكات المتاحة لمادة معينة"""
        material = self.get_material(material_name)
        if material and material.thickness_options:
            return material.thickness_options
        return []
    
    def get_sheet_sizes(self, material_name: str) -> List[Dict[str, float]]:
        """الحصول على أحجام الألواح المتاحة لمادة معينة"""
        material = self.get_material(material_name)
        if material and material.sheet_sizes:
            return material.sheet_sizes
        return []
    
    def _add_default_materials(self):
        """إضافة المواد الافتراضية"""
        default_materials = [
            Material(
                name="خشب",
                density=600,  # كغ/م³
                cost_per_m3=500,  # ريال/م³
                cost_per_m2=25,   # ريال/م²
                color="#8B4513",
                category="خشب",
                description="خشب طبيعي عالي الجودة",
                thickness_options=[6, 9, 12, 15, 18, 20, 25, 30],
                sheet_sizes=[
                    {"length": 2440, "width": 1220, "thickness": 18},
                    {"length": 2500, "width": 1250, "thickness": 18},
                    {"length": 3000, "width": 1500, "thickness": 20}
                ]
            ),
            Material(
                name="MDF",
                density=750,
                cost_per_m3=300,
                cost_per_m2=18,
                color="#DEB887",
                category="خشب مصنع",
                description="ألواح ليفية متوسطة الكثافة",
                thickness_options=[3, 6, 9, 12, 15, 18, 25, 30],
                sheet_sizes=[
                    {"length": 2440, "width": 1220, "thickness": 15},
                    {"length": 2500, "width": 1250, "thickness": 18},
                    {"length": 2800, "width": 2070, "thickness": 18}
                ]
            ),
            Material(
                name="خشب رقائقي",
                density=650,
                cost_per_m3=400,
                cost_per_m2=20,
                color="#D2691E",
                category="خشب مصنع",
                description="خشب رقائقي متعدد الطبقات",
                thickness_options=[3, 4, 6, 9, 12, 15, 18, 21],
                sheet_sizes=[
                    {"length": 2440, "width": 1220, "thickness": 6},
                    {"length": 2440, "width": 1220, "thickness": 12},
                    {"length": 2440, "width": 1220, "thickness": 18}
                ]
            ),
            Material(
                name="ألمنيوم",
                density=2700,
                cost_per_m3=2000,
                cost_per_m2=80,
                color="#C0C0C0",
                category="معدن",
                description="ألمنيوم خفيف الوزن ومقاوم للصدأ",
                thickness_options=[1, 2, 3, 4, 5, 6, 8, 10],
                sheet_sizes=[
                    {"length": 2000, "width": 1000, "thickness": 2},
                    {"length": 2500, "width": 1250, "thickness": 3},
                    {"length": 3000, "width": 1500, "thickness": 4}
                ]
            ),
            Material(
                name="بلاستيك",
                density=900,
                cost_per_m3=800,
                cost_per_m2=35,
                color="#FFFFFF",
                category="بلاستيك",
                description="بلاستيك عالي الجودة",
                thickness_options=[2, 3, 4, 5, 6, 8, 10, 12],
                sheet_sizes=[
                    {"length": 2000, "width": 1000, "thickness": 3},
                    {"length": 2440, "width": 1220, "thickness": 5},
                    {"length": 3000, "width": 1500, "thickness": 6}
                ]
            )
        ]
        
        for material in default_materials:
            self.add_material(material)


def test_material_manager():
    """اختبار مدير المواد"""
    # إنشاء مدير المواد
    manager = MaterialManager("test_materials.json")
    
    # عرض جميع المواد
    print("المواد المتاحة:")
    for name, material in manager.get_all_materials().items():
        print(f"- {name}: كثافة {material.density} كغ/م³، تكلفة {material.cost_per_m3} ريال/م³")
    
    # اختبار حساب الوزن والتكلفة
    volume = 0.01296  # 1200×600×18 مم = 0.01296 م³
    weight = manager.calculate_weight("خشب", volume)
    cost = manager.calculate_cost("خشب", volume)
    
    print(f"\nلوح خشب 1200×600×18:")
    print(f"الوزن: {weight:.2f} كغ")
    print(f"التكلفة: {cost:.2f} ريال")
    
    # اختبار اقتراح المادة
    dimensions = {"length": 1200, "width": 600, "thickness": 18}
    suggested = manager.suggest_material(dimensions)
    print(f"\nالمادة المقترحة للأبعاد {dimensions}: {suggested}")
    
    # تنظيف ملف الاختبار
    if os.path.exists("test_materials.json"):
        os.remove("test_materials.json")


if __name__ == "__main__":
    test_material_manager()
