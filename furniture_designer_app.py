#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CutList Desktop App - Professional Furniture Designer Edition
تطبيق جدول القطع الاحترافي لمصممي الأثاث
"""

import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import json

# PyQt5 imports
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QTabWidget, QTableWidget, QTableWidgetItem, QTreeWidget,
    QTreeWidgetItem, QListWidget, QListWidgetItem, QPushButton, QLabel,
    QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox,
    QGroupBox, QFormLayout, QGridLayout, QScrollArea, QFrame, QSlider,
    QProgressBar, QStatusBar, QMenuBar, QToolBar, QAction, QFileDialog,
    QMessageBox, QDialog, QDialogButtonBox, QCalendarWidget, QDateEdit,
    QTimeEdit, QColorDialog, QFontDialog, QInputDialog, QWizard, QWizardPage
)
from PyQt5.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QSize, QRect, QPoint, QPropertyAnimation,
    QEasingCurve, QParallelAnimationGroup, QSequentialAnimationGroup
)
from PyQt5.QtGui import (
    QFont, QIcon, QPixmap, QPainter, QPen, QBrush, QColor, QLinearGradient,
    QRadialGradient, QConicalGradient, QPalette, QMovie, QDrag, QCursor
)

# إضافة مسارات الاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), 'models'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'cutlist'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'export'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'materials'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'furniture'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'clients'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'optimization'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'visualization'))

# محاولة استيراد المكتبات المتقدمة
try:
    from models.model_reader import ModelReader
    from cutlist.cutlist_generator import CutListGenerator
    from export.export_excel import export_to_excel
    from export.export_pdf import export_to_pdf
    from materials.material_manager import MaterialManager
    from project_manager import ProjectManager, CutListProject

    # الوحدات الجديدة
    from furniture.furniture_templates import FurnitureTemplateManager
    from furniture.furniture_designer import FurnitureDesigner
    from clients.client_manager import ClientManager
    from optimization.cutting_optimizer import CuttingOptimizer
    from visualization.model_viewer import ModelViewer3D
    from pricing.market_pricing import MarketPricingEngine
    from inventory.inventory_manager import InventoryManager
    from scheduling.production_scheduler import ProductionScheduler
    from cnc.cnc_exporter import CNCExporter
    from labels.label_printer import LabelPrinter

    PROFESSIONAL_FEATURES = True
except ImportError as e:
    print(f"تحذير: بعض الميزات الاحترافية غير متاحة: {e}")
    PROFESSIONAL_FEATURES = False


class FurnitureDesignerMainWindow(QMainWindow):
    """النافذة الرئيسية لتطبيق مصمم الأثاث الاحترافي"""

    def __init__(self):
        super().__init__()

        # البيانات الأساسية
        self.current_project = None
        self.current_client = None
        self.components_data = []
        self.selected_template = None

        # المدراء والمحركات
        self.init_managers()

        # إعداد الواجهة
        self.init_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_animations()

        # تحميل البيانات الأولية
        self.load_initial_data()

    def init_managers(self):
        """تهيئة جميع المدراء والمحركات"""
        if PROFESSIONAL_FEATURES:
            self.material_manager = MaterialManager()
            self.project_manager = ProjectManager()
            self.template_manager = FurnitureTemplateManager()
            self.client_manager = ClientManager()
            self.cutting_optimizer = CuttingOptimizer()
            self.pricing_engine = MarketPricingEngine()
            self.inventory_manager = InventoryManager()
            self.production_scheduler = ProductionScheduler()
            self.cnc_exporter = CNCExporter()
            self.label_printer = LabelPrinter()

    def init_ui(self):
        """إعداد واجهة المستخدم الاحترافية"""
        self.setWindowTitle("CutList Pro - تطبيق مصمم الأثاث الاحترافي")
        self.setGeometry(50, 50, 1600, 1000)
        self.setMinimumSize(1200, 800)

        # تطبيق نمط احترافي
        self.apply_professional_style()

        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي مع مقسمات متعددة
        main_layout = QHBoxLayout(central_widget)

        # المقسم الرئيسي
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)

        # اللوحة اليسرى - قوالب وأدوات
        left_panel = self.create_left_panel()
        main_splitter.addWidget(left_panel)

        # المنطقة الوسطى - العمل الرئيسي
        center_panel = self.create_center_panel()
        main_splitter.addWidget(center_panel)

        # اللوحة اليمنى - خصائص ومعلومات
        right_panel = self.create_right_panel()
        main_splitter.addWidget(right_panel)

        # تحديد النسب
        main_splitter.setSizes([300, 900, 400])

    def apply_professional_style(self):
        """تطبيق نمط احترافي حديث للتطبيق"""
        style = """
        QMainWindow {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #f8f9fa, stop: 1 #e9ecef);
            color: #212529;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
        }

        QTabWidget::pane {
            border: 2px solid #dee2e6;
            background-color: white;
            border-radius: 8px;
            margin-top: 5px;
        }

        QTabBar::tab {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #ffffff, stop: 1 #f8f9fa);
            border: 2px solid #dee2e6;
            padding: 12px 20px;
            margin-right: 3px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            font-weight: 600;
            color: #495057;
            min-width: 100px;
        }

        QTabBar::tab:selected {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #007bff, stop: 1 #0056b3);
            color: white;
            border-bottom-color: white;
            font-weight: bold;
        }

        QTabBar::tab:hover:!selected {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #e3f2fd, stop: 1 #bbdefb);
            color: #1976d2;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            margin-top: 1ex;
            padding-top: 15px;
            background-color: white;
            color: #495057;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            background-color: white;
            color: #007bff;
            font-size: 14px;
            font-weight: bold;
        }

        QPushButton {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #007bff, stop: 1 #0056b3);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 13px;
            min-height: 20px;
        }

        QPushButton:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #0056b3, stop: 1 #004085);
            transform: translateY(-1px);
        }

        QPushButton:pressed {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #004085, stop: 1 #002752);
            transform: translateY(1px);
        }

        QPushButton:disabled {
            background-color: #6c757d;
            color: #adb5bd;
        }

        /* أزرار ثانوية */
        QPushButton[class="secondary"] {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #6c757d, stop: 1 #495057);
        }

        QPushButton[class="success"] {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #28a745, stop: 1 #1e7e34);
        }

        QPushButton[class="warning"] {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #ffc107, stop: 1 #e0a800);
            color: #212529;
        }

        QPushButton[class="danger"] {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #dc3545, stop: 1 #bd2130);
        }

        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            selection-background-color: #007bff;
        }

        QHeaderView::section {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #f8f9fa, stop: 1 #e9ecef);
            padding: 8px;
            border: 1px solid #dee2e6;
            font-weight: bold;
            color: #495057;
        }

        QListWidget {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: white;
            alternate-background-color: #f8f9fa;
            selection-background-color: #007bff;
            padding: 5px;
        }

        QListWidget::item {
            padding: 8px;
            border-radius: 4px;
            margin: 2px;
        }

        QListWidget::item:hover {
            background-color: #e3f2fd;
        }

        QListWidget::item:selected {
            background-color: #007bff;
            color: white;
        }

        QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px;
            background-color: white;
            font-size: 13px;
        }

        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border-color: #007bff;
            outline: none;
        }

        QComboBox::drop-down {
            border: none;
            width: 30px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #495057;
            margin-right: 10px;
        }

        QScrollBar:vertical {
            background-color: #f8f9fa;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background-color: #ced4da;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #adb5bd;
        }

        QStatusBar {
            background-color: #343a40;
            color: white;
            border-top: 1px solid #495057;
            padding: 5px;
        }

        QMenuBar {
            background-color: #343a40;
            color: white;
            border-bottom: 1px solid #495057;
        }

        QMenuBar::item {
            padding: 8px 16px;
            background-color: transparent;
        }

        QMenuBar::item:selected {
            background-color: #495057;
        }

        QMenu {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
        }

        QMenu::item {
            padding: 8px 20px;
        }

        QMenu::item:selected {
            background-color: #007bff;
            color: white;
        }

        QToolBar {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #ffffff, stop: 1 #f8f9fa);
            border-bottom: 1px solid #dee2e6;
            spacing: 5px;
            padding: 5px;
        }

        QSplitter::handle {
            background-color: #dee2e6;
            width: 3px;
            height: 3px;
        }

        QSplitter::handle:hover {
            background-color: #007bff;
        }
        """
        self.setStyleSheet(style)

    def create_left_panel(self):
        """إنشاء اللوحة اليسرى - قوالب وأدوات"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # تبويبات اللوحة اليسرى
        left_tabs = QTabWidget()

        # تبويب قوالب الأثاث
        templates_tab = self.create_templates_tab()
        left_tabs.addTab(templates_tab, "🪑 قوالب الأثاث")

        # تبويب المواد
        materials_tab = self.create_materials_tab()
        left_tabs.addTab(materials_tab, "🏗️ المواد")

        # تبويب الأدوات
        tools_tab = self.create_tools_tab()
        left_tabs.addTab(tools_tab, "🔧 الأدوات")

        layout.addWidget(left_tabs)
        return panel

    def create_center_panel(self):
        """إنشاء المنطقة الوسطى - العمل الرئيسي"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # شريط الأدوات السريعة
        quick_toolbar = self.create_quick_toolbar()
        layout.addWidget(quick_toolbar)

        # تبويبات العمل الرئيسية
        main_tabs = QTabWidget()

        # تبويب التصميم والمعاينة
        design_tab = self.create_design_tab()
        main_tabs.addTab(design_tab, "🎨 التصميم والمعاينة")

        # تبويب جدول القطع
        cutlist_tab = self.create_cutlist_tab()
        main_tabs.addTab(cutlist_tab, "📋 جدول القطع")

        # تبويب تحسين القطع
        optimization_tab = self.create_optimization_tab()
        main_tabs.addTab(optimization_tab, "⚡ تحسين القطع")

        # تبويب الإنتاج
        production_tab = self.create_production_tab()
        main_tabs.addTab(production_tab, "🏭 الإنتاج")

        layout.addWidget(main_tabs)
        return panel

    def create_right_panel(self):
        """إنشاء اللوحة اليمنى - خصائص ومعلومات"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # تبويبات اللوحة اليمنى
        right_tabs = QTabWidget()

        # تبويب معلومات المشروع
        project_tab = self.create_project_info_tab()
        right_tabs.addTab(project_tab, "📁 المشروع")

        # تبويب العميل
        client_tab = self.create_client_tab()
        right_tabs.addTab(client_tab, "👤 العميل")

        # تبويب التكلفة والتسعير
        pricing_tab = self.create_pricing_tab()
        right_tabs.addTab(pricing_tab, "💰 التسعير")

        # تبويب الإحصائيات
        stats_tab = self.create_statistics_tab()
        right_tabs.addTab(stats_tab, "📊 الإحصائيات")

        layout.addWidget(right_tabs)
        return panel

    def create_templates_tab(self):
        """إنشاء تبويب قوالب الأثاث"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # شريط البحث
        search_layout = QHBoxLayout()
        search_edit = QLineEdit()
        search_edit.setPlaceholderText("🔍 البحث في القوالب...")
        search_btn = QPushButton("بحث")
        search_layout.addWidget(search_edit)
        search_layout.addWidget(search_btn)
        layout.addLayout(search_layout)

        # فلتر الفئات
        category_combo = QComboBox()
        category_combo.addItem("جميع الفئات")
        if PROFESSIONAL_FEATURES:
            categories = self.template_manager.get_all_categories()
            category_combo.addItems(categories)
        layout.addWidget(category_combo)

        # قائمة القوالب
        self.templates_list = QListWidget()
        self.templates_list.itemDoubleClicked.connect(self.on_template_selected)
        layout.addWidget(self.templates_list)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        use_template_btn = QPushButton("استخدام القالب")
        use_template_btn.clicked.connect(self.use_selected_template)
        customize_btn = QPushButton("تخصيص")
        customize_btn.clicked.connect(self.customize_template)

        buttons_layout.addWidget(use_template_btn)
        buttons_layout.addWidget(customize_btn)
        layout.addLayout(buttons_layout)

        # تحديث قائمة القوالب
        self.update_templates_list()

        return widget

    def create_materials_tab(self):
        """إنشاء تبويب المواد"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # قائمة المواد
        self.materials_list = QListWidget()
        layout.addWidget(self.materials_list)

        # معلومات المادة المختارة
        material_info = QGroupBox("معلومات المادة")
        info_layout = QFormLayout(material_info)

        self.material_name_label = QLabel("-")
        self.material_price_label = QLabel("-")
        self.material_density_label = QLabel("-")
        self.material_supplier_label = QLabel("-")

        info_layout.addRow("الاسم:", self.material_name_label)
        info_layout.addRow("السعر:", self.material_price_label)
        info_layout.addRow("الكثافة:", self.material_density_label)
        info_layout.addRow("المورد:", self.material_supplier_label)

        layout.addWidget(material_info)

        # أزرار إدارة المواد
        materials_buttons = QHBoxLayout()
        add_material_btn = QPushButton("إضافة مادة")
        edit_material_btn = QPushButton("تعديل")
        update_prices_btn = QPushButton("تحديث الأسعار")

        materials_buttons.addWidget(add_material_btn)
        materials_buttons.addWidget(edit_material_btn)
        materials_buttons.addWidget(update_prices_btn)
        layout.addLayout(materials_buttons)

        # تحديث قائمة المواد
        self.update_materials_list()

        return widget

    def create_tools_tab(self):
        """إنشاء تبويب الأدوات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # أدوات سريعة
        quick_tools = QGroupBox("أدوات سريعة")
        tools_layout = QVBoxLayout(quick_tools)

        calc_btn = QPushButton("🧮 حاسبة المواد")
        calc_btn.clicked.connect(self.open_material_calculator)

        measure_btn = QPushButton("📏 أداة القياس")
        measure_btn.clicked.connect(self.open_measurement_tool)

        cut_optimizer_btn = QPushButton("⚡ محسن القطع")
        cut_optimizer_btn.clicked.connect(self.open_cutting_optimizer)

        price_checker_btn = QPushButton("💰 فاحص الأسعار")
        price_checker_btn.clicked.connect(self.open_price_checker)

        tools_layout.addWidget(calc_btn)
        tools_layout.addWidget(measure_btn)
        tools_layout.addWidget(cut_optimizer_btn)
        tools_layout.addWidget(price_checker_btn)

        layout.addWidget(quick_tools)

        # إعدادات سريعة
        settings_group = QGroupBox("إعدادات سريعة")
        settings_layout = QFormLayout(settings_group)

        self.kerf_width_spin = QDoubleSpinBox()
        self.kerf_width_spin.setRange(1.0, 10.0)
        self.kerf_width_spin.setValue(3.0)
        self.kerf_width_spin.setSuffix(" مم")

        self.edge_margin_spin = QDoubleSpinBox()
        self.edge_margin_spin.setRange(0.0, 20.0)
        self.edge_margin_spin.setValue(5.0)
        self.edge_margin_spin.setSuffix(" مم")

        settings_layout.addRow("عرض القطع:", self.kerf_width_spin)
        settings_layout.addRow("هامش الحافة:", self.edge_margin_spin)

        layout.addWidget(settings_group)
        layout.addStretch()

        return widget

    def create_quick_toolbar(self):
        """إنشاء شريط الأدوات السريعة"""
        toolbar = QWidget()
        layout = QHBoxLayout(toolbar)

        # أزرار سريعة
        new_btn = QPushButton("🆕 جديد")
        new_btn.clicked.connect(self.new_project)

        open_btn = QPushButton("📂 فتح")
        open_btn.clicked.connect(self.open_project)

        save_btn = QPushButton("💾 حفظ")
        save_btn.clicked.connect(self.save_project)

        import_btn = QPushButton("📥 استيراد 3D")
        import_btn.clicked.connect(self.import_3d_model)

        layout.addWidget(new_btn)
        layout.addWidget(open_btn)
        layout.addWidget(save_btn)
        layout.addWidget(import_btn)
        layout.addStretch()

        # مؤشر التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        return toolbar

    def create_design_tab(self):
        """إنشاء تبويب التصميم والمعاينة"""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        # المقسم للتصميم والمعاينة
        design_splitter = QSplitter(Qt.Horizontal)

        # منطقة التصميم
        design_area = QWidget()
        design_layout = QVBoxLayout(design_area)

        # شريط أدوات التصميم
        design_toolbar = QWidget()
        toolbar_layout = QHBoxLayout(design_toolbar)

        add_component_btn = QPushButton("➕ إضافة مكون")
        add_component_btn.clicked.connect(self.add_component_manually)

        duplicate_btn = QPushButton("📋 نسخ")
        duplicate_btn.clicked.connect(self.duplicate_component)

        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.clicked.connect(self.delete_component)

        toolbar_layout.addWidget(add_component_btn)
        toolbar_layout.addWidget(duplicate_btn)
        toolbar_layout.addWidget(delete_btn)
        toolbar_layout.addStretch()

        design_layout.addWidget(design_toolbar)

        # قائمة المكونات
        self.components_tree = QTreeWidget()
        self.components_tree.setHeaderLabels(["المكون", "الأبعاد", "المادة", "الكمية"])
        self.components_tree.itemSelectionChanged.connect(self.on_component_selected)
        design_layout.addWidget(self.components_tree)

        # منطقة المعاينة ثلاثية الأبعاد
        preview_area = QWidget()
        preview_layout = QVBoxLayout(preview_area)

        preview_label = QLabel("معاينة ثلاثية الأبعاد")
        preview_label.setAlignment(Qt.AlignCenter)
        preview_label.setStyleSheet("background-color: #f0f0f0; border: 2px dashed #ccc; padding: 20px;")
        preview_label.setMinimumHeight(400)

        preview_layout.addWidget(preview_label)

        # أدوات المعاينة
        preview_controls = QWidget()
        controls_layout = QHBoxLayout(preview_controls)

        zoom_in_btn = QPushButton("🔍+")
        zoom_out_btn = QPushButton("🔍-")
        rotate_btn = QPushButton("🔄")
        reset_view_btn = QPushButton("🏠")

        controls_layout.addWidget(zoom_in_btn)
        controls_layout.addWidget(zoom_out_btn)
        controls_layout.addWidget(rotate_btn)
        controls_layout.addWidget(reset_view_btn)
        controls_layout.addStretch()

        preview_layout.addWidget(preview_controls)

        # إضافة للمقسم
        design_splitter.addWidget(design_area)
        design_splitter.addWidget(preview_area)
        design_splitter.setSizes([400, 500])

        layout.addWidget(design_splitter)
        return widget

    # إضافة الوظائف المفقودة
    def on_template_selected(self, item):
        """عند اختيار قالب"""
        if item:
            template_name = item.text()
            print(f"تم اختيار القالب: {template_name}")

    def use_selected_template(self):
        """استخدام القالب المختار"""
        current_item = self.templates_list.currentItem()
        if current_item:
            template_name = current_item.text().split(' (')[0]  # إزالة اسم الفئة
            print(f"استخدام القالب: {template_name}")

            if PROFESSIONAL_FEATURES:
                try:
                    template = self.template_manager.get_template(template_name)
                    if template:
                        # مسح المكونات الحالية
                        self.components_data.clear()
                        self.components_tree.clear()

                        # إضافة مكونات القالب
                        for component in template.components:
                            component_data = {
                                'name': component.name,
                                'dimensions': {
                                    'length': component.length,
                                    'width': component.width,
                                    'thickness': component.thickness
                                },
                                'material': component.material,
                                'quantity': component.quantity,
                                'edge_banding': component.edge_banding,
                                'notes': component.notes
                            }
                            self.components_data.append(component_data)

                            # إضافة للشجرة
                            item = QTreeWidgetItem(self.components_tree)
                            item.setText(0, component.name)
                            item.setText(1, f"{component.length}×{component.width}×{component.thickness}")
                            item.setText(2, component.material)
                            item.setText(3, str(component.quantity))

                        # تحديث الإحصائيات
                        self.update_project_statistics()
                        self.status_bar.showMessage(f"تم تحميل القالب: {template_name} ({len(template.components)} مكون)")

                        # تحديث التسعير
                        self.calculate_pricing()

                except Exception as e:
                    print(f"خطأ في تحميل القالب: {e}")
                    self.status_bar.showMessage("خطأ في تحميل القالب")
            else:
                # محاكاة تحميل القالب
                self.simulate_template_loading(template_name)

    def customize_template(self):
        """تخصيص القالب"""
        current_item = self.templates_list.currentItem()
        if current_item:
            template_name = current_item.text()
            print(f"تخصيص القالب: {template_name}")

    def update_templates_list(self):
        """تحديث قائمة القوالب"""
        self.templates_list.clear()
        if PROFESSIONAL_FEATURES:
            try:
                categories = self.template_manager.get_all_categories()
                for category in categories:
                    templates = self.template_manager.get_templates_by_category(category)
                    for template in templates:
                        self.templates_list.addItem(f"{template.name} ({category})")
            except:
                pass
        else:
            # قوالب افتراضية
            default_templates = [
                "مكتب مكتبي بسيط",
                "خزانة ملابس بابين",
                "كرسي خشبي بسيط"
            ]
            for template in default_templates:
                self.templates_list.addItem(template)

    def update_materials_list(self):
        """تحديث قائمة المواد"""
        self.materials_list.clear()
        if PROFESSIONAL_FEATURES:
            try:
                for material_name in self.material_manager.materials.keys():
                    self.materials_list.addItem(material_name)
            except:
                pass
        else:
            # مواد افتراضية
            default_materials = ["خشب", "MDF", "خشب رقائقي", "ألمنيوم", "بلاستيك"]
            for material in default_materials:
                self.materials_list.addItem(material)

    def setup_cutlist_table(self):
        """إعداد جدول القطع"""
        self.cutlist_table.setColumnCount(7)
        self.cutlist_table.setHorizontalHeaderLabels([
            "المكون", "الطول", "العرض", "السماكة", "المادة", "الكمية", "الملاحظات"
        ])

        # تعديل عرض الأعمدة
        header = self.cutlist_table.horizontalHeader()
        header.setStretchLastSection(True)

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة ملف
        file_menu = menubar.addMenu('ملف')

        new_action = file_menu.addAction('جديد')
        new_action.triggered.connect(self.new_project)

        open_action = file_menu.addAction('فتح')
        open_action.triggered.connect(self.open_project)

        save_action = file_menu.addAction('حفظ')
        save_action.triggered.connect(self.save_project)

        file_menu.addSeparator()

        exit_action = file_menu.addAction('خروج')
        exit_action.triggered.connect(self.close)

        # قائمة أدوات
        tools_menu = menubar.addMenu('أدوات')

        calc_action = tools_menu.addAction('حاسبة المواد')
        calc_action.triggered.connect(self.open_material_calculator)

        optimizer_action = tools_menu.addAction('محسن القطع')
        optimizer_action.triggered.connect(self.open_cutting_optimizer)

        # قائمة مساعدة
        help_menu = menubar.addMenu('مساعدة')

        about_action = help_menu.addAction('حول')
        about_action.triggered.connect(self.show_about)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar('الأدوات الرئيسية')

        # أزرار سريعة
        new_action = toolbar.addAction('🆕 جديد')
        new_action.triggered.connect(self.new_project)

        open_action = toolbar.addAction('📂 فتح')
        open_action.triggered.connect(self.open_project)

        save_action = toolbar.addAction('💾 حفظ')
        save_action.triggered.connect(self.save_project)

        toolbar.addSeparator()

        import_action = toolbar.addAction('📥 استيراد 3D')
        import_action.triggered.connect(self.import_3d_model)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("جاهز")

    def setup_animations(self):
        """إعداد الرسوم المتحركة"""
        # يمكن إضافة رسوم متحركة هنا لاحقاً
        pass

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        # تحديث القوائم
        self.update_templates_list()
        self.update_materials_list()

        # رسالة ترحيب
        self.status_bar.showMessage("مرحباً بك في تطبيق مصمم الأثاث الاحترافي")

    # وظائف الأزرار والأحداث
    def new_project(self):
        """مشروع جديد"""
        print("إنشاء مشروع جديد")
        self.status_bar.showMessage("تم إنشاء مشروع جديد")

    def open_project(self):
        """فتح مشروع"""
        print("فتح مشروع")
        self.status_bar.showMessage("فتح مشروع...")

    def save_project(self):
        """حفظ مشروع"""
        print("حفظ مشروع")
        self.status_bar.showMessage("تم حفظ المشروع")

    def import_3d_model(self):
        """استيراد نموذج ثلاثي الأبعاد"""
        print("استيراد نموذج ثلاثي الأبعاد")
        self.status_bar.showMessage("استيراد نموذج...")

    def add_component_manually(self):
        """إضافة مكون يدوياً"""
        print("إضافة مكون جديد")

    def duplicate_component(self):
        """نسخ مكون"""
        print("نسخ مكون")

    def delete_component(self):
        """حذف مكون"""
        print("حذف مكون")

    def on_component_selected(self):
        """عند اختيار مكون"""
        print("تم اختيار مكون")

    def generate_cutlist(self):
        """توليد جدول القطع"""
        print("توليد جدول القطع")
        self.status_bar.showMessage("تم توليد جدول القطع")

    def export_cutlist_excel(self):
        """تصدير جدول القطع إلى Excel"""
        print("تصدير إلى Excel")
        self.status_bar.showMessage("تم التصدير إلى Excel")

    def export_cutlist_pdf(self):
        """تصدير جدول القطع إلى PDF"""
        print("تصدير إلى PDF")
        self.status_bar.showMessage("تم التصدير إلى PDF")

    def optimize_cutting_plan(self):
        """تحسين خطة القطع"""
        print("تحسين خطة القطع")
        self.status_bar.showMessage("تم تحسين خطة القطع")

    def preview_cutting_layout(self):
        """معاينة تخطيط القطع"""
        print("معاينة تخطيط القطع")

    def save_cutting_plan(self):
        """حفظ خطة القطع"""
        print("حفظ خطة القطع")

    def export_cnc_files(self):
        """تصدير ملفات CNC"""
        print("تصدير ملفات CNC")
        self.status_bar.showMessage("تم تصدير ملفات CNC")

    def print_component_labels(self):
        """طباعة ملصقات المكونات"""
        print("طباعة ملصقات المكونات")

    def export_assembly_instructions(self):
        """تصدير تعليمات التجميع"""
        print("تصدير تعليمات التجميع")

    def open_material_calculator(self):
        """فتح حاسبة المواد"""
        print("فتح حاسبة المواد")

    def open_measurement_tool(self):
        """فتح أداة القياس"""
        print("فتح أداة القياس")

    def open_cutting_optimizer(self):
        """فتح محسن القطع"""
        print("فتح محسن القطع")

    def open_price_checker(self):
        """فتح فاحص الأسعار"""
        print("فتح فاحص الأسعار")

    def show_about(self):
        """عرض معلومات حول التطبيق"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.about(self, "حول التطبيق",
                         "CutList Pro - Furniture Designer Edition\n"
                         "تطبيق مصمم الأثاث الاحترافي\n\n"
                         "الإصدار 3.0\n"
                         "تطوير: فريق التطوير الاحترافي")

    # إضافة وظائف التبويبات المفقودة
    def create_project_info_tab(self):
        """إنشاء تبويب معلومات المشروع"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # معلومات المشروع
        project_group = QGroupBox("معلومات المشروع")
        project_layout = QFormLayout(project_group)

        self.project_name_edit = QLineEdit()
        self.project_desc_edit = QTextEdit()
        self.project_desc_edit.setMaximumHeight(100)

        project_layout.addRow("اسم المشروع:", self.project_name_edit)
        project_layout.addRow("الوصف:", self.project_desc_edit)

        layout.addWidget(project_group)
        layout.addStretch()

        return widget

    def create_client_tab(self):
        """إنشاء تبويب العميل"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # معلومات العميل
        client_group = QGroupBox("معلومات العميل")
        client_layout = QFormLayout(client_group)

        self.client_name_edit = QLineEdit()
        self.client_phone_edit = QLineEdit()
        self.client_email_edit = QLineEdit()

        client_layout.addRow("اسم العميل:", self.client_name_edit)
        client_layout.addRow("الهاتف:", self.client_phone_edit)
        client_layout.addRow("البريد الإلكتروني:", self.client_email_edit)

        layout.addWidget(client_group)
        layout.addStretch()

        return widget

    def create_pricing_tab(self):
        """إنشاء تبويب التسعير"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # تفاصيل التسعير
        pricing_group = QGroupBox("تفاصيل التسعير")
        pricing_layout = QFormLayout(pricing_group)

        self.materials_cost_label = QLabel("0.00 ريال")
        self.labor_cost_label = QLabel("0.00 ريال")
        self.total_cost_label = QLabel("0.00 ريال")

        pricing_layout.addRow("تكلفة المواد:", self.materials_cost_label)
        pricing_layout.addRow("تكلفة العمالة:", self.labor_cost_label)
        pricing_layout.addRow("التكلفة الإجمالية:", self.total_cost_label)

        layout.addWidget(pricing_group)
        layout.addStretch()

        return widget

    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إحصائيات المشروع
        stats_group = QGroupBox("إحصائيات المشروع")
        stats_layout = QFormLayout(stats_group)

        self.components_count_label = QLabel("0")
        self.total_volume_label = QLabel("0.00 م³")
        self.efficiency_label = QLabel("0%")

        stats_layout.addRow("عدد المكونات:", self.components_count_label)
        stats_layout.addRow("الحجم الإجمالي:", self.total_volume_label)
        stats_layout.addRow("كفاءة القطع:", self.efficiency_label)

        layout.addWidget(stats_group)
        layout.addStretch()

        return widget

    def create_cutlist_tab(self):
        """إنشاء تبويب جدول القطع"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # شريط أدوات جدول القطع
        cutlist_toolbar = QWidget()
        toolbar_layout = QHBoxLayout(cutlist_toolbar)

        generate_btn = QPushButton("🔄 توليد جدول القطع")
        generate_btn.clicked.connect(self.generate_cutlist)

        group_similar_cb = QCheckBox("تجميع المتشابه")
        group_similar_cb.setChecked(True)

        export_excel_btn = QPushButton("📊 تصدير Excel")
        export_excel_btn.clicked.connect(self.export_cutlist_excel)

        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.clicked.connect(self.export_cutlist_pdf)

        toolbar_layout.addWidget(generate_btn)
        toolbar_layout.addWidget(group_similar_cb)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(export_excel_btn)
        toolbar_layout.addWidget(export_pdf_btn)

        layout.addWidget(cutlist_toolbar)

        # جدول القطع
        self.cutlist_table = QTableWidget()
        self.setup_cutlist_table()
        layout.addWidget(self.cutlist_table)

        # ملخص سريع
        summary_group = QGroupBox("ملخص سريع")
        summary_layout = QFormLayout(summary_group)

        self.total_components_label = QLabel("0")
        self.total_pieces_label = QLabel("0")
        self.total_cost_label = QLabel("0.00 ريال")
        self.total_time_label = QLabel("0 ساعة")

        summary_layout.addRow("أنواع المكونات:", self.total_components_label)
        summary_layout.addRow("إجمالي القطع:", self.total_pieces_label)
        summary_layout.addRow("التكلفة المقدرة:", self.total_cost_label)
        summary_layout.addRow("وقت الإنتاج:", self.total_time_label)

        layout.addWidget(summary_group)

        return widget

    def create_optimization_tab(self):
        """إنشاء تبويب تحسين القطع"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إعدادات التحسين
        settings_group = QGroupBox("إعدادات التحسين")
        settings_layout = QFormLayout(settings_group)

        self.optimization_method_combo = QComboBox()
        self.optimization_method_combo.addItems([
            "تقليل الهدر", "تقليل عدد الألواح", "تقليل التكلفة", "توازن"
        ])

        self.allow_rotation_cb = QCheckBox("السماح بالدوران")
        self.allow_rotation_cb.setChecked(True)

        self.grain_direction_cb = QCheckBox("مراعاة اتجاه الألياف")
        self.grain_direction_cb.setChecked(False)

        settings_layout.addRow("طريقة التحسين:", self.optimization_method_combo)
        settings_layout.addRow("", self.allow_rotation_cb)
        settings_layout.addRow("", self.grain_direction_cb)

        layout.addWidget(settings_group)

        # أزرار التحسين
        optimization_buttons = QHBoxLayout()

        optimize_btn = QPushButton("⚡ تحسين خطة القطع")
        optimize_btn.clicked.connect(self.optimize_cutting_plan)

        preview_btn = QPushButton("👁️ معاينة التخطيط")
        preview_btn.clicked.connect(self.preview_cutting_layout)

        save_plan_btn = QPushButton("💾 حفظ الخطة")
        save_plan_btn.clicked.connect(self.save_cutting_plan)

        optimization_buttons.addWidget(optimize_btn)
        optimization_buttons.addWidget(preview_btn)
        optimization_buttons.addWidget(save_plan_btn)

        layout.addLayout(optimization_buttons)

        # نتائج التحسين
        results_group = QGroupBox("نتائج التحسين")
        results_layout = QVBoxLayout(results_group)

        # جدول الألواح المطلوبة
        self.sheets_table = QTableWidget()
        self.sheets_table.setColumnCount(6)
        self.sheets_table.setHorizontalHeaderLabels([
            "اللوح", "الأبعاد", "المادة", "الكمية", "الكفاءة", "التكلفة"
        ])
        results_layout.addWidget(self.sheets_table)

        # إحصائيات التحسين
        stats_layout = QHBoxLayout()

        self.efficiency_label = QLabel("الكفاءة: -")
        self.waste_label = QLabel("الهدر: -")
        self.savings_label = QLabel("التوفير: -")

        stats_layout.addWidget(self.efficiency_label)
        stats_layout.addWidget(self.waste_label)
        stats_layout.addWidget(self.savings_label)

        results_layout.addLayout(stats_layout)
        layout.addWidget(results_group)

        return widget

    def create_production_tab(self):
        """إنشاء تبويب الإنتاج"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # جدولة الإنتاج
        schedule_group = QGroupBox("جدولة الإنتاج")
        schedule_layout = QFormLayout(schedule_group)

        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(datetime.now().date())

        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["عادي", "عاجل", "طارئ"])

        self.workers_spin = QSpinBox()
        self.workers_spin.setRange(1, 10)
        self.workers_spin.setValue(2)

        schedule_layout.addRow("تاريخ البدء:", self.start_date_edit)
        schedule_layout.addRow("الأولوية:", self.priority_combo)
        schedule_layout.addRow("عدد العمال:", self.workers_spin)

        layout.addWidget(schedule_group)

        # خطوات الإنتاج
        steps_group = QGroupBox("خطوات الإنتاج")
        steps_layout = QVBoxLayout(steps_group)

        self.production_steps_list = QListWidget()
        steps_layout.addWidget(self.production_steps_list)

        # أزرار إدارة الخطوات
        steps_buttons = QHBoxLayout()

        add_step_btn = QPushButton("➕ إضافة خطوة")
        edit_step_btn = QPushButton("✏️ تعديل")
        delete_step_btn = QPushButton("🗑️ حذف")

        steps_buttons.addWidget(add_step_btn)
        steps_buttons.addWidget(edit_step_btn)
        steps_buttons.addWidget(delete_step_btn)

        steps_layout.addLayout(steps_buttons)
        layout.addWidget(steps_group)

        # تصدير تعليمات الإنتاج
        export_group = QGroupBox("تصدير تعليمات الإنتاج")
        export_layout = QHBoxLayout(export_group)

        export_cnc_btn = QPushButton("🤖 تصدير CNC")
        export_cnc_btn.clicked.connect(self.export_cnc_files)

        print_labels_btn = QPushButton("🏷️ طباعة الملصقات")
        print_labels_btn.clicked.connect(self.print_component_labels)

        export_instructions_btn = QPushButton("📋 تعليمات التجميع")
        export_instructions_btn.clicked.connect(self.export_assembly_instructions)

        export_layout.addWidget(export_cnc_btn)
        export_layout.addWidget(print_labels_btn)
        export_layout.addWidget(export_instructions_btn)

        layout.addWidget(export_group)
        layout.addStretch()

        return widget

    # وظائف مساعدة جديدة
    def simulate_template_loading(self, template_name: str):
        """محاكاة تحميل القالب للوضع البسيط"""
        # مسح المكونات الحالية
        self.components_data.clear()
        self.components_tree.clear()

        # إضافة مكونات افتراضية حسب نوع القالب
        if "مكتب" in template_name:
            components = [
                {"name": "سطح المكتب", "length": 1200, "width": 600, "thickness": 25, "material": "خشب", "quantity": 1},
                {"name": "الساق الأيسر", "length": 720, "width": 500, "thickness": 25, "material": "خشب", "quantity": 1},
                {"name": "الساق الأيمن", "length": 720, "width": 500, "thickness": 25, "material": "خشب", "quantity": 1},
                {"name": "الرف السفلي", "length": 1150, "width": 450, "thickness": 18, "material": "خشب", "quantity": 1},
            ]
        elif "خزانة" in template_name:
            components = [
                {"name": "الجانب الأيسر", "length": 2000, "width": 600, "thickness": 18, "material": "خشب", "quantity": 1},
                {"name": "الجانب الأيمن", "length": 2000, "width": 600, "thickness": 18, "material": "خشب", "quantity": 1},
                {"name": "السقف", "length": 1200, "width": 600, "thickness": 18, "material": "خشب", "quantity": 1},
                {"name": "القاعدة", "length": 1200, "width": 600, "thickness": 18, "material": "خشب", "quantity": 1},
                {"name": "الرف الأوسط", "length": 1164, "width": 580, "thickness": 18, "material": "خشب", "quantity": 2},
                {"name": "الباب الأيسر", "length": 600, "width": 1982, "thickness": 18, "material": "خشب", "quantity": 1},
                {"name": "الباب الأيمن", "length": 600, "width": 1982, "thickness": 18, "material": "خشب", "quantity": 1},
            ]
        elif "كرسي" in template_name:
            components = [
                {"name": "المقعد", "length": 450, "width": 450, "thickness": 25, "material": "خشب", "quantity": 1},
                {"name": "الظهر", "length": 450, "width": 400, "thickness": 25, "material": "خشب", "quantity": 1},
                {"name": "الساق الأمامية اليسرى", "length": 450, "width": 50, "thickness": 50, "material": "خشب", "quantity": 1},
                {"name": "الساق الأمامية اليمنى", "length": 450, "width": 50, "thickness": 50, "material": "خشب", "quantity": 1},
            ]
        else:
            components = [
                {"name": "مكون أساسي", "length": 500, "width": 300, "thickness": 18, "material": "خشب", "quantity": 1}
            ]

        # إضافة المكونات
        for comp in components:
            self.components_data.append({
                'name': comp['name'],
                'dimensions': {
                    'length': comp['length'],
                    'width': comp['width'],
                    'thickness': comp['thickness']
                },
                'material': comp['material'],
                'quantity': comp['quantity'],
                'edge_banding': [],
                'notes': ""
            })

            # إضافة للشجرة
            item = QTreeWidgetItem(self.components_tree)
            item.setText(0, comp['name'])
            item.setText(1, f"{comp['length']}×{comp['width']}×{comp['thickness']}")
            item.setText(2, comp['material'])
            item.setText(3, str(comp['quantity']))

        # تحديث الإحصائيات
        self.update_project_statistics()
        self.status_bar.showMessage(f"تم تحميل القالب: {template_name} ({len(components)} مكون)")

        # تحديث التسعير
        self.calculate_pricing()

    def update_project_statistics(self):
        """تحديث إحصائيات المشروع"""
        total_components = len(self.components_data)
        total_pieces = sum(comp.get('quantity', 1) for comp in self.components_data)

        # حساب الحجم الإجمالي
        total_volume = 0.0
        for comp in self.components_data:
            dims = comp.get('dimensions', {})
            volume = (dims.get('length', 0) * dims.get('width', 0) * dims.get('thickness', 0)) / 1000000000  # م³
            total_volume += volume * comp.get('quantity', 1)

        # تحديث التسميات
        self.total_components_label.setText(str(total_components))
        self.total_pieces_label.setText(str(total_pieces))
        self.components_count_label.setText(str(total_components))
        self.total_volume_label.setText(f"{total_volume:.3f} م³")

    def calculate_pricing(self):
        """حساب التسعير"""
        if not self.components_data:
            return

        try:
            if PROFESSIONAL_FEATURES:
                # استخدام محرك التسعير الاحترافي
                from pricing.market_pricing import ComplexityLevel

                pricing = self.pricing_engine.generate_project_pricing(
                    self.components_data,
                    "CURRENT_PROJECT",
                    ComplexityLevel.MEDIUM,
                    profit_margin=0.25
                )

                materials_cost = pricing.materials_cost
                labor_cost = pricing.labor_cost
                total_cost = pricing.final_price

            else:
                # حساب بسيط
                materials_cost = 0.0
                for comp in self.components_data:
                    dims = comp.get('dimensions', {})
                    volume = (dims.get('length', 0) * dims.get('width', 0) * dims.get('thickness', 0)) / 1000000000  # م³
                    cost_per_m3 = 500  # سعر افتراضي
                    materials_cost += volume * cost_per_m3 * comp.get('quantity', 1)

                labor_cost = materials_cost * 0.4  # 40% من تكلفة المواد
                total_cost = (materials_cost + labor_cost) * 1.25  # هامش ربح 25%

            # تحديث التسميات
            self.materials_cost_label.setText(f"{materials_cost:.2f} ريال")
            self.labor_cost_label.setText(f"{labor_cost:.2f} ريال")
            self.total_cost_label.setText(f"{total_cost:.2f} ريال")

            # تقدير الوقت
            estimated_hours = len(self.components_data) * 2  # ساعتان لكل مكون
            self.total_time_label.setText(f"{estimated_hours} ساعة")

        except Exception as e:
            print(f"خطأ في حساب التسعير: {e}")

    def generate_cutlist(self):
        """توليد جدول القطع"""
        if not self.components_data:
            self.status_bar.showMessage("لا توجد مكونات لتوليد جدول القطع")
            return

        # مسح الجدول الحالي
        self.cutlist_table.setRowCount(0)

        # إضافة المكونات للجدول
        for i, comp in enumerate(self.components_data):
            self.cutlist_table.insertRow(i)

            # ملء البيانات
            self.cutlist_table.setItem(i, 0, QTableWidgetItem(comp.get('name', '')))

            dims = comp.get('dimensions', {})
            self.cutlist_table.setItem(i, 1, QTableWidgetItem(str(dims.get('length', 0))))
            self.cutlist_table.setItem(i, 2, QTableWidgetItem(str(dims.get('width', 0))))
            self.cutlist_table.setItem(i, 3, QTableWidgetItem(str(dims.get('thickness', 0))))
            self.cutlist_table.setItem(i, 4, QTableWidgetItem(comp.get('material', '')))
            self.cutlist_table.setItem(i, 5, QTableWidgetItem(str(comp.get('quantity', 1))))
            self.cutlist_table.setItem(i, 6, QTableWidgetItem(comp.get('notes', '')))

        # تحديث الإحصائيات
        self.update_project_statistics()
        self.status_bar.showMessage(f"تم توليد جدول القطع - {len(self.components_data)} مكون")

    def add_component_manually(self):
        """إضافة مكون يدوياً"""
        from PyQt5.QtWidgets import QDialog, QFormLayout, QDialogButtonBox

        dialog = QDialog(self)
        dialog.setWindowTitle("إضافة مكون جديد")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QFormLayout(dialog)

        # حقول الإدخال
        name_edit = QLineEdit()
        name_edit.setText("مكون جديد")

        length_spin = QDoubleSpinBox()
        length_spin.setRange(1, 10000)
        length_spin.setValue(500)
        length_spin.setSuffix(" مم")

        width_spin = QDoubleSpinBox()
        width_spin.setRange(1, 10000)
        width_spin.setValue(300)
        width_spin.setSuffix(" مم")

        thickness_spin = QDoubleSpinBox()
        thickness_spin.setRange(1, 200)
        thickness_spin.setValue(18)
        thickness_spin.setSuffix(" مم")

        material_combo = QComboBox()
        material_combo.addItems(["خشب", "MDF", "خشب رقائقي", "ألمنيوم", "بلاستيك"])

        quantity_spin = QSpinBox()
        quantity_spin.setRange(1, 100)
        quantity_spin.setValue(1)

        notes_edit = QLineEdit()

        # إضافة الحقول
        layout.addRow("اسم المكون:", name_edit)
        layout.addRow("الطول:", length_spin)
        layout.addRow("العرض:", width_spin)
        layout.addRow("السماكة:", thickness_spin)
        layout.addRow("المادة:", material_combo)
        layout.addRow("الكمية:", quantity_spin)
        layout.addRow("ملاحظات:", notes_edit)

        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addRow(buttons)

        # عرض الحوار
        if dialog.exec_() == QDialog.Accepted:
            # إضافة المكون الجديد
            new_component = {
                'name': name_edit.text(),
                'dimensions': {
                    'length': length_spin.value(),
                    'width': width_spin.value(),
                    'thickness': thickness_spin.value()
                },
                'material': material_combo.currentText(),
                'quantity': quantity_spin.value(),
                'edge_banding': [],
                'notes': notes_edit.text()
            }

            self.components_data.append(new_component)

            # إضافة للشجرة
            item = QTreeWidgetItem(self.components_tree)
            item.setText(0, new_component['name'])
            item.setText(1, f"{new_component['dimensions']['length']}×{new_component['dimensions']['width']}×{new_component['dimensions']['thickness']}")
            item.setText(2, new_component['material'])
            item.setText(3, str(new_component['quantity']))

            # تحديث الإحصائيات والتسعير
            self.update_project_statistics()
            self.calculate_pricing()
            self.status_bar.showMessage(f"تم إضافة المكون: {new_component['name']}")

    def optimize_cutting_plan(self):
        """تحسين خطة القطع"""
        if not self.components_data:
            self.status_bar.showMessage("لا توجد مكونات لتحسين القطع")
            return

        try:
            if PROFESSIONAL_FEATURES:
                from optimization.cutting_optimizer import CuttingOptimizer, CutPiece, Sheet

                optimizer = CuttingOptimizer()

                # تحويل المكونات إلى قطع
                pieces = []
                for i, comp in enumerate(self.components_data):
                    dims = comp.get('dimensions', {})
                    piece = CutPiece(
                        id=f"P{i}",
                        name=comp.get('name', f'قطعة {i}'),
                        length=dims.get('length', 0),
                        width=dims.get('width', 0),
                        thickness=dims.get('thickness', 0),
                        material=comp.get('material', 'خشب'),
                        quantity=comp.get('quantity', 1)
                    )
                    pieces.append(piece)

                # ألواح افتراضية
                sheets = [
                    Sheet("S1", 2440, 1220, 18, "خشب", 150, 10),
                    Sheet("S2", 2440, 1220, 25, "خشب", 180, 5),
                ]

                # تحسين القطع
                layouts = optimizer.optimize_cutting_plan(pieces, sheets)

                # عرض النتائج
                if layouts:
                    efficiency = sum(layout.efficiency for layout in layouts) / len(layouts)
                    self.efficiency_label.setText(f"الكفاءة: {efficiency:.1%}")

                    total_waste = sum(layout.waste_area for layout in layouts)
                    self.waste_label.setText(f"الهدر: {total_waste:.0f} مم²")

                    # تحديث جدول الألواح
                    self.sheets_table.setRowCount(len(layouts))
                    for i, layout in enumerate(layouts):
                        self.sheets_table.setItem(i, 0, QTableWidgetItem(f"لوح {i+1}"))
                        self.sheets_table.setItem(i, 1, QTableWidgetItem(f"{layout.sheet.length}×{layout.sheet.width}"))
                        self.sheets_table.setItem(i, 2, QTableWidgetItem(layout.sheet.material))
                        self.sheets_table.setItem(i, 3, QTableWidgetItem("1"))
                        self.sheets_table.setItem(i, 4, QTableWidgetItem(f"{layout.efficiency:.1%}"))
                        self.sheets_table.setItem(i, 5, QTableWidgetItem(f"{layout.sheet.cost:.2f}"))

                    self.status_bar.showMessage(f"تم تحسين القطع - {len(layouts)} لوح، كفاءة {efficiency:.1%}")
                else:
                    self.status_bar.showMessage("لا يمكن تحسين القطع - تحقق من الأبعاد")
            else:
                # محاكاة التحسين
                efficiency = 0.75  # كفاءة افتراضية
                self.efficiency_label.setText(f"الكفاءة: {efficiency:.1%}")
                self.waste_label.setText("الهدر: 500 مم²")
                self.savings_label.setText("التوفير: 15%")
                self.status_bar.showMessage("تم تحسين القطع (محاكاة)")

        except Exception as e:
            print(f"خطأ في تحسين القطع: {e}")
            self.status_bar.showMessage("خطأ في تحسين القطع")


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    import sys

    app = QApplication(sys.argv)

    # تعيين معلومات التطبيق
    app.setApplicationName("CutList Pro - Furniture Designer")
    app.setApplicationVersion("3.0")
    app.setOrganizationName("Professional Furniture Design Solutions")

    # إنشاء النافذة الرئيسية
    window = FurnitureDesignerMainWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
