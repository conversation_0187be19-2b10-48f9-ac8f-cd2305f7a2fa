#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CutList Desktop App - Professional Furniture Designer Edition
تطبيق جدول القطع الاحترافي لمصممي الأثاث
"""

import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import json

# PyQt5 imports
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QTabWidget, QTableWidget, QTableWidgetItem, QTreeWidget,
    QTreeWidgetItem, QListWidget, QListWidgetItem, QPushButton, QLabel,
    QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox,
    QGroupBox, QFormLayout, QGridLayout, QScrollArea, QFrame, QSlider,
    QProgressBar, QStatusBar, QMenuBar, QToolBar, QAction, QFileDialog,
    QMessageBox, QDialog, QDialogButtonBox, QCalendarWidget, QDateEdit,
    QTimeEdit, QColorDialog, QFontDialog, QInputDialog, QWizard, QWizardPage
)
from PyQt5.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QSize, QRect, QPoint, QPropertyAnimation,
    QEasingCurve, QParallelAnimationGroup, QSequentialAnimationGroup
)
from PyQt5.QtGui import (
    QFont, QIcon, QPixmap, QPainter, QPen, QBrush, QColor, QLinearGradient,
    QRadialGradient, QConicalGradient, QPalette, QMovie, QDrag, QCursor
)

# إضافة مسارات الاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), 'models'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'cutlist'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'export'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'materials'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'furniture'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'clients'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'optimization'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'visualization'))

# محاولة استيراد المكتبات المتقدمة
try:
    from models.model_reader import ModelReader
    from cutlist.cutlist_generator import CutListGenerator
    from export.export_excel import export_to_excel
    from export.export_pdf import export_to_pdf
    from materials.material_manager import MaterialManager
    from project_manager import ProjectManager, CutListProject

    # الوحدات الجديدة
    from furniture.furniture_templates import FurnitureTemplateManager
    from furniture.furniture_designer import FurnitureDesigner
    from clients.client_manager import ClientManager
    from optimization.cutting_optimizer import CuttingOptimizer
    from visualization.model_viewer import ModelViewer3D
    from pricing.market_pricing import MarketPricingEngine
    from inventory.inventory_manager import InventoryManager
    from scheduling.production_scheduler import ProductionScheduler
    from cnc.cnc_exporter import CNCExporter
    from labels.label_printer import LabelPrinter

    PROFESSIONAL_FEATURES = True
except ImportError as e:
    print(f"تحذير: بعض الميزات الاحترافية غير متاحة: {e}")
    PROFESSIONAL_FEATURES = False


class FurnitureDesignerMainWindow(QMainWindow):
    """النافذة الرئيسية لتطبيق مصمم الأثاث الاحترافي"""

    def __init__(self):
        super().__init__()

        # البيانات الأساسية
        self.current_project = None
        self.current_client = None
        self.components_data = []
        self.selected_template = None

        # المدراء والمحركات
        self.init_managers()

        # إعداد الواجهة
        self.init_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        self.setup_animations()

        # تحميل البيانات الأولية
        self.load_initial_data()

    def init_managers(self):
        """تهيئة جميع المدراء والمحركات"""
        if PROFESSIONAL_FEATURES:
            self.material_manager = MaterialManager()
            self.project_manager = ProjectManager()
            self.template_manager = FurnitureTemplateManager()
            self.client_manager = ClientManager()
            self.cutting_optimizer = CuttingOptimizer()
            self.pricing_engine = MarketPricingEngine()
            self.inventory_manager = InventoryManager()
            self.production_scheduler = ProductionScheduler()
            self.cnc_exporter = CNCExporter()
            self.label_printer = LabelPrinter()

    def init_ui(self):
        """إعداد واجهة المستخدم الاحترافية"""
        self.setWindowTitle("CutList Pro - تطبيق مصمم الأثاث الاحترافي")
        self.setGeometry(50, 50, 1600, 1000)
        self.setMinimumSize(1200, 800)

        # تطبيق نمط احترافي
        self.apply_professional_style()

        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي مع مقسمات متعددة
        main_layout = QHBoxLayout(central_widget)

        # المقسم الرئيسي
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)

        # اللوحة اليسرى - قوالب وأدوات
        left_panel = self.create_left_panel()
        main_splitter.addWidget(left_panel)

        # المنطقة الوسطى - العمل الرئيسي
        center_panel = self.create_center_panel()
        main_splitter.addWidget(center_panel)

        # اللوحة اليمنى - خصائص ومعلومات
        right_panel = self.create_right_panel()
        main_splitter.addWidget(right_panel)

        # تحديد النسب
        main_splitter.setSizes([300, 900, 400])

    def apply_professional_style(self):
        """تطبيق نمط احترافي للتطبيق"""
        style = """
        QMainWindow {
            background-color: #f5f5f5;
            color: #333333;
        }

        QTabWidget::pane {
            border: 1px solid #c0c0c0;
            background-color: white;
        }

        QTabBar::tab {
            background-color: #e1e1e1;
            border: 1px solid #c0c0c0;
            padding: 8px 16px;
            margin-right: 2px;
        }

        QTabBar::tab:selected {
            background-color: white;
            border-bottom-color: white;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }

        QPushButton {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #45a049;
        }

        QPushButton:pressed {
            background-color: #3d8b40;
        }

        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }

        QTableWidget {
            gridline-color: #d0d0d0;
            background-color: white;
            alternate-background-color: #f9f9f9;
        }

        QHeaderView::section {
            background-color: #e1e1e1;
            padding: 4px;
            border: 1px solid #c0c0c0;
            font-weight: bold;
        }
        """
        self.setStyleSheet(style)

    def create_left_panel(self):
        """إنشاء اللوحة اليسرى - قوالب وأدوات"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # تبويبات اللوحة اليسرى
        left_tabs = QTabWidget()

        # تبويب قوالب الأثاث
        templates_tab = self.create_templates_tab()
        left_tabs.addTab(templates_tab, "🪑 قوالب الأثاث")

        # تبويب المواد
        materials_tab = self.create_materials_tab()
        left_tabs.addTab(materials_tab, "🏗️ المواد")

        # تبويب الأدوات
        tools_tab = self.create_tools_tab()
        left_tabs.addTab(tools_tab, "🔧 الأدوات")

        layout.addWidget(left_tabs)
        return panel

    def create_center_panel(self):
        """إنشاء المنطقة الوسطى - العمل الرئيسي"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # شريط الأدوات السريعة
        quick_toolbar = self.create_quick_toolbar()
        layout.addWidget(quick_toolbar)

        # تبويبات العمل الرئيسية
        main_tabs = QTabWidget()

        # تبويب التصميم والمعاينة
        design_tab = self.create_design_tab()
        main_tabs.addTab(design_tab, "🎨 التصميم والمعاينة")

        # تبويب جدول القطع
        cutlist_tab = self.create_cutlist_tab()
        main_tabs.addTab(cutlist_tab, "📋 جدول القطع")

        # تبويب تحسين القطع
        optimization_tab = self.create_optimization_tab()
        main_tabs.addTab(optimization_tab, "⚡ تحسين القطع")

        # تبويب الإنتاج
        production_tab = self.create_production_tab()
        main_tabs.addTab(production_tab, "🏭 الإنتاج")

        layout.addWidget(main_tabs)
        return panel

    def create_right_panel(self):
        """إنشاء اللوحة اليمنى - خصائص ومعلومات"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # تبويبات اللوحة اليمنى
        right_tabs = QTabWidget()

        # تبويب معلومات المشروع
        project_tab = self.create_project_info_tab()
        right_tabs.addTab(project_tab, "📁 المشروع")

        # تبويب العميل
        client_tab = self.create_client_tab()
        right_tabs.addTab(client_tab, "👤 العميل")

        # تبويب التكلفة والتسعير
        pricing_tab = self.create_pricing_tab()
        right_tabs.addTab(pricing_tab, "💰 التسعير")

        # تبويب الإحصائيات
        stats_tab = self.create_statistics_tab()
        right_tabs.addTab(stats_tab, "📊 الإحصائيات")

        layout.addWidget(right_tabs)
        return panel

    def create_templates_tab(self):
        """إنشاء تبويب قوالب الأثاث"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # شريط البحث
        search_layout = QHBoxLayout()
        search_edit = QLineEdit()
        search_edit.setPlaceholderText("🔍 البحث في القوالب...")
        search_btn = QPushButton("بحث")
        search_layout.addWidget(search_edit)
        search_layout.addWidget(search_btn)
        layout.addLayout(search_layout)

        # فلتر الفئات
        category_combo = QComboBox()
        category_combo.addItem("جميع الفئات")
        if PROFESSIONAL_FEATURES:
            categories = self.template_manager.get_all_categories()
            category_combo.addItems(categories)
        layout.addWidget(category_combo)

        # قائمة القوالب
        self.templates_list = QListWidget()
        self.templates_list.itemDoubleClicked.connect(self.on_template_selected)
        layout.addWidget(self.templates_list)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        use_template_btn = QPushButton("استخدام القالب")
        use_template_btn.clicked.connect(self.use_selected_template)
        customize_btn = QPushButton("تخصيص")
        customize_btn.clicked.connect(self.customize_template)

        buttons_layout.addWidget(use_template_btn)
        buttons_layout.addWidget(customize_btn)
        layout.addLayout(buttons_layout)

        # تحديث قائمة القوالب
        self.update_templates_list()

        return widget

    def create_materials_tab(self):
        """إنشاء تبويب المواد"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # قائمة المواد
        self.materials_list = QListWidget()
        layout.addWidget(self.materials_list)

        # معلومات المادة المختارة
        material_info = QGroupBox("معلومات المادة")
        info_layout = QFormLayout(material_info)

        self.material_name_label = QLabel("-")
        self.material_price_label = QLabel("-")
        self.material_density_label = QLabel("-")
        self.material_supplier_label = QLabel("-")

        info_layout.addRow("الاسم:", self.material_name_label)
        info_layout.addRow("السعر:", self.material_price_label)
        info_layout.addRow("الكثافة:", self.material_density_label)
        info_layout.addRow("المورد:", self.material_supplier_label)

        layout.addWidget(material_info)

        # أزرار إدارة المواد
        materials_buttons = QHBoxLayout()
        add_material_btn = QPushButton("إضافة مادة")
        edit_material_btn = QPushButton("تعديل")
        update_prices_btn = QPushButton("تحديث الأسعار")

        materials_buttons.addWidget(add_material_btn)
        materials_buttons.addWidget(edit_material_btn)
        materials_buttons.addWidget(update_prices_btn)
        layout.addLayout(materials_buttons)

        # تحديث قائمة المواد
        self.update_materials_list()

        return widget

    def create_tools_tab(self):
        """إنشاء تبويب الأدوات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # أدوات سريعة
        quick_tools = QGroupBox("أدوات سريعة")
        tools_layout = QVBoxLayout(quick_tools)

        calc_btn = QPushButton("🧮 حاسبة المواد")
        calc_btn.clicked.connect(self.open_material_calculator)

        measure_btn = QPushButton("📏 أداة القياس")
        measure_btn.clicked.connect(self.open_measurement_tool)

        cut_optimizer_btn = QPushButton("⚡ محسن القطع")
        cut_optimizer_btn.clicked.connect(self.open_cutting_optimizer)

        price_checker_btn = QPushButton("💰 فاحص الأسعار")
        price_checker_btn.clicked.connect(self.open_price_checker)

        tools_layout.addWidget(calc_btn)
        tools_layout.addWidget(measure_btn)
        tools_layout.addWidget(cut_optimizer_btn)
        tools_layout.addWidget(price_checker_btn)

        layout.addWidget(quick_tools)

        # إعدادات سريعة
        settings_group = QGroupBox("إعدادات سريعة")
        settings_layout = QFormLayout(settings_group)

        self.kerf_width_spin = QDoubleSpinBox()
        self.kerf_width_spin.setRange(1.0, 10.0)
        self.kerf_width_spin.setValue(3.0)
        self.kerf_width_spin.setSuffix(" مم")

        self.edge_margin_spin = QDoubleSpinBox()
        self.edge_margin_spin.setRange(0.0, 20.0)
        self.edge_margin_spin.setValue(5.0)
        self.edge_margin_spin.setSuffix(" مم")

        settings_layout.addRow("عرض القطع:", self.kerf_width_spin)
        settings_layout.addRow("هامش الحافة:", self.edge_margin_spin)

        layout.addWidget(settings_group)
        layout.addStretch()

        return widget

    def create_quick_toolbar(self):
        """إنشاء شريط الأدوات السريعة"""
        toolbar = QWidget()
        layout = QHBoxLayout(toolbar)

        # أزرار سريعة
        new_btn = QPushButton("🆕 جديد")
        new_btn.clicked.connect(self.new_project)

        open_btn = QPushButton("📂 فتح")
        open_btn.clicked.connect(self.open_project)

        save_btn = QPushButton("💾 حفظ")
        save_btn.clicked.connect(self.save_project)

        import_btn = QPushButton("📥 استيراد 3D")
        import_btn.clicked.connect(self.import_3d_model)

        layout.addWidget(new_btn)
        layout.addWidget(open_btn)
        layout.addWidget(save_btn)
        layout.addWidget(import_btn)
        layout.addStretch()

        # مؤشر التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        return toolbar

    def create_design_tab(self):
        """إنشاء تبويب التصميم والمعاينة"""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        # المقسم للتصميم والمعاينة
        design_splitter = QSplitter(Qt.Horizontal)

        # منطقة التصميم
        design_area = QWidget()
        design_layout = QVBoxLayout(design_area)

        # شريط أدوات التصميم
        design_toolbar = QWidget()
        toolbar_layout = QHBoxLayout(design_toolbar)

        add_component_btn = QPushButton("➕ إضافة مكون")
        add_component_btn.clicked.connect(self.add_component_manually)

        duplicate_btn = QPushButton("📋 نسخ")
        duplicate_btn.clicked.connect(self.duplicate_component)

        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.clicked.connect(self.delete_component)

        toolbar_layout.addWidget(add_component_btn)
        toolbar_layout.addWidget(duplicate_btn)
        toolbar_layout.addWidget(delete_btn)
        toolbar_layout.addStretch()

        design_layout.addWidget(design_toolbar)

        # قائمة المكونات
        self.components_tree = QTreeWidget()
        self.components_tree.setHeaderLabels(["المكون", "الأبعاد", "المادة", "الكمية"])
        self.components_tree.itemSelectionChanged.connect(self.on_component_selected)
        design_layout.addWidget(self.components_tree)

        # منطقة المعاينة ثلاثية الأبعاد
        preview_area = QWidget()
        preview_layout = QVBoxLayout(preview_area)

        preview_label = QLabel("معاينة ثلاثية الأبعاد")
        preview_label.setAlignment(Qt.AlignCenter)
        preview_label.setStyleSheet("background-color: #f0f0f0; border: 2px dashed #ccc; padding: 20px;")
        preview_label.setMinimumHeight(400)

        preview_layout.addWidget(preview_label)

        # أدوات المعاينة
        preview_controls = QWidget()
        controls_layout = QHBoxLayout(preview_controls)

        zoom_in_btn = QPushButton("🔍+")
        zoom_out_btn = QPushButton("🔍-")
        rotate_btn = QPushButton("🔄")
        reset_view_btn = QPushButton("🏠")

        controls_layout.addWidget(zoom_in_btn)
        controls_layout.addWidget(zoom_out_btn)
        controls_layout.addWidget(rotate_btn)
        controls_layout.addWidget(reset_view_btn)
        controls_layout.addStretch()

        preview_layout.addWidget(preview_controls)

        # إضافة للمقسم
        design_splitter.addWidget(design_area)
        design_splitter.addWidget(preview_area)
        design_splitter.setSizes([400, 500])

        layout.addWidget(design_splitter)
        return widget

    def create_cutlist_tab(self):
        """إنشاء تبويب جدول القطع"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # شريط أدوات جدول القطع
        cutlist_toolbar = QWidget()
        toolbar_layout = QHBoxLayout(cutlist_toolbar)

        generate_btn = QPushButton("🔄 توليد جدول القطع")
        generate_btn.clicked.connect(self.generate_cutlist)

        group_similar_cb = QCheckBox("تجميع المتشابه")
        group_similar_cb.setChecked(True)

        export_excel_btn = QPushButton("📊 تصدير Excel")
        export_excel_btn.clicked.connect(self.export_cutlist_excel)

        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.clicked.connect(self.export_cutlist_pdf)

        toolbar_layout.addWidget(generate_btn)
        toolbar_layout.addWidget(group_similar_cb)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(export_excel_btn)
        toolbar_layout.addWidget(export_pdf_btn)

        layout.addWidget(cutlist_toolbar)

        # جدول القطع
        self.cutlist_table = QTableWidget()
        self.setup_cutlist_table()
        layout.addWidget(self.cutlist_table)

        # ملخص سريع
        summary_group = QGroupBox("ملخص سريع")
        summary_layout = QFormLayout(summary_group)

        self.total_components_label = QLabel("0")
        self.total_pieces_label = QLabel("0")
        self.total_cost_label = QLabel("0.00 ريال")
        self.total_time_label = QLabel("0 ساعة")

        summary_layout.addRow("أنواع المكونات:", self.total_components_label)
        summary_layout.addRow("إجمالي القطع:", self.total_pieces_label)
        summary_layout.addRow("التكلفة المقدرة:", self.total_cost_label)
        summary_layout.addRow("وقت الإنتاج:", self.total_time_label)

        layout.addWidget(summary_group)

        return widget

    def create_optimization_tab(self):
        """إنشاء تبويب تحسين القطع"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # إعدادات التحسين
        settings_group = QGroupBox("إعدادات التحسين")
        settings_layout = QFormLayout(settings_group)

        self.optimization_method_combo = QComboBox()
        self.optimization_method_combo.addItems([
            "تقليل الهدر", "تقليل عدد الألواح", "تقليل التكلفة", "توازن"
        ])

        self.allow_rotation_cb = QCheckBox("السماح بالدوران")
        self.allow_rotation_cb.setChecked(True)

        self.grain_direction_cb = QCheckBox("مراعاة اتجاه الألياف")
        self.grain_direction_cb.setChecked(False)

        settings_layout.addRow("طريقة التحسين:", self.optimization_method_combo)
        settings_layout.addRow("", self.allow_rotation_cb)
        settings_layout.addRow("", self.grain_direction_cb)

        layout.addWidget(settings_group)

        # أزرار التحسين
        optimization_buttons = QHBoxLayout()

        optimize_btn = QPushButton("⚡ تحسين خطة القطع")
        optimize_btn.clicked.connect(self.optimize_cutting_plan)

        preview_btn = QPushButton("👁️ معاينة التخطيط")
        preview_btn.clicked.connect(self.preview_cutting_layout)

        save_plan_btn = QPushButton("💾 حفظ الخطة")
        save_plan_btn.clicked.connect(self.save_cutting_plan)

        optimization_buttons.addWidget(optimize_btn)
        optimization_buttons.addWidget(preview_btn)
        optimization_buttons.addWidget(save_plan_btn)

        layout.addLayout(optimization_buttons)

        # نتائج التحسين
        results_group = QGroupBox("نتائج التحسين")
        results_layout = QVBoxLayout(results_group)

        # جدول الألواح المطلوبة
        self.sheets_table = QTableWidget()
        self.sheets_table.setColumnCount(6)
        self.sheets_table.setHorizontalHeaderLabels([
            "اللوح", "الأبعاد", "المادة", "الكمية", "الكفاءة", "التكلفة"
        ])
        results_layout.addWidget(self.sheets_table)

        # إحصائيات التحسين
        stats_layout = QHBoxLayout()

        self.efficiency_label = QLabel("الكفاءة: -")
        self.waste_label = QLabel("الهدر: -")
        self.savings_label = QLabel("التوفير: -")

        stats_layout.addWidget(self.efficiency_label)
        stats_layout.addWidget(self.waste_label)
        stats_layout.addWidget(self.savings_label)

        results_layout.addLayout(stats_layout)
        layout.addWidget(results_group)

        return widget

    def create_production_tab(self):
        """إنشاء تبويب الإنتاج"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # جدولة الإنتاج
        schedule_group = QGroupBox("جدولة الإنتاج")
        schedule_layout = QFormLayout(schedule_group)

        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(datetime.now().date())

        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["عادي", "عاجل", "طارئ"])

        self.workers_spin = QSpinBox()
        self.workers_spin.setRange(1, 10)
        self.workers_spin.setValue(2)

        schedule_layout.addRow("تاريخ البدء:", self.start_date_edit)
        schedule_layout.addRow("الأولوية:", self.priority_combo)
        schedule_layout.addRow("عدد العمال:", self.workers_spin)

        layout.addWidget(schedule_group)

        # خطوات الإنتاج
        steps_group = QGroupBox("خطوات الإنتاج")
        steps_layout = QVBoxLayout(steps_group)

        self.production_steps_list = QListWidget()
        steps_layout.addWidget(self.production_steps_list)

        # أزرار إدارة الخطوات
        steps_buttons = QHBoxLayout()

        add_step_btn = QPushButton("➕ إضافة خطوة")
        edit_step_btn = QPushButton("✏️ تعديل")
        delete_step_btn = QPushButton("🗑️ حذف")

        steps_buttons.addWidget(add_step_btn)
        steps_buttons.addWidget(edit_step_btn)
        steps_buttons.addWidget(delete_step_btn)

        steps_layout.addLayout(steps_buttons)
        layout.addWidget(steps_group)

        # تصدير تعليمات الإنتاج
        export_group = QGroupBox("تصدير تعليمات الإنتاج")
        export_layout = QHBoxLayout(export_group)

        export_cnc_btn = QPushButton("🤖 تصدير CNC")
        export_cnc_btn.clicked.connect(self.export_cnc_files)

        print_labels_btn = QPushButton("🏷️ طباعة الملصقات")
        print_labels_btn.clicked.connect(self.print_component_labels)

        export_instructions_btn = QPushButton("📋 تعليمات التجميع")
        export_instructions_btn.clicked.connect(self.export_assembly_instructions)

        export_layout.addWidget(export_cnc_btn)
        export_layout.addWidget(print_labels_btn)
        export_layout.addWidget(export_instructions_btn)

        layout.addWidget(export_group)
        layout.addStretch()

        return widget
